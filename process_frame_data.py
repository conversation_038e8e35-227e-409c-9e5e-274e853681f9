#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理包含on_process_frame数据的文件
提取x、y、w、h值，计算w/h比例，过滤出与16/9偏差较大的数据
"""

import re
import os

def process_frame_data(file_path):
    """
    处理文件中的on_process_frame数据
    
    Args:
        file_path: 文件路径
    
    Returns:
        tuple: (all_data, filtered_data)
    """
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return [], []
    
    # 16:9的标准比例
    standard_ratio = 16.0 / 9.0
    
    all_data = []
    filtered_data = []
    
    # 正则表达式匹配on_process_frame:后面的4个数字
    pattern = r'on_process_frame:\s*(\d+)\s*,?\s*(\d+)\s*,?\s*(\d+)\s*,?\s*(\d+)'
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                matches = re.findall(pattern, line)
                
                for match in matches:
                    x, y, w, h = map(int, match)
                    
                    # 避免除零错误
                    if h == 0:
                        print(f"警告: 第{line_num}行，h=0，跳过此数据")
                        continue
                    
                    ratio = w / h
                    deviation = abs(ratio - standard_ratio)
                    deviation_percent = (deviation / standard_ratio) * 100
                    
                    data_entry = {
                        'line_num': line_num,
                        'x': x,
                        'y': y,
                        'w': w,
                        'h': h,
                        'ratio': ratio,
                        'deviation': deviation,
                        'deviation_percent': deviation_percent,
                        'original_line': line.strip()
                    }
                    
                    all_data.append(data_entry)
                    
                    # 过滤条件：偏差超过10%的数据
                    if deviation_percent > 10:
                        filtered_data.append(data_entry)
    
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return [], []
    
    return all_data, filtered_data

def print_results(all_data, filtered_data):
    """打印分析结果"""
    print(f"总共找到 {len(all_data)} 条数据")
    print(f"16:9标准比例: {16/9:.4f}")
    print(f"偏差较大的数据 (>10%): {len(filtered_data)} 条")
    print("-" * 80)
    
    if filtered_data:
        print("偏差较大的数据详情:")
        print(f"{'行号':<6} {'x':<6} {'y':<6} {'w':<6} {'h':<6} {'w/h':<8} {'偏差%':<8} {'原始行'}")
        print("-" * 80)
        
        for data in filtered_data:
            print(f"{data['line_num']:<6} {data['x']:<6} {data['y']:<6} {data['w']:<6} {data['h']:<6} "
                  f"{data['ratio']:<8.4f} {data['deviation_percent']:<8.2f} {data['original_line'][:50]}...")
    
    # 统计信息
    if all_data:
        ratios = [d['ratio'] for d in all_data]
        avg_ratio = sum(ratios) / len(ratios)
        min_ratio = min(ratios)
        max_ratio = max(ratios)
        
        print(f"\n统计信息:")
        print(f"平均w/h比例: {avg_ratio:.4f}")
        print(f"最小w/h比例: {min_ratio:.4f}")
        print(f"最大w/h比例: {max_ratio:.4f}")
        print(f"标准16:9比例: {16/9:.4f}")

def main():
    # 尝试多个可能的文件路径
    possible_paths = [
        "D:\\new 2.txt",
        "new 2.txt",
        "D:/new 2.txt",
        "./new 2.txt",
        "D:\\Program\\Project\\project\\czcv_camera_new\\new 2.txt"
    ]

    file_path = None
    for path in possible_paths:
        print(f"尝试路径: {path}")
        if os.path.exists(path):
            file_path = path
            print(f"找到文件: {path}")
            break
        else:
            print(f"文件不存在: {path}")

    if not file_path:
        print("\n所有预设路径都不存在，请手动输入文件路径:")
        print("当前工作目录:", os.getcwd())
        print("请输入完整的文件路径:")
        file_path = input().strip()
        if not file_path or not os.path.exists(file_path):
            print(f"文件 {file_path} 不存在或路径为空")
            return

    print(f"\n开始处理文件: {file_path}")
    all_data, filtered_data = process_frame_data(file_path)
    print_results(all_data, filtered_data)
    
    # 保存结果到文件
    if filtered_data:
        output_file = "filtered_frame_data.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("偏差较大的on_process_frame数据 (与16:9比例偏差>10%)\n")
            f.write("=" * 80 + "\n")
            f.write(f"{'行号':<6} {'x':<6} {'y':<6} {'w':<6} {'h':<6} {'w/h':<8} {'偏差%':<8}\n")
            f.write("-" * 80 + "\n")
            
            for data in filtered_data:
                f.write(f"{data['line_num']:<6} {data['x']:<6} {data['y']:<6} {data['w']:<6} {data['h']:<6} "
                       f"{data['ratio']:<8.4f} {data['deviation_percent']:<8.2f}\n")
                f.write(f"原始行: {data['original_line']}\n\n")
        
        print(f"\n结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
