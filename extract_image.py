import fitz # 安装命令: pip install PyMuPDF

def extract_images_pymupdf(pdf_path, output_folder):
    """
    使用PyMuPDF提取PDF中的原始图片
    
    参数:
        pdf_path: PDF文件路径
        output_folder: 图片输出文件夹
    """
    # 打开PDF文档
    pdf_document = fitz.open(pdf_path)
    
    # 遍历每一页
    for page_num in range(len(pdf_document)):
        page = pdf_document.load_page(page_num)
        # 获取当前页的所有图像（完整信息）
        image_list = page.get_images(full=True)
        
        # 遍历当前页的每个图像
        for img_index, img in enumerate(image_list):
            # 获取图像的交叉引用编号
            xref = img[0]
            # 提取图像数据
            base_image = pdf_document.extract_image(xref)
            image_bytes = base_image["image"]  # 图像二进制数据
            image_ext = base_image["ext"]     # 原始图像格式（如jpg、png）
            
            # 生成图像文件名
            image_filename = f"{output_folder}/page_{page_num+1}_img_{img_index+1}.{image_ext}"
            
            # 保存图像
            with open(image_filename, "wb") as image_file:
                image_file.write(image_bytes)
            print(f"已保存: {image_filename}")
    
    pdf_document.close()
    print("提取完成！")

# 使用示例
extract_images_pymupdf(r"D:/Download/1.pdf", "./extracted_images")