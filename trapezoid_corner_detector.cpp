#include "trapezoid_corner_detector.hpp"
#include <algorithm>
#include <cmath>

TrapezoidCornerDetector::TrapezoidCornerDetector(double minArea, double maxArea, double approxEpsilon, 
                                               double cannyLow, double cannyHigh)
    : minArea_(minArea), maxArea_(maxArea), approxEpsilon_(approxEpsilon),
      cannyLow_(cannyLow), cannyHigh_(cannyHigh) {
}

TrapezoidCornerDetector::~TrapezoidCornerDetector() {
}

bool TrapezoidCornerDetector::detectTrapezoidCorners(const cv::Mat& image) {
    // Clear previous results
    detectedTrapezoids_.clear();
    gridCorners_.isValid = false;
    
    if (image.empty()) {
        return false;
    }
    
    // Preprocess the image
    cv::Mat binary = preprocessImage(image);
    
    // Detect trapezoid contours
    std::vector<std::vector<cv::Point>> contours = detectTrapezoidContours(binary);
 
    // Filter valid trapezoid contours
    std::vector<std::vector<cv::Point>> validContours = filterTrapezoidContours(contours);
    std::cout << validContours.size() << std::endl;
    // Check if we have exactly 4 trapezoids
    if (validContours.size() != 4) {
        return false;
    }
    
    // Convert contours to DetectedTrapezoid objects
    convertContoursToTrapezoids(validContours);
    
    // Arrange trapezoids and extract corner points
    return arrangeTrapezoidsAndExtractCorners();
}

cv::Mat TrapezoidCornerDetector::preprocessImage(const cv::Mat& image) {
    cv::Mat gray, binary;
    
    // Convert to grayscale if needed
    if (image.channels() == 3) {
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    } else {
        gray = image.clone();
    }
    
    // Apply Gaussian blur to reduce noise
    //cv::GaussianBlur(gray, gray, cv::Size(5, 5), 0);
    
    // Apply threshold to get binary image (white trapezoids on dark background)
    cv::threshold(gray, binary, 200, 255, cv::THRESH_BINARY );
    
    // Apply morphological operations to clean up the image
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(3, 3));
    cv::morphologyEx(binary, binary, cv::MORPH_CLOSE, kernel);
    cv::morphologyEx(binary, binary, cv::MORPH_OPEN, kernel);
    
    return binary;
}

std::vector<std::vector<cv::Point>> TrapezoidCornerDetector::detectTrapezoidContours(const cv::Mat& binary) {
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    
    // Find contours
    cv::findContours(binary, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    
    return contours;
}

std::vector<std::vector<cv::Point>> TrapezoidCornerDetector::filterTrapezoidContours(
    const std::vector<std::vector<cv::Point>>& contours) {
    
    std::vector<std::vector<cv::Point>> validContours;
   
    for (const auto& contour : contours) {
        // Check area constraint
        double area = cv::contourArea(contour);
        if (area < minArea_ || area > maxArea_) {
            continue;
        }
        //validContours.push_back(contour);
        // Check if contour approximates to a trapezoid
        if (isTrapezoidContour(contour)) {
            validContours.push_back(contour);
        }
    }
   
    return validContours;
}

bool TrapezoidCornerDetector::isTrapezoidContour(const std::vector<cv::Point>& contour) {
    // Approximate contour to polygon
    std::vector<cv::Point> approx;
    double epsilon = approxEpsilon_ * cv::arcLength(contour, true);
    cv::approxPolyDP(contour, approx, epsilon, true);
    
    // Check if we have 4 vertices (trapezoid/quadrilateral)
    if (approx.size() != 4) {
        return false;
    }
    
    // Check if the contour is convex
    if (!cv::isContourConvex(approx)) {
        return false;
    }
    
    // For trapezoids, we don't require 90-degree angles like squares
    // Just ensure it's a valid quadrilateral with reasonable proportions
    
    // Calculate side lengths
    std::vector<double> sideLengths;
    for (int i = 0; i < 4; i++) {
        cv::Point2f side = approx[(i + 1) % 4] - approx[i];
        double length = cv::norm(side);
        sideLengths.push_back(length);
    }
    
    // Check that no side is too small (avoid degenerate cases)
    const double minSideLength = 10.0;
    for (double length : sideLengths) {
        if (length < minSideLength) {
            return false;
        }
    }
    
    return true;
}

void TrapezoidCornerDetector::convertContoursToTrapezoids(const std::vector<std::vector<cv::Point>>& contours) {
    for (const auto& contour : contours) {
        DetectedTrapezoid trapezoid;
        
        // Approximate contour to get corner points
        std::vector<cv::Point> approx;
        double epsilon = approxEpsilon_ * cv::arcLength(contour, true);
        cv::approxPolyDP(contour, approx, epsilon, true);
        
        // Convert to Point2f and store corners
        for (const auto& point : approx) {
            trapezoid.corners.push_back(cv::Point2f(point.x, point.y));
        }
        
        // Calculate center and area
        trapezoid.center = calculateCenter(contour);
        trapezoid.area = cv::contourArea(contour);
        
        detectedTrapezoids_.push_back(trapezoid);
    }
}

cv::Point2f TrapezoidCornerDetector::calculateCenter(const std::vector<cv::Point>& contour) {
    cv::Moments moments = cv::moments(contour);
    if (moments.m00 != 0) {
        return cv::Point2f(moments.m10 / moments.m00, moments.m01 / moments.m00);
    }
    return cv::Point2f(0, 0);
}

bool TrapezoidCornerDetector::arrangeTrapezoidsAndExtractCorners() {
    if (detectedTrapezoids_.size() != 4) {
        return false;
    }
    
    // Sort trapezoids by position
    std::vector<DetectedTrapezoid> sortedTrapezoids = sortTrapezoidsByPosition(detectedTrapezoids_);
    
    // Extract corner points from each trapezoid
    // Top-left trapezoid: get top-left corner
    gridCorners_.topLeft = findTrapezoidCorner(sortedTrapezoids[0], 0);
    
    // Top-right trapezoid: get top-right corner
    gridCorners_.topRight = findTrapezoidCorner(sortedTrapezoids[1], 1);
    
    // Bottom-left trapezoid: get bottom-left corner
    gridCorners_.bottomLeft = findTrapezoidCorner(sortedTrapezoids[2], 2);
    
    // Bottom-right trapezoid: get bottom-right corner
    gridCorners_.bottomRight = findTrapezoidCorner(sortedTrapezoids[3], 3);
    
    gridCorners_.isValid = true;
    return true;
}

std::vector<DetectedTrapezoid> TrapezoidCornerDetector::sortTrapezoidsByPosition(
    const std::vector<DetectedTrapezoid>& trapezoids) {
    
    std::vector<DetectedTrapezoid> sorted = trapezoids;

    // Sort by center position: first by y-coordinate, then by x-coordinate
    std::sort(sorted.begin(), sorted.end(), [](const DetectedTrapezoid& a, const DetectedTrapezoid& b) {
        if (std::abs(a.center.y - b.center.y) < 50) { // Same row (tolerance for alignment)
            return a.center.x < b.center.x; // Sort by x-coordinate
        }
        return a.center.y < b.center.y; // Sort by y-coordinate
    });

    // Rearrange to get: [top-left, top-right, bottom-left, bottom-right]
    std::vector<DetectedTrapezoid> result(4);

    // Determine which trapezoids are in top row and bottom row
    std::vector<DetectedTrapezoid> topRow, bottomRow;

    // Find the median y-coordinate to separate top and bottom rows
    std::vector<float> yCoords;
    for (const auto& trapezoid : sorted) {
        yCoords.push_back(trapezoid.center.y);
    }
    std::sort(yCoords.begin(), yCoords.end());
    float medianY = (yCoords[1] + yCoords[2]) / 2.0f;

    for (const auto& trapezoid : sorted) {
        if (trapezoid.center.y < medianY) {
            topRow.push_back(trapezoid);
        } else {
            bottomRow.push_back(trapezoid);
        }
    }

    // Sort each row by x-coordinate
    std::sort(topRow.begin(), topRow.end(), [](const DetectedTrapezoid& a, const DetectedTrapezoid& b) {
        return a.center.x < b.center.x;
    });
    std::sort(bottomRow.begin(), bottomRow.end(), [](const DetectedTrapezoid& a, const DetectedTrapezoid& b) {
        return a.center.x < b.center.x;
    });

    // Assign positions: [top-left, top-right, bottom-left, bottom-right]
    if (topRow.size() >= 2 && bottomRow.size() >= 2) {
        result[0] = topRow[0];    // top-left
        result[1] = topRow[1];    // top-right
        result[2] = bottomRow[0]; // bottom-left
        result[3] = bottomRow[1]; // bottom-right
    }

    return result;
}

cv::Point2f TrapezoidCornerDetector::findTrapezoidCorner(const DetectedTrapezoid& trapezoid, int cornerType) {
    const auto& corners = trapezoid.corners;
    
    switch (cornerType) {
        case 0: // Top-left corner
            return *std::min_element(corners.begin(), corners.end(),
                [](const cv::Point2f& a, const cv::Point2f& b) {
                    return (a.x + a.y) < (b.x + b.y);
                });
        
        case 1: // Top-right corner
            return *std::max_element(corners.begin(), corners.end(),
                [](const cv::Point2f& a, const cv::Point2f& b) {
                    return (a.x - a.y) < (b.x - b.y);
                });
        
        case 2: // Bottom-left corner
            return *std::min_element(corners.begin(), corners.end(),
                [](const cv::Point2f& a, const cv::Point2f& b) {
                    return (a.x - a.y) < (b.x - b.y);
                });
        
        case 3: // Bottom-right corner
            return *std::max_element(corners.begin(), corners.end(),
                [](const cv::Point2f& a, const cv::Point2f& b) {
                    return (a.x + a.y) < (b.x + b.y);
                });
        
        default:
            return corners[0]; // Return first corner as fallback
    }
}

cv::Mat TrapezoidCornerDetector::drawResults(const cv::Mat& image, bool drawTrapezoids, bool drawCorners) const {
    cv::Mat result = image.clone();

    if (drawTrapezoids) {
        // Draw detected trapezoids
        for (const auto& trapezoid : detectedTrapezoids_) {
            std::vector<cv::Point> intCorners;
            for (const auto& corner : trapezoid.corners) {
                intCorners.push_back(cv::Point(static_cast<int>(corner.x), static_cast<int>(corner.y)));
            }

            // Draw trapezoid contour
            cv::polylines(result, intCorners, true, cv::Scalar(0, 255, 0), 2);

            // Draw center point
            cv::circle(result, cv::Point(static_cast<int>(trapezoid.center.x), static_cast<int>(trapezoid.center.y)),
                      5, cv::Scalar(255, 0, 0), -1);
        }
    }

    if (drawCorners && gridCorners_.isValid) {
        // Draw the four corner points of the grid
        cv::circle(result, cv::Point(static_cast<int>(gridCorners_.topLeft.x), static_cast<int>(gridCorners_.topLeft.y)),
                  8, cv::Scalar(0, 0, 255), -1);
        cv::circle(result, cv::Point(static_cast<int>(gridCorners_.topRight.x), static_cast<int>(gridCorners_.topRight.y)),
                  8, cv::Scalar(0, 0, 255), -1);
        cv::circle(result, cv::Point(static_cast<int>(gridCorners_.bottomLeft.x), static_cast<int>(gridCorners_.bottomLeft.y)),
                  8, cv::Scalar(0, 0, 255), -1);
        cv::circle(result, cv::Point(static_cast<int>(gridCorners_.bottomRight.x), static_cast<int>(gridCorners_.bottomRight.y)),
                  8, cv::Scalar(0, 0, 255), -1);

        // Draw labels for corner points
        cv::putText(result, "TL", cv::Point(static_cast<int>(gridCorners_.topLeft.x) - 20,
                   static_cast<int>(gridCorners_.topLeft.y) - 10), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255), 1);
        cv::putText(result, "TR", cv::Point(static_cast<int>(gridCorners_.topRight.x) + 10,
                   static_cast<int>(gridCorners_.topRight.y) - 10), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255), 1);
        cv::putText(result, "BL", cv::Point(static_cast<int>(gridCorners_.bottomLeft.x) - 20,
                   static_cast<int>(gridCorners_.bottomLeft.y) + 20), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255), 1);
        cv::putText(result, "BR", cv::Point(static_cast<int>(gridCorners_.bottomRight.x) + 10,
                   static_cast<int>(gridCorners_.bottomRight.y) + 20), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255), 1);
    }

    return result;
}

void TrapezoidCornerDetector::reset() {
    detectedTrapezoids_.clear();
    gridCorners_.isValid = false;
    gridCorners_.topLeft = cv::Point2f(0, 0);
    gridCorners_.topRight = cv::Point2f(0, 0);
    gridCorners_.bottomLeft = cv::Point2f(0, 0);
    gridCorners_.bottomRight = cv::Point2f(0, 0);
}
