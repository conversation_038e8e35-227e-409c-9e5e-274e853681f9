#ifndef CURVE_FIT_HPP
#define CURVE_FIT_HPP

#include <vector>
#include <functional>
#include <cmath>
#include <algorithm>
#include <iostream>

class CurveFit {
public:
    using ModelFunction = std::function<double(double, const std::vector<double>&)>;
    
    struct FitResult {
        std::vector<double> parameters;
        std::vector<std::vector<double>> covariance;
        double rmse;
        bool success;
    };
    
    static FitResult curve_fit(
        ModelFunction model,
        const std::vector<double>& x_data,
        const std::vector<double>& y_data,
        const std::vector<double>& initial_guess,
        int max_iterations = 1000,
        double tolerance = 1e-8
    );

private:
    static std::vector<std::vector<double>> computeJacobian(
        ModelFunction model,
        const std::vector<double>& x_data,
        const std::vector<double>& parameters,
        double h = 1e-8
    );
    
    static std::vector<std::vector<double>> matrixMultiply(
        const std::vector<std::vector<double>>& A,
        const std::vector<std::vector<double>>& B
    );
    
    static std::vector<std::vector<double>> matrixTranspose(
        const std::vector<std::vector<double>>& matrix
    );
    
    static bool matrixInverse(
        std::vector<std::vector<double>>& matrix
    );
};

#endif