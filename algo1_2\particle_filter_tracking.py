import cv2
import numpy as np
from quadrangle_detection import detect_quadrangle, line_length, line_overlap

# -------------------------- 1. 粒子滤波配置（论文参数）--------------------------
NUM_PARTICLES = 80  # 粒子数量（论文用80）
STATE_DIM = 6  # 状态维度：[rx, ry, rz, tx, ty, tz]
NOISE_SCALE = 0.02  # 状态转移噪声（随机游走的e，需调试）
OVERLAP_WEIGHT = 1.0  # 重叠率权重

# -------------------------- 2. 位姿转换工具函数 --------------------------
def pose_to_transform(rx, ry, rz, tx, ty, tz):
    """将欧拉角（rx, ry, rz）和平移（tx, ty, tz）转换为旋转矩阵R和平移向量T"""
    # 欧拉角→旋转矩阵（Z-Y-X顺序，右手坐标系）
    R_x = np.array([
        [1, 0, 0],
        [0, np.cos(rx), -np.sin(rx)],
        [0, np.sin(rx), np.cos(rx)]
    ])
    R_y = np.array([
        [np.cos(ry), 0, np.sin(ry)],
        [0, 1, 0],
        [-np.sin(ry), 0, np.cos(ry)]
    ])
    R_z = np.array([
        [np.cos(rz), -np.sin(rz), 0],
        [np.sin(rz), np.cos(rz), 0],
        [0, 0, 1]
    ])
    R = R_z @ R_y @ R_x  # 旋转矩阵
    T = np.array([tx, ty, tz], dtype=np.float32)  # 平移向量
    return R, T

def project_points_3d_to_2d(points_3d, R, T, camera_matrix, dist_coeffs):
    """将3D点（相机坐标系）投影到2D图像平面"""
    # 3D点：(N, 3)，旋转矩阵R：(3,3)，平移T：(3,)
    points_3d_transformed = (R @ points_3d.T).T + T
    # 投影到图像平面（考虑畸变）
    points_2d, _ = cv2.projectPoints(
        points_3d_transformed, np.zeros(3), np.zeros(3),
        camera_matrix, dist_coeffs
    )
    return points_2d.squeeze().astype(np.float32)

# -------------------------- 3. 粒子滤波类 --------------------------
class ParticleFilter:
    def __init__(self, init_pose, camera_matrix, dist_coeffs, cardboard_size):
        """
        初始化粒子滤波：
            init_pose: 初始位姿 [rx, ry, rz, tx, ty, tz]
            camera_matrix: 相机内参
            dist_coeffs: 畸变系数
            cardboard_size: 纸板尺寸 (width, height)（mm）
        """
        self.camera_matrix = camera_matrix
        self.dist_coeffs = dist_coeffs
        self.cardboard_size = cardboard_size
        
        # 纸板3D顶点（纸板坐标系：原点左上，X右，Y下，Z=0）
        self.cardboard_3d = np.array([
            [0, 0, 0],                  # 左上
            [cardboard_size[0], 0, 0],  # 右上
            [cardboard_size[0], cardboard_size[1], 0],  # 右下
            [0, cardboard_size[1], 0]   # 左下
        ], dtype=np.float32)
        
        # 初始化粒子集：[NUM_PARTICLES, STATE_DIM]，权重均为1/NUM_PARTICLES
        self.particles = np.random.normal(
            loc=init_pose, 
            scale=NOISE_SCALE, 
            size=(NUM_PARTICLES, STATE_DIM)
        )
        self.weights = np.ones(NUM_PARTICLES) / NUM_PARTICLES
    
    def predict(self):
        """状态转移：随机游走模型（论文公式，p(qk|qk-1) = U(qk-1-e, qk-1+e)）"""
        noise = np.random.normal(0, NOISE_SCALE, size=self.particles.shape)
        self.particles += noise
    
    def update(self, observed_lines):
        """观测模型：根据提取的线段更新粒子权重（论文公式19-20）"""
        if observed_lines is None or len(observed_lines) < 4:
            self.weights = np.ones(NUM_PARTICLES) / NUM_PARTICLES
            return
        
        for i in range(NUM_PARTICLES):
            # 1. 获取当前粒子的位姿
            rx, ry, rz, tx, ty, tz = self.particles[i]
            R, T = pose_to_transform(rx, ry, rz, tx, ty, tz)
            
            # 2. 重投影纸板3D顶点到2D图像
            projected_2d = project_points_3d_to_2d(
                self.cardboard_3d, R, T, self.camera_matrix, self.dist_coeffs
            )
            
            # 3. 生成重投影的四边形线段
            projected_lines = []
            for j in range(4):
                x1, y1 = projected_2d[j]
                x2, y2 = projected_2d[(j+1)%4]
                projected_lines.append((x1, y1, x2, y2))
            
            # 4. 计算重叠率之和（论文公式19：p(yk|qk^n) = sum(r1^t * r2^t)）
            total_overlap = 0.0
            for proj_line in projected_lines:
                max_r1r2 = 0.0
                for obs_line in observed_lines:
                    s3 = line_overlap(proj_line, obs_line)
                    s1 = line_length(proj_line)
                    s2 = line_length(obs_line)
                    if s1 == 0 or s2 == 0:
                        continue
                    r1 = s3 / s1
                    r2 = s3 / s2
                    r1r2 = r1 * r2
                    if r1r2 > max_r1r2:
                        max_r1r2 = r1r2
                total_overlap += max_r1r2
            
            # 5. 赋值权重（添加小常数避免权重为0）
            self.weights[i] = total_overlap + 1e-6
        
        # 6. 权重归一化（论文公式20）
        self.weights /= np.sum(self.weights)
    
    def resample(self):
        """重采样：解决粒子退化问题（轮盘赌法）"""
        # 生成累积概率分布
        cum_weights = np.cumsum(self.weights)
        # 生成均匀采样点
        samples = np.random.uniform(0, 1, size=NUM_PARTICLES)
        # 轮盘赌选择粒子
        new_particles = []
        idx = 0
        for s in samples:
            while cum_weights[idx] < s:
                idx += 1
            new_particles.append(self.particles[idx])
            idx = 0
        self.particles = np.array(new_particles)
        # 重置权重
        self.weights = np.ones(NUM_PARTICLES) / NUM_PARTICLES
    
    def get_best_pose(self):
        """获取最优位姿（权重最大的粒子）"""
        best_idx = np.argmax(self.weights)
        return self.particles[best_idx]

# -------------------------- 测试代码 --------------------------
if __name__ == "__main__":
    # 加载相机内参
    import yaml
    with open("./camera_params.yml", "r") as f:
        params = yaml.safe_load(f)
    camera_matrix = np.array(params["camera_matrix"])
    dist_coeffs = np.array(params["dist_coeffs"])
    cardboard_size = (351, 300)  # 论文纸板尺寸（mm）
    
    # 1. 初始化：先检测纸板获取初始位姿
    cap = cv2.VideoCapture(0)
    print("正在检测纸板以初始化跟踪...")
    init_pose = None
    while init_pose is None:
        ret, frame = cap.read()
        if not ret: break
        quad_corners, frame_draw = detect_quadrangle(frame, camera_matrix, dist_coeffs)
        cv2.imshow("Initial Detection (Wait for cardboard)...", frame_draw)
        cv2.waitKey(1)
        if quad_corners is not None:
            # 用solvePnP获取初始位姿（纸板3D→图像2D）
            ret, rvec, tvec = cv2.solvePnP(
                np.array([[0,0,0], [351,0,0], [351,300,0], [0,300,0]], dtype=np.float32),
                quad_corners,
                camera_matrix,
                dist_coeffs
            )
            if ret:
                rx, ry, rz = rvec.flatten()
                tx, ty, tz = tvec.flatten()
                init_pose = [rx, ry, rz, tx, ty, tz]
                print(f"初始位姿：{init_pose}")
    cv2.destroyAllWindows()
    
    if init_pose is None:
        raise ValueError("未检测到纸板，无法初始化跟踪！")
    
    # 2. 初始化粒子滤波
    pf = ParticleFilter(init_pose, camera_matrix, dist_coeffs, cardboard_size)
    
    # 3. 实时跟踪
    print("开始跟踪（Press 'q' to quit）...")
    while True:
        ret, frame = cap.read()
        if not ret: break
        
        # 步骤1：检测线段（用于观测模型）
        frame_undistort = cv2.undistort(frame, camera_matrix, dist_coeffs)
        gray = cv2.cvtColor(frame_undistort, cv2.COLOR_BGR2GRAY)
        blur = cv2.GaussianBlur(gray, (5,5), 0)
        edges = cv2.Canny(blur, 50, 150)
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, 50, minLineLength=50, maxLineGap=20)
        observed_lines = lines[0] if lines is not None else []
        
        # 步骤2：粒子滤波预测→更新→重采样
        pf.predict()
        pf.update(observed_lines)
        pf.resample()
        
        # 步骤3：获取最优位姿并绘制重投影结果
        best_pose = pf.get_best_pose()
        rx, ry, rz, tx, ty, tz = best_pose
        R, T = pose_to_transform(rx, ry, rz, tx, ty, tz)
        projected_2d = project_points_3d_to_2d(
            pf.cardboard_3d, R, T, camera_matrix, dist_coeffs
        )
        
        # 绘制跟踪结果
        frame_track = frame_undistort.copy()
        for i in range(4):
            x1, y1 = projected_2d[i].astype(int)
            x2, y2 = projected_2d[(i+1)%4].astype(int)
            cv2.line(frame_track, (x1, y1), (x2, y2), (255, 0, 0), 2)
            cv2.circle(frame_track, (x1, y1), 5, (0, 255, 255), -1)
        
        cv2.imshow("Cardboard Tracking (Press 'q' to quit)", frame_track)
        if cv2.waitKey(1) == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()