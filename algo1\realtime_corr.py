import cv2, numpy as np, threading, time


# ---------- 参数 ----------
Kc = np.load('Kc.npy')

G = np.load('G.npy')
STAR_HUB = 'starhub.png'      # 原始内容
GREEN_BORDER = 8              # 绿框像素
# ----------------------------

# 1. 生成带绿框的校准图
def make_green_frame(img):
    h, w = img.shape[:2]
    canvas = cv2.copyMakeBorder(img, GREEN_BORDER, GREEN_BORDER,
                                GREEN_BORDER, GREEN_BORDER,
                                cv2.BORDER_CONSTANT, value=(0, 255, 0))
    return canvas, (w, h)       # 返回画布 + 原图尺寸

# 2. 检测四边形（简化版）
def detect_quad(gray):
    edges = cv2.Canny(gray, 50, 150)
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, 80,
                             minLineLength=80, maxLineGap=10)
    if lines is None:
        return None
    # 暴力合并四边形（仅示例，生产请用 LSD + 聚类）
    pts = []
    for l in lines:
        x1, y1, x2, y2 = l[0]
        pts.extend([(x1, y1), (x2, y2)])
    pts = np.array(pts, dtype=np.float32)
    rect = cv2.minAreaRect(pts)
    box = cv2.boxPoints(rect)  # 4×2
    return np.int0(box)

# 3. 三角化 + 内接矩形
def triangulate_and_rect(xc, xp, Kc, G):
    """ xc, xp: 4×2 角点 """
    X = []
    for i in range(4):
        # 线性三角化
        # 构建齐次坐标系下的线性方程组
        # 对于相机坐标系 (Kc是3x3)，需要扩展为齐次形式
        Kc_homo = np.hstack([Kc, np.zeros((3, 1))])  # 扩展为3x4

        # 构建线性方程组 A * X = 0
        A = np.vstack([xc[i][0] * Kc_homo[2, :] - Kc_homo[0, :],
                       xc[i][1] * Kc_homo[2, :] - Kc_homo[1, :],
                       xp[i][0] * G[2, :] - G[0, :],
                       xp[i][1] * G[2, :] - G[1, :]])

        # SVD求解
        _, _, V = np.linalg.svd(A)
        Xh = V[-1]

        # 转换为非齐次坐标
        if abs(Xh[3]) > 1e-8:  # 避免除零
            Xh /= Xh[3]
            X.append(Xh[:3])
        else:
            # 如果齐次坐标的最后一个分量接近0，使用前3个分量
            X.append(Xh[:3])

    X = np.array(X)                    # 4×3 相机坐标
    # 简化：直接取 XY 框
    xy = X[:, :2]
    rect = cv2.minAreaRect(xy.astype(np.float32))
    box = cv2.boxPoints(rect)
    return box                         # 4×2

# 4. 计算预扭曲 homography
def compute_homography(src_pts, dst_pts):
    H, _ = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
    return H

# 5. 主循环
def main():
    canvas = cv2.imread(r"D:\Dataset\keystone\cam_1080p_10.jpg")
    if canvas is None:
        # 如果图像文件不存在，创建一个测试图像
        print("Warning: Could not load image file, using test image instead")
        canvas = np.zeros((720, 1280, 3), dtype=np.uint8)
        canvas[:] = (100, 150, 200)  # 填充颜色

    quad = np.array([[380,218], [870, 213], [895,492], [349, 491]], dtype=np.float32)
    dist = np.zeros(5)
    opt = np.array([[0, 0, 1], [128, 0, 1], [128,72, 1], [0, 72, 1]], dtype=np.float32)
    _, rvec, tvec = cv2.solvePnP(opt, quad, Kc, dist)
    R, _ = cv2.Rodrigues(rvec)
    # 把棋盘格点转到相机坐标系
    X = (R @ opt.T + tvec).T
    X = np.append(X, np.ones((4, 1)), axis=1)
    print(G,X)
    p = G @ X.T
    p[0] = p[0] / p[2]
    p[1] = p[1] / p[2]
    p[2] = p[2] / p[2]
    print(p)
    quit()
    if quad is not None:
        # 对应投影仪角点 = 绿框四角
        h_canvas, w_canvas = canvas.shape[:2]
        xp = np.array([[0, 0], [w_canvas, 0], [w_canvas, h_canvas], [0, h_canvas]], dtype=np.float32)
        xc = quad.astype(np.float32)

        try:
            # 三角化→内接矩形
            rect_xy = triangulate_and_rect(xc, xp, Kc, G)
            # 映射回投影仪像素
            print(xp, rect_xy)
            H = compute_homography(xp, rect_xy)
            warped = cv2.warpPerspective(canvas, H, (w_canvas, h_canvas))
        except Exception as e:
            print("Error during triangulation:", e)
            import traceback
            traceback.print_exc()
            warped = canvas
    else:
        warped = canvas

    cv2.imshow('proj', warped)
    cv2.waitKey(0)  # 等待按键
    cv2.destroyAllWindows()


    # cap = cv2.VideoCapture(0)
    # img_orig = cv2.imread(STAR_HUB)
    # canvas, (w_orig, h_orig) = make_green_frame(img_orig)
    # cv2.namedWindow('proj', cv2.WINDOW_NORMAL)
    # cv2.setWindowProperty('proj', cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_FULLSCREEN)

    # while True:
    #     ret, frame = cap.read()
    #     if not ret:
    #         break
    #     gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    #     quad = detect_quad(gray)
    #     if quad is not None:
    #         # 对应投影仪角点 = 绿框四角
    #         h_canvas, w_canvas = canvas.shape[:2]
    #         xp = np.array([[0, 0], [w_canvas, 0], [w_canvas, h_canvas], [0, h_canvas]], dtype=np.float32)
    #         xc = quad.astype(np.float32)
    #         # 三角化→内接矩形
    #         rect_xy = triangulate_and_rect(xc, xp, Kc, G)
    #         # 映射回投影仪像素
    #         H = compute_homography(xp, rect_xy)
    #         warped = cv2.warpPerspective(canvas, H, (w_canvas, h_canvas))
    #     else:
    #         warped = canvas
    #     cv2.imshow('proj', warped)
    #     if cv2.waitKey(1) & 0xFF == ord('q'):
    #         break
    # cap.release()
    # cv2.destroyAllWindows()

if __name__ == '__main__':
    main()