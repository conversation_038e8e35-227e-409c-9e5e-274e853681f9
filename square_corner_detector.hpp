#ifndef SQUARE_CORNER_DETECTOR_HPP
#define SQUARE_CORNER_DETECTOR_HPP

#include <opencv2/opencv.hpp>
#include <vector>

/**
 * @brief Structure to hold the four corner points of the 2x2 square grid
 */
struct SquareGridCorners {
    cv::Point2f topLeft;      // Top-left corner of top-left square
    cv::Point2f topRight;     // Top-right corner of top-right square
    cv::Point2f bottomLeft;   // Bottom-left corner of bottom-left square
    cv::Point2f bottomRight;  // Bottom-right corner of bottom-right square
    bool isValid;             // Whether all corners were successfully detected
    
    SquareGridCorners() : isValid(false) {}
};

/**
 * @brief Structure to represent a detected square with its corner points
 */
struct DetectedSquare {
    std::vector<cv::Point2f> corners;  // Four corner points of the square
    cv::Point2f center;                // Center point of the square
    double area;                       // Area of the square
    
    DetectedSquare() : area(0.0) {}
};

/**
 * @brief Class for detecting 4 white squares arranged in 2x2 grid and extracting corner points
 */
class SquareCornerDetector {
public:
    /**
     * @brief Constructor with configurable parameters
     * @param minArea Minimum area for square detection
     * @param maxArea Maximum area for square detection
     * @param approxEpsilon Epsilon for contour approximation
     * @param cannyLow Lower threshold for Canny edge detection
     * @param cannyHigh Upper threshold for Canny edge detection
     */
    SquareCornerDetector(double minArea = 100.0, 
                        double maxArea = 50000.0,
                        double approxEpsilon = 0.02,
                        double cannyLow = 50.0,
                        double cannyHigh = 150.0);
    
    /**
     * @brief Destructor
     */
    ~SquareCornerDetector();
    
    /**
     * @brief Detect squares in the input image and extract corner points
     * @param image Input image containing 4 white squares in 2x2 arrangement
     * @return True if exactly 4 squares were detected and corners extracted successfully
     */
    bool detectSquareCorners(const cv::Mat& image);
    
    /**
     * @brief Get the detected corner points of the 2x2 square grid
     * @return SquareGridCorners structure containing the four corner points
     */
    const SquareGridCorners& getGridCorners() const { return gridCorners_; }
    
    /**
     * @brief Get all detected squares
     * @return Vector of detected squares
     */
    const std::vector<DetectedSquare>& getDetectedSquares() const { return detectedSquares_; }
    
    /**
     * @brief Draw the detected squares and corner points on the image
     * @param image Input image to draw on
     * @param drawSquares Whether to draw square contours
     * @param drawCorners Whether to draw corner points
     * @return Image with drawn results
     */
    cv::Mat drawResults(const cv::Mat& image, bool drawSquares = true, bool drawCorners = true) const;
    
    /**
     * @brief Set minimum area threshold for square detection
     * @param minArea Minimum area value
     */
    void setMinArea(double minArea) { minArea_ = minArea; }
    
    /**
     * @brief Set maximum area threshold for square detection
     * @param maxArea Maximum area value
     */
    void setMaxArea(double maxArea) { maxArea_ = maxArea; }
    
    /**
     * @brief Set epsilon for contour approximation
     * @param epsilon Approximation epsilon value
     */
    void setApproxEpsilon(double epsilon) { approxEpsilon_ = epsilon; }
    
    /**
     * @brief Set Canny edge detection thresholds
     * @param low Lower threshold
     * @param high Upper threshold
     */
    void setCannyThresholds(double low, double high) { 
        cannyLow_ = low; 
        cannyHigh_ = high; 
    }

private:
    // Detection parameters
    double minArea_;        // Minimum area for square detection
    double maxArea_;        // Maximum area for square detection
    double approxEpsilon_;  // Epsilon for contour approximation
    double cannyLow_;       // Lower threshold for Canny edge detection
    double cannyHigh_;      // Upper threshold for Canny edge detection
    
    // Detection results
    SquareGridCorners gridCorners_;           // Final corner points of the 2x2 grid
    std::vector<DetectedSquare> detectedSquares_;  // All detected squares
    
    /**
     * @brief Preprocess the input image for contour detection
     * @param image Input image
     * @return Preprocessed binary image
     */
    cv::Mat preprocessImage(const cv::Mat& image);
    
    /**
     * @brief Detect square contours in the binary image
     * @param binary Binary input image
     * @return Vector of detected square contours
     */
    std::vector<std::vector<cv::Point>> detectSquareContours(const cv::Mat& binary);
    
    /**
     * @brief Filter and validate square contours
     * @param contours Input contours
     * @return Vector of valid square contours
     */
    std::vector<std::vector<cv::Point>> filterSquareContours(const std::vector<std::vector<cv::Point>>& contours);
    
    /**
     * @brief Convert contours to DetectedSquare objects
     * @param contours Valid square contours
     */
    void convertContoursToSquares(const std::vector<std::vector<cv::Point>>& contours);
    
    /**
     * @brief Arrange detected squares in 2x2 grid and extract corner points
     * @return True if arrangement was successful
     */
    bool arrangeSquaresAndExtractCorners();
    
    /**
     * @brief Check if a contour approximates to a square (4 vertices)
     * @param contour Input contour
     * @return True if contour is square-like
     */
    bool isSquareContour(const std::vector<cv::Point>& contour);
    
    /**
     * @brief Calculate the center point of a contour
     * @param contour Input contour
     * @return Center point
     */
    cv::Point2f calculateCenter(const std::vector<cv::Point>& contour);
    
    /**
     * @brief Sort squares by their position (top-left, top-right, bottom-left, bottom-right)
     * @param squares Input squares to sort
     * @return Sorted squares in the order: [top-left, top-right, bottom-left, bottom-right]
     */
    std::vector<DetectedSquare> sortSquaresByPosition(const std::vector<DetectedSquare>& squares);
};

#endif // SQUARE_CORNER_DETECTOR_HPP
