#!/usr/bin/env python3
"""
测试修改后的recover_3d_points函数
"""

import numpy as np
import cv2
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import recover_3d_points, PROJ_RES

def test_recover_3d_points():
    """测试recover_3d_points函数"""

    # 创建测试数据
    print("创建测试数据...")

    # 模拟相机内参矩阵K (3x3)
    K = np.array([
        [800, 0, 320],
        [0, 800, 240],
        [0, 0, 1]
    ], dtype=np.float32)

    # 模拟投影矩阵G (3x4)
    G = np.array([
        [1000, 0, 960, 100],
        [0, 1000, 540, 200],
        [0, 0, 1, 0.001]
    ], dtype=np.float32)

    # 模拟畸变系数（假设无畸变）
    dist = np.zeros(5, dtype=np.float32)

    # 模拟四边形在相机图像中的2D坐标（像素坐标）
    quad_2d = np.array([
        [100, 100],
        [500, 120],
        [480, 400],
        [120, 380]
    ], dtype=np.float32)

    print(f"输入参数:")
    print(f"K (相机内参矩阵):\n{K}")
    print(f"G shape: {G.shape}")
    print(f"quad_2d (像素坐标): {quad_2d}")
    print(f"PROJ_RES: {PROJ_RES}")

    # 验证相机内参的提取
    fx, fy = K[0,0], K[1,1]
    cx, cy = K[0,2], K[1,2]
    print(f"提取的相机内参: fx={fx}, fy={fy}, cx={cx}, cy={cy}")

    try:
        # 调用修改后的函数
        print("\n调用recover_3d_points函数...")
        X_3d = recover_3d_points(quad_2d, G, K, dist)

        print(f"\n成功恢复3D点!")
        print(f"输出3D点形状: {X_3d.shape}")
        print(f"3D点坐标:")
        for i, point in enumerate(X_3d):
            print(f"  点{i+1}: [{point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f}]")

        # 验证结果的合理性
        print(f"\n验证结果:")
        print(f"Z坐标范围: [{np.min(X_3d[:,2]):.3f}, {np.max(X_3d[:,2]):.3f}]")
        print(f"X坐标范围: [{np.min(X_3d[:,0]):.3f}, {np.max(X_3d[:,0]):.3f}]")
        print(f"Y坐标范围: [{np.min(X_3d[:,1]):.3f}, {np.max(X_3d[:,1]):.3f}]")

        # 检查是否有无效值
        if np.any(np.isnan(X_3d)) or np.any(np.isinf(X_3d)):
            print("警告: 结果中包含NaN或无穷大值!")
        else:
            print("结果数值正常")

        # 验证重投影误差
        print(f"\n验证重投影:")
        for i, X_pt in enumerate(X_3d):
            # 相机重投影
            X_hom = np.append(X_pt, 1)
            cam_proj = K @ X_pt
            u_reproj = cam_proj[0] / cam_proj[2]
            v_reproj = cam_proj[1] / cam_proj[2]

            u_orig, v_orig = quad_2d[i]
            error = np.sqrt((u_reproj - u_orig)**2 + (v_reproj - v_orig)**2)
            print(f"  点{i+1} 重投影误差: {error:.3f} 像素")

        return True

    except Exception as e:
        print(f"函数调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_data():
    """使用真实数据测试"""
    print("\n" + "="*50)
    print("使用真实数据测试")
    print("="*50)
    
    try:
        # 尝试加载真实的相机参数
        K_path = r"D:\Program\Project\project\czcv_camera_new\algo1\Kc.npy"
        G_path = r"D:\Program\Project\project\czcv_camera_new\algo1_2\G.npy"
        
        if os.path.exists(K_path) and os.path.exists(G_path):
            K = np.load(K_path)
            G = np.load(G_path)
            dist = np.zeros(5, dtype=np.float32)
            
            # 使用main.py中的真实数据
            quad_2d = np.array([[378,218], [870,213], [895,493], [348,491]], dtype=np.float32)
            
            print(f"加载真实参数:")
            print(f"K:\n{K}")
            print(f"G shape: {G.shape}")
            print(f"quad_2d: {quad_2d}")
            
            X_3d = recover_3d_points(quad_2d, G, K, dist)
            
            print(f"\n真实数据恢复的3D点:")
            for i, point in enumerate(X_3d):
                print(f"  点{i+1}: [{point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f}]")
                
            return True
        else:
            print("真实数据文件不存在，跳过真实数据测试")
            return True
            
    except Exception as e:
        print(f"真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("测试recover_3d_points函数")
    print("="*50)
    
    # 基本功能测试
    success1 = test_recover_3d_points()
    
    # 真实数据测试
    success2 = test_with_real_data()
    
    if success1 and success2:
        print("\n" + "="*50)
        print("所有测试通过! ✓")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("测试失败! ✗")
        print("="*50)
