#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件D列时间格式统一脚本
将D列的时间格式统一改为：yyyy.mm.dd
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

def format_date_column(file_path, output_path=None):
    """
    将Excel文件D列的时间格式统一改为yyyy.mm.dd格式
    
    Args:
        file_path (str): 输入Excel文件路径
        output_path (str, optional): 输出文件路径，如果为None则覆盖原文件
    
    Returns:
        bool: 处理是否成功
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 '{file_path}' 不存在")
            return False
        
        print(f"正在读取文件: {file_path}")
        
        # 读取Excel文件
        # 尝试不同的引擎来读取文件
        try:
            df = pd.read_excel(file_path, engine='openpyxl')
        except ImportError:
            try:
                df = pd.read_excel(file_path, engine='xlrd')
            except ImportError:
                try:
                    df = pd.read_excel(file_path)
                except Exception as e:
                    print(f"无法读取Excel文件，请安装必要的依赖: {e}")
                    return False
        
        print(f"文件读取成功，共有 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        
        # 检查是否有D列（第4列，索引为3）
        if len(df.columns) < 4:
            print("错误：文件中没有D列")
            return False
        
        # 获取D列的列名
        d_column = df.columns[3]  # D列是第4列，索引为3
        print(f"D列列名: {d_column}")
        
        # 显示D列前几行的原始数据
        print(f"\nD列前5行原始数据:")
        print(df[d_column].head())
        print(f"D列数据类型: {df[d_column].dtype}")
        
        # 备份原始D列数据
        original_data = df[d_column].copy()
        
        # 处理D列的时间格式
        def format_date_value(value):
            """
            将各种时间格式转换为yyyy.mm.dd格式
            """
            if pd.isna(value) or value == '' or value is None:
                return value  # 保持空值不变
            
            try:
                # 如果已经是datetime类型
                if isinstance(value, pd.Timestamp) or isinstance(value, datetime):
                    return value.strftime('%Y.%m.%d')
                
                # 如果是字符串，尝试解析
                if isinstance(value, str):
                    # 移除可能的空格
                    value = value.strip()
                    if value == '':
                        return value
                    
                    # 尝试多种日期格式解析
                    date_formats = [
                        '%Y-%m-%d',      # 2023-01-01
                        '%Y/%m/%d',      # 2023/01/01
                        '%Y.%m.%d',      # 2023.01.01 (已经是目标格式)
                        '%m/%d/%Y',      # 01/01/2023
                        '%m-%d-%Y',      # 01-01-2023
                        '%d/%m/%Y',      # 01/01/2023 (欧洲格式)
                        '%d-%m-%Y',      # 01-01-2023 (欧洲格式)
                        '%Y%m%d',        # 20230101
                        '%m/%d/%y',      # 01/01/23
                        '%m-%d-%y',      # 01-01-23
                        '%d/%m/%y',      # 01/01/23 (欧洲格式)
                        '%d-%m-%y',      # 01-01-23 (欧洲格式)
                    ]
                    
                    for fmt in date_formats:
                        try:
                            parsed_date = datetime.strptime(value, fmt)
                            return parsed_date.strftime('%Y.%m.%d')
                        except ValueError:
                            continue
                    
                    # 如果所有格式都失败，尝试pandas的智能解析
                    try:
                        parsed_date = pd.to_datetime(value)
                        return parsed_date.strftime('%Y.%m.%d')
                    except:
                        print(f"警告：无法解析日期格式: '{value}'，保持原值")
                        return value
                
                # 如果是数字（可能是Excel的日期序列号）
                if isinstance(value, (int, float)):
                    try:
                        # Excel日期序列号转换（从1900年1月1日开始计算）
                        parsed_date = pd.to_datetime(value, origin='1899-12-30', unit='D')
                        return parsed_date.strftime('%Y.%m.%d')
                    except:
                        print(f"警告：无法解析数字日期: {value}，保持原值")
                        return value
                
                # 其他情况保持原值
                return value
                
            except Exception as e:
                print(f"警告：处理日期值 '{value}' 时出错: {e}，保持原值")
                return value
        
        # 应用格式转换
        print("\n正在转换D列时间格式...")
        df[d_column] = df[d_column].apply(format_date_value)
        
        # 显示转换后的前几行数据
        print(f"\nD列前5行转换后数据:")
        print(df[d_column].head())
        
        # 统计转换情况
        changed_count = 0
        for i, (orig, new) in enumerate(zip(original_data, df[d_column])):
            if str(orig) != str(new):
                changed_count += 1
        
        print(f"\n转换统计:")
        print(f"总行数: {len(df)}")
        print(f"已转换行数: {changed_count}")
        print(f"未变更行数: {len(df) - changed_count}")
        
        # 确定输出文件路径
        if output_path is None:
            output_path = file_path
        
        # 保存文件
        print(f"\n正在保存文件到: {output_path}")
        
        # 创建输出目录（如果不存在）
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 保存Excel文件
        try:
            df.to_excel(output_path, index=False, engine='openpyxl')
        except ImportError:
            try:
                df.to_excel(output_path, index=False, engine='xlsxwriter')
            except ImportError:
                df.to_excel(output_path, index=False)
        
        print(f"文件保存成功！")
        print(f"D列时间格式已统一为 yyyy.mm.dd 格式")
        
        return True
        
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 目标文件路径
    file_path = r"D:\大图.xlsx"
    
    print("Excel文件D列时间格式统一工具")
    print("=" * 50)
    print(f"目标文件: {file_path}")
    print(f"目标格式: yyyy.mm.dd")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 '{file_path}' 不存在")
        print("请确认文件路径是否正确")
        return
    
    # 询问是否创建备份
    backup_choice = input("\n是否创建备份文件？(y/n，默认为y): ").strip().lower()
    if backup_choice != 'n':
        backup_path = file_path.replace('.xlsx', '_backup.xlsx')
        try:
            import shutil
            shutil.copy2(file_path, backup_path)
            print(f"备份文件已创建: {backup_path}")
        except Exception as e:
            print(f"创建备份文件失败: {e}")
            return
    
    # 执行格式转换
    success = format_date_column(file_path)
    
    if success:
        print("\n" + "=" * 50)
        print("处理完成！D列时间格式已统一为 yyyy.mm.dd")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("处理失败，请检查错误信息")
        print("=" * 50)

if __name__ == "__main__":
    main()
