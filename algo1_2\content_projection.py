import cv2
import numpy as np
import yaml
from particle_filter_tracking import Part<PERSON><PERSON><PERSON>er, pose_to_transform, project_points_3d_to_2d
from quadrangle_detection import detect_quadrangle

# -------------------------- 1. 加载校准参数 --------------------------
def load_params():
    # 相机参数
    with open("./camera_params.yml", "r") as f:
        cam_params = yaml.safe_load(f)
    camera_matrix = np.array(cam_params["camera_matrix"])
    dist_coeffs = np.array(cam_params["dist_coeffs"])
    
    # 投影仪投影矩阵Gp
    with open("./projector_Gp.yml", "r") as f:
        proj_params = yaml.safe_load(f)
    Gp = np.array(proj_params["Gp"])
    
    return camera_matrix, dist_coeffs, Gp

camera_matrix, dist_coeffs, Gp = load_params()

# -------------------------- 2. 核心函数 --------------------------
def cardboard_3d_to_projector_2d(cardboard_3d, Gp):
    """将纸板3D点（相机坐标系）投影到投影仪2D平面（论文公式3、9-11）"""
    # Gp: (3,4)，3D点：(N,3) → 齐次坐标：(N,4)
    cardboard_3d_homo = np.hstack([cardboard_3d, np.ones((cardboard_3d.shape[0], 1), dtype=np.float32)])
    # 投影：Ip ∝ Gp * Pc（齐次乘法）
    projector_homo = Gp @ cardboard_3d_homo.T  # (3, N)
    # 归一化到2D坐标（U/S, V/S）
    projector_2d = (projector_homo[:2] / projector_homo[2:]).T
    return projector_2d.astype(np.float32)

def warp_content(content, src_corners, dst_corners):
    """内容预扭曲：根据单应矩阵H将内容 warp 到目标四边形"""
    # 计算单应矩阵H（论文公式24-25）
    H, _ = cv2.findHomography(src_corners, dst_corners, cv2.RANSAC)
    if H is None:
        return None
    # 应用单应矩阵扭曲内容
    h, w = content.shape[:2]
    warped_content = cv2.warpPerspective(content, H, (w, h))
    return warped_content

# -------------------------- 3. 系统主流程 --------------------------
def main(content_path, projector_res=(1280, 1024), cardboard_size=(351, 300)):
    """
    系统主函数：
        content_path: 显示内容路径（图片或视频）
        projector_res: 投影仪分辨率
        cardboard_size: 纸板尺寸（mm）
    """
    # 1. 加载显示内容（支持图片或视频）
    is_video = content_path.endswith((".mp4", ".avi", ".mov"))
    if is_video:
        cap_content = cv2.VideoCapture(content_path)
        ret, content = cap_content.read()
        if not ret:
            raise ValueError("无法读取视频文件！")
    else:
        content = cv2.imread(content_path)
        if content is None:
            raise ValueError("无法读取图片文件！")
    # 调整内容尺寸为投影仪分辨率
    content = cv2.resize(content, projector_res)
    # 内容源角点（左上→右上→右下→左下）
    src_corners = np.array([
        [0, 0],
        [projector_res[0], 0],
        [projector_res[0], projector_res[1]],
        [0, projector_res[1]]
    ], dtype=np.float32)


    #quad_corners, frame_draw = detect_quadrangle(frame_camera, camera_matrix, dist_coeffs)
    quad_corners = np.array([[378,218], [870,213], [895,493], [348,491]], dtype=np.float32)
    if quad_corners is not None:
        # 用solvePnP获取初始位姿
        cardboard_3d_init = np.array([
            [0, 0, 0], [280, 0, 0], [280, 160, 0], [0, 160, 0]
        ], dtype=np.float32)
        ret, rvec, tvec = cv2.solvePnP(cardboard_3d_init, quad_corners, camera_matrix, dist_coeffs)
        if ret:
            rx, ry, rz = rvec.flatten()
            tx, ty, tz = tvec.flatten()
            init_pose = [rx, ry, rz, tx, ty, tz]

    R, T = pose_to_transform(rx, ry, rz, tx, ty, tz)

    cardboard_3d = np.array([
            [0, 0, 0],                  # 左上
            [cardboard_size[0], 0, 0],  # 右上
            [cardboard_size[0], cardboard_size[1], 0],  # 右下
            [0, cardboard_size[1], 0]   # 左下
        ], dtype=np.float32)
        
    # 步骤3：计算纸板3D坐标（相机坐标系）并投影到投影仪2D平面
    cardboard_3d = (R @ cardboard_3d.T).T + T  # 纸板3D点（相机坐标系）

    print("cardboard_3d", cardboard_3d)
    print("Gp", Gp)
    projector_2d = cardboard_3d_to_projector_2d(cardboard_3d, Gp)  # 投影仪2D角点
    print("projector_2d", projector_2d)
    # 确保投影仪角点在有效范围内
    projector_2d = np.clip(projector_2d, 0, max(projector_res))
    
    print("src_corners", src_corners)
    
    # 步骤4：内容预扭曲并投影
    warped_content = warp_content(content, src_corners, projector_2d)
    print(content.shape, warped_content.shape)
    proj_window = "Projector Display"
    cv2.namedWindow(proj_window, cv2.WINDOW_NORMAL)
    cv2.imshow(proj_window, warped_content)
    cv2.waitKey(0)
    # # 步骤5：显示相机跟踪结果
    # projected_2d_cam = project_points_3d_to_2d(
    #     cardboard_3d, R, T, camera_matrix, dist_coeffs
    # )            
    

    # # 2. 初始化相机和投影仪窗口
    # cap_camera = cv2.VideoCapture(0)
    # cap_camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    # cap_camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    # # 投影仪窗口（移到第二屏幕）
    # proj_window = "Projector Display"
    # cv2.namedWindow(proj_window, cv2.WINDOW_NORMAL)
    # cv2.moveWindow(proj_window, projector_res[0], 0)  # 假设第二屏幕在右侧
    
    # # 3. 检测纸板并初始化粒子滤波跟踪
    # print("正在检测纸板以初始化系统...")
    # init_pose = None
    # while init_pose is None:
    #     ret, frame_camera = cap_camera.read()
    #     if not ret: break
    #     quad_corners, frame_draw = detect_quadrangle(frame_camera, camera_matrix, dist_coeffs)
    #     cv2.imshow("Initial Detection (Wait for cardboard)...", frame_draw)
    #     cv2.waitKey(1)
    #     if quad_corners is not None:
    #         # 用solvePnP获取初始位姿
    #         cardboard_3d_init = np.array([
    #             [0, 0, 0], [351, 0, 0], [351, 300, 0], [0, 300, 0]
    #         ], dtype=np.float32)
    #         ret, rvec, tvec = cv2.solvePnP(cardboard_3d_init, quad_corners, camera_matrix, dist_coeffs)
    #         if ret:
    #             rx, ry, rz = rvec.flatten()
    #             tx, ty, tz = tvec.flatten()
    #             init_pose = [rx, ry, rz, tx, ty, tz]
    #             print(f"初始位姿初始化完成！")
    # cv2.destroyAllWindows()
    
    # if init_pose is None:
    #     raise ValueError("未检测到纸板，系统无法启动！")
    
    # # 4. 初始化粒子滤波
    # pf = ParticleFilter(init_pose, camera_matrix, dist_coeffs, cardboard_size)
    
    # # 5. 实时跟踪与投影
    # print("系统启动成功！（Press 'q' to quit）")
    # while True:
    #     # 步骤1：读取相机图像和显示内容
    #     ret_cam, frame_camera = cap_camera.read()
    #     if not ret_cam: break
    #     if is_video:
    #         ret_cont, content = cap_content.read()
    #         if not ret_cont:
    #             cap_content.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 视频循环
    #             ret_cont, content = cap_content.read()
    #         content = cv2.resize(content, projector_res)
        
    #     # 步骤2：粒子滤波跟踪纸板位姿
    #     # 提取观测线段
    #     frame_undistort = cv2.undistort(frame_camera, camera_matrix, dist_coeffs)
    #     gray = cv2.cvtColor(frame_undistort, cv2.COLOR_BGR2GRAY)
    #     blur = cv2.GaussianBlur(gray, (5,5), 0)
    #     edges = cv2.Canny(blur, 50, 150)
    #     lines = cv2.HoughLinesP(edges, 1, np.pi/180, 50, minLineLength=50, maxLineGap=20)
    #     observed_lines = lines[0] if lines is not None else []
    #     # 粒子滤波预测→更新→重采样
    #     pf.predict()
    #     pf.update(observed_lines)
    #     pf.resample()
    #     # 获取最优位姿
    #     best_pose = pf.get_best_pose()
    #     rx, ry, rz, tx, ty, tz = best_pose
    #     R, T = pose_to_transform(rx, ry, rz, tx, ty, tz)
        
    #     # 步骤3：计算纸板3D坐标（相机坐标系）并投影到投影仪2D平面
    #     cardboard_3d = (R @ pf.cardboard_3d.T).T + T  # 纸板3D点（相机坐标系）
    #     projector_2d = cardboard_3d_to_projector_2d(cardboard_3d, Gp)  # 投影仪2D角点
    #     # 确保投影仪角点在有效范围内
    #     projector_2d = np.clip(projector_2d, 0, max(projector_res))
        
    #     # 步骤4：内容预扭曲并投影
    #     warped_content = warp_content(content, src_corners, projector_2d)
    #     if warped_content is not None:
    #         cv2.imshow(proj_window, warped_content)
        
    #     # 步骤5：显示相机跟踪结果
    #     projected_2d_cam = project_points_3d_to_2d(
    #         pf.cardboard_3d, R, T, camera_matrix, dist_coeffs
    #     )
    #     frame_track = frame_undistort.copy()
    #     for i in range(4):
    #         x1, y1 = projected_2d_cam[i].astype(int)
    #         x2, y2 = projected_2d_cam[(i+1)%4].astype(int)
    #         cv2.line(frame_track, (x1, y1), (x2, y2), (255, 0, 0), 2)
    #     cv2.imshow("Camera Tracking View", cv2.resize(frame_track, (800, 600)))
        
    #     # 退出条件
    #     if cv2.waitKey(1) == ord('q'):
    #         break
    
    # # 释放资源
    # cap_camera.release()
    # if is_video:
    #     cap_content.release()
    # cv2.destroyAllWindows()

# -------------------------- 运行系统 --------------------------
if __name__ == "__main__":
    # 输入显示内容路径（图片或视频）
    CONTENT_PATH = "D:/Dataset/keystone/20251027-113839.png"  # 替换为你的内容路径
    # 运行系统
    main(CONTENT_PATH, projector_res=(1920, 1080), cardboard_size=(280, 160))