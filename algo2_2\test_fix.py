#!/usr/bin/env python3
"""
测试修复后的代码是否能正常导入和运行关键函数
"""

import numpy as np
from sklearn.base import BaseEstimator
from sklearn.linear_model import RANSACRegressor

print("开始测试修复...")

# 测试1: 导入模块
try:
    import main
    print("✓ 成功导入 main 模块")
except Exception as e:
    print(f"✗ 导入 main 模块失败: {e}")
    exit(1)

# 测试2: 测试自定义估计器
try:
    class ProjectionMatrixEstimator(BaseEstimator):
        def __init__(self):
            self.coef_ = None
            
        def fit(self, X, y=None):
            # 使用SVD求解投影矩阵
            U, S, VT = np.linalg.svd(X, full_matrices=False)
            self.coef_ = VT[-1].reshape(3, 4)  # 最小奇异值对应的特征向量即G
            return self
            
        def predict(self, X):
            # 计算重投影误差用于RANSAC
            if self.coef_ is None:
                raise ValueError("Model not fitted yet")
            # 这里返回零向量，因为我们主要关心的是fit过程
            return np.zeros(X.shape[0])
            
        def score(self, X, y=None):
            # 返回负的重投影误差作为得分
            if self.coef_ is None:
                return -np.inf
            # 简单返回0，RANSAC会使用residual_threshold来判断内点
            return 0.0

    # 创建测试数据
    test_data = np.random.rand(10, 12)  # 10个点，每个点12个方程系数
    
    # 测试自定义估计器
    estimator = ProjectionMatrixEstimator()
    ransac = RANSACRegressor(
        estimator=estimator,
        residual_threshold=2.0,
        random_state=42
    )
    
    ransac.fit(test_data, np.zeros(test_data.shape[0]))
    G = ransac.estimator_.coef_
    
    print("✓ RANSAC估计器测试成功")
    print(f"  投影矩阵形状: {G.shape}")
    
except Exception as e:
    print(f"✗ RANSAC估计器测试失败: {e}")

# 测试3: 测试关键函数是否存在
try:
    # 检查关键函数是否存在
    functions_to_check = [
        'get_cardboard_plane',
        'collect_correspondences', 
        'estimate_proj_matrix',
        'detect_quadrilateral',
        'recover_3d_points'
    ]
    
    for func_name in functions_to_check:
        if hasattr(main, func_name):
            print(f"✓ 函数 {func_name} 存在")
        else:
            print(f"✗ 函数 {func_name} 不存在")
            
except Exception as e:
    print(f"✗ 函数检查失败: {e}")

print("测试完成！")
