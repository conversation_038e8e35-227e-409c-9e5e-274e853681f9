import numpy as np
from scipy.linalg import svd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# 定义8个3维点
points = np.array([
    # 四边形1的4个点
    [-663.40788856, -416.93399977, 1241.72878607],
    [817.47550169, -512.12748461, 1448.39459962],
    [911.65503184, 392.34151613, 1322.65532539],
    [-727.93803401, 316.05002203, 1121.55349277],
    # 四边形2的4个点
    [-371.96439105, -306.48398539, 1262.69683101],
    [440.2381145, -358.69362418, 1376.04437253],
    [494.75465923, 289.95606478, 1284.17905177],
    [-317.44784632, 342.16570357, 1170.83151024]
])

def method1_rank_analysis(points):
    """方法1: 使用矩阵秩分析判断共面性"""
    print("=== 方法1: 矩阵秩分析 ===")
    
    # 将所有点相对于第一个点进行平移
    centered_points = points - points[0]
    
    # 去掉第一个点（现在是原点）
    vectors = centered_points[1:]
    
    print(f"向量矩阵形状: {vectors.shape}")
    print(f"向量矩阵:\n{vectors}")
    
    # 计算矩阵的秩
    rank = np.linalg.matrix_rank(vectors)
    print(f"矩阵的秩: {rank}")
    
    # 如果8个点共面，那么7个向量的秩应该 <= 2
    is_coplanar_rank = rank <= 2
    print(f"基于秩的共面判断: {'共面' if is_coplanar_rank else '不共面'}")
    
    return is_coplanar_rank, rank

def method2_svd_analysis(points):
    """方法2: 使用SVD分析判断共面性"""
    print("\n=== 方法2: SVD奇异值分解分析 ===")
    
    # 中心化点集
    centroid = np.mean(points, axis=0)
    centered_points = points - centroid
    
    print(f"质心: {centroid}")
    print(f"中心化后的点集:\n{centered_points}")
    
    # 进行SVD分解
    U, s, Vt = svd(centered_points)
    
    print(f"奇异值: {s}")
    print(f"奇异值比例: {s / s[0] if s[0] > 0 else s}")
    
    # 如果点共面，最小的奇异值应该接近0
    tolerance = 1e-10
    num_significant_singular_values = np.sum(s > tolerance)
    
    print(f"显著奇异值数量 (> {tolerance}): {num_significant_singular_values}")
    
    is_coplanar_svd = num_significant_singular_values <= 2
    print(f"基于SVD的共面判断: {'共面' if is_coplanar_svd else '不共面'}")
    
    return is_coplanar_svd, s

def method3_plane_fitting(points):
    """方法3: 拟合平面并计算点到平面的距离"""
    print("\n=== 方法3: 平面拟合分析 ===")
    
    # 使用前3个点定义一个平面
    p1, p2, p3 = points[0], points[1], points[2]
    
    # 计算平面的法向量
    v1 = p2 - p1
    v2 = p3 - p1
    normal = np.cross(v1, v2)
    
    # 归一化法向量
    if np.linalg.norm(normal) > 0:
        normal = normal / np.linalg.norm(normal)
    else:
        print("前三个点共线，无法定义平面")
        return False, []
    
    print(f"平面法向量: {normal}")
    print(f"平面上的参考点: {p1}")
    
    # 计算平面方程: normal · (x - p1) = 0
    # 即: ax + by + cz + d = 0, 其中 d = -normal · p1
    d = -np.dot(normal, p1)
    print(f"平面方程: {normal[0]:.6f}x + {normal[1]:.6f}y + {normal[2]:.6f}z + {d:.6f} = 0")
    
    # 计算所有点到平面的距离
    distances = []
    for i, point in enumerate(points):
        # 点到平面的距离公式: |ax + by + cz + d| / sqrt(a² + b² + c²)
        distance = abs(np.dot(normal, point) + d)
        distances.append(distance)
        print(f"点{i+1} 到平面的距离: {distance:.10f}")
    
    # 判断是否共面
    tolerance = 1e-8
    max_distance = max(distances)
    is_coplanar_plane = max_distance < tolerance
    
    print(f"最大距离: {max_distance:.10f}")
    print(f"容差: {tolerance}")
    print(f"基于平面拟合的共面判断: {'共面' if is_coplanar_plane else '不共面'}")
    
    return is_coplanar_plane, distances

def method4_determinant_analysis(points):
    """方法4: 使用行列式判断共面性"""
    print("\n=== 方法4: 行列式分析 ===")
    
    # 对于任意4个点，如果它们共面，那么由这4个点构成的体积应该为0
    # 即行列式应该为0
    
    coplanar_groups = 0
    total_groups = 0
    tolerance = 1e-8
    
    print("检查所有4点组合的体积:")
    
    from itertools import combinations
    for i, four_points in enumerate(combinations(range(8), 4)):
        if i >= 10:  # 只显示前10个组合
            break
            
        indices = list(four_points)
        selected_points = points[indices]
        
        # 构造矩阵计算体积
        # 体积 = |det(p2-p1, p3-p1, p4-p1)| / 6
        p1, p2, p3, p4 = selected_points
        matrix = np.array([p2-p1, p3-p1, p4-p1])
        det = np.linalg.det(matrix)
        volume = abs(det) / 6
        
        is_coplanar_group = volume < tolerance
        if is_coplanar_group:
            coplanar_groups += 1
        total_groups += 1
        
        print(f"点组合 {[i+1 for i in indices]}: 体积 = {volume:.10f}, 共面: {is_coplanar_group}")
    
    print(f"前{total_groups}个4点组合中，{coplanar_groups}个共面")
    
    return coplanar_groups, total_groups

def visualize_points(points):
    """可视化8个点"""
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制四边形1的点（蓝色）
    quad1_points = points[:4]
    ax.scatter(quad1_points[:, 0], quad1_points[:, 1], quad1_points[:, 2], 
               c='blue', s=100, alpha=0.8, label='Quadrilateral 1')
    
    # 绘制四边形2的点（红色）
    quad2_points = points[4:]
    ax.scatter(quad2_points[:, 0], quad2_points[:, 1], quad2_points[:, 2], 
               c='red', s=100, alpha=0.8, label='Quadrilateral 2')
    
    # 添加点的标签
    for i, point in enumerate(points):
        ax.text(point[0], point[1], point[2], f'P{i+1}', fontsize=10)
    
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.legend()
    ax.set_title('8 Points in 3D Space - Coplanarity Analysis')
    
    plt.tight_layout()
    plt.savefig('coplanarity_analysis.png', dpi=300, bbox_inches='tight')
    print("\n3D可视化图像已保存为: coplanarity_analysis.png")
    plt.show()

if __name__ == "__main__":
    print("8个3维点的共面性分析")
    print("=" * 60)
    
    print("\n所有8个点的坐标:")
    for i, point in enumerate(points):
        quad_label = "四边形1" if i < 4 else "四边形2"
        print(f"点{i+1} ({quad_label}): [{point[0]:.8f}, {point[1]:.8f}, {point[2]:.8f}]")
    
    # 使用多种方法分析共面性
    is_coplanar1, rank = method1_rank_analysis(points)
    is_coplanar2, singular_values = method2_svd_analysis(points)
    is_coplanar3, distances = method3_plane_fitting(points)
    coplanar_groups, total_groups = method4_determinant_analysis(points)
    
    # 综合判断
    print("\n" + "=" * 60)
    print("综合分析结果:")
    print(f"方法1 (矩阵秩): {'共面' if is_coplanar1 else '不共面'} (秩={rank})")
    print(f"方法2 (SVD): {'共面' if is_coplanar2 else '不共面'}")
    print(f"方法3 (平面拟合): {'共面' if is_coplanar3 else '不共面'}")
    print(f"方法4 (行列式): 部分4点组合共面")
    
    # 最终结论
    methods_agree = [is_coplanar1, is_coplanar2, is_coplanar3]
    consensus = sum(methods_agree) >= 2
    
    print(f"\n最终结论: 这8个点 {'共面' if consensus else '不共面'}")
    
    if not consensus:
        print("\n详细说明:")
        print("- 虽然每个四边形内部的4个点可能接近共面")
        print("- 但两个四边形的8个点整体上不在同一个平面内")
        print("- 这表明两个四边形位于不同的平面上")
    
    # 可视化
    visualize_points(points)
