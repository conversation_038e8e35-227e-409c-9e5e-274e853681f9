import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from typing import List, Tuple, Dict

class ZoomSpringModel:
    def __init__(self, screen_width: int = 1080, screen_height: int = 1920):
        """
        初始化Zoom弹簧模型
        :param screen_width: 屏幕宽度（像素）
        :param screen_height: 屏幕高度（像素）
        """
        # 屏幕参数
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # 基础物理参数（默认值，会动态调整）
        self.base_params = {
            "k": 1000,          # 刚度系数
            "c": 25,            # 阻尼系数
            "m": 1,             # 质量（固定为1简化计算）
            "max_speed": screen_width / 0.8  # 最大速度（像素/秒）
        }
        
        # 帧率与时间步长（60fps）
        self.fps = 60
        self.dt = 1 / self.fps  # 每帧时间间隔（秒）
        
        # 窗口状态存储
        self.windows: Dict[str, Dict] = {}  # key:窗口ID，value:窗口状态
        
    def _get_dynamic_params(self, move_distance: float, device_level: str = "high") -> Tu<PERSON>[float, float]:
        """
        动态调整弹簧参数（模拟Zoom场景适配逻辑）
        :param move_distance: 移动距离（像素）
        :param device_level: 设备性能等级（high/mid/low）
        :return: 调整后的k（刚度系数）、c（阻尼系数）
        """
        # 1. 按移动距离调整基础参数
        if move_distance < 50:  # 短距离（局部微调）
            k, c = 800, 28
        elif 50 <= move_distance <= 300:  # 中距离
            k, c = 1000, 25
        else:  # 长距离（跨屏移动）
            k, c = 1200, 20
        
        # 2. 按设备性能调整参数
        if device_level == "mid":  # 中端设备
            k *= 0.85  # k降低15%
            c *= 1.2   # c提高20%
        elif device_level == "low":  # 低端设备（强制过阻尼）
            k = 600
            c = 35
        
        return k, c
    
    def add_window(self, window_id: str, init_x: float, init_y: float, width: float = 200, height: float = 300, is_main: bool = False):
        """
        添加窗口到弹簧系统
        :param window_id: 窗口唯一ID
        :param init_x: 初始X坐标（像素）
        :param init_y: 初始Y坐标（像素）
        :param width: 窗口宽度
        :param height: 窗口高度
        :param is_main: 是否为主窗口（优先级更高）
        """
        self.windows[window_id] = {
            "x": init_x,          # 当前X坐标
            "y": init_y,          # 当前Y坐标
            "target_x": init_x,   # 目标X坐标
            "target_y": init_y,   # 目标Y坐标
            "v_x": 0.0,           # X方向速度（像素/秒）
            "v_y": 0.0,           # Y方向速度（像素/秒）
            "width": width,
            "height": height,
            "is_main": is_main,
            "k": self.base_params["k"],  # 当前刚度系数
            "c": self.base_params["c"],  # 当前阻尼系数
            "device_level": "high"       # 设备性能等级
        }
    
    def set_window_target(self, window_id: str, target_x: float, target_y: float, device_level: str = "high"):
        """
        设置窗口目标位置（触发移动）
        :param window_id: 窗口ID
        :param target_x: 目标X坐标
        :param target_y: 目标Y坐标
        :param device_level: 设备性能等级
        """
        if window_id not in self.windows:
            raise ValueError(f"Window {window_id} not found")
        
        window = self.windows[window_id]
        # 计算移动距离（欧氏距离）
        move_distance = np.sqrt((target_x - window["x"])**2 + (target_y - window["y"])**2)
        # 动态调整参数
        window["k"], window["c"] = self._get_dynamic_params(move_distance, device_level)
        # 设置目标位置
        window["target_x"] = target_x
        window["target_y"] = target_y
        window["device_level"] = device_level
    
    def _calculate_repel_force(self, window1: Dict, window2: Dict) -> Tuple[float, float]:
        """
        计算两个窗口间的排斥力（避免重叠）
        :param window1: 窗口1状态
        :param window2: 窗口2状态
        :return: 窗口1受到的排斥力（Fx, Fy）
        """
        # 计算窗口中心距离
        center1_x = window1["x"] + window1["width"]/2
        center1_y = window1["y"] + window1["height"]/2
        center2_x = window2["x"] + window2["width"]/2
        center2_y = window2["y"] + window2["height"]/2
        
        # 距离向量
        dx = center2_x - center1_x
        dy = center2_y - center1_y
        distance = np.sqrt(dx**2 + dy**2)
        
        # 最小安全距离（窗口半径之和的1.2倍）
        min_safe_dist = 1.2 * (np.sqrt((window1["width"]/2)**2 + (window1["height"]/2)**2) + 
                               np.sqrt((window2["width"]/2)**2 + (window2["height"]/2)**2))
        
        # 距离大于安全距离时无排斥力
        if distance >= min_safe_dist:
            return 0.0, 0.0
        
        # 排斥力计算（参考Zoom多窗口协同逻辑）
        k_repel = 50000  # 排斥力系数
        force_magnitude = k_repel * (min_safe_dist - distance) / distance
        # 力的方向（从窗口2指向窗口1）
        fx = -force_magnitude * (dx / distance)
        fy = -force_magnitude * (dy / distance)
        
        # 主窗口优先级更高，排斥力权重调整
        if window1["is_main"]:
            fx *= 1.5
            fy *= 1.5
        elif window2["is_main"]:
            fx *= 0.5
            fy *= 0.5
        
        return fx, fy
    
    def update(self) -> bool:
        """
        逐帧更新所有窗口位置（核心计算逻辑）
        :return: 是否所有窗口都已稳定（到达目标位置）
        """
        all_stable = True
        repel_forces = {wid: {"fx": 0.0, "fy": 0.0} for wid in self.windows}
        
        # 第一步：计算所有窗口间的排斥力
        window_ids = list(self.windows.keys())
        for i in range(len(window_ids)):
            for j in range(i+1, len(window_ids)):
                wid1 = window_ids[i]
                wid2 = window_ids[j]
                w1 = self.windows[wid1]
                w2 = self.windows[wid2]
                
                fx1, fy1 = self._calculate_repel_force(w1, w2)
                fx2, fy2 = self._calculate_repel_force(w2, w1)
                
                repel_forces[wid1]["fx"] += fx1
                repel_forces[wid1]["fy"] += fy1
                repel_forces[wid2]["fx"] += fx2
                repel_forces[wid2]["fy"] += fy2
        
        # 第二步：更新每个窗口的位置和速度
        for wid, window in self.windows.items():
            # 1. 计算弹簧力（X方向）
            dx = window["x"] - window["target_x"]
            spring_force_x = -window["k"] * dx - window["c"] * window["v_x"]
            # 叠加排斥力
            total_force_x = spring_force_x + repel_forces[wid]["fx"]
            # 计算加速度（F=ma，m=1）
            a_x = total_force_x / window["m"]
            
            # 2. 计算弹簧力（Y方向）
            dy = window["y"] - window["target_y"]
            spring_force_y = -window["k"] * dy - window["c"] * window["v_y"]
            # 叠加排斥力
            total_force_y = spring_force_y + repel_forces[wid]["fy"]
            # 计算加速度
            a_y = total_force_y / window["m"]
            
            # 3. 更新速度（考虑最大速度限制）
            window["v_x"] += a_x * self.dt
            window["v_y"] += a_y * self.dt
            
            # 速度裁剪（避免超速）
            current_speed = np.sqrt(window["v_x"]**2 + window["v_y"]**2)
            if current_speed > window["max_speed"]:
                scale = window["max_speed"] / current_speed
                window["v_x"] *= scale
                window["v_y"] *= scale
            
            # 4. 更新位置
            window["x"] += window["v_x"] * self.dt
            window["y"] += window["v_y"] * self.dt
            
            # 5. 检查是否稳定（位置偏差<1像素且速度<0.5像素/帧）
            pos_stable = (abs(dx) < 1) and (abs(dy) < 1)
            speed_stable = (abs(window["v_x"] * self.dt) < 0.5) and (abs(window["v_y"] * self.dt) < 0.5)
            
            if not (pos_stable and speed_stable):
                all_stable = False
        
        return all_stable
    
    def visualize(self, total_frames: int = 120):
        """
        可视化窗口移动过程（Matplotlib动画）
        :param total_frames: 最大动画帧数
        """
        # 初始化画布
        fig, ax = plt.subplots(figsize=(10, 16))
        ax.set_xlim(0, self.screen_width)
        ax.set_ylim(0, self.screen_height)
        ax.set_title("Zoom Spring Model Window Movement Simulation")
        ax.invert_yaxis()  # 翻转Y轴（符合屏幕坐标系：左上角为原点）
        
        # 绘制屏幕边界
        screen_rect = plt.Rectangle((0, 0), self.screen_width, self.screen_height, 
                                   fill=False, edgecolor="black", linewidth=2)
        ax.add_patch(screen_rect)
        
        # 初始化窗口图形
        window_patches = {}
        for wid, window in self.windows.items():
            color = "red" if window["is_main"] else "blue"
            rect = plt.Rectangle((window["x"], window["y"]), window["width"], window["height"],
                                facecolor=color, alpha=0.7, label=f"Window {wid}")
            ax.add_patch(rect)
            window_patches[wid] = rect
        
        # 添加图例
        ax.legend()
        
        # 动画更新函数
        def update_frame(frame):
            # 更新弹簧模型状态
            stable = self.update()
            # 更新窗口图形位置
            for wid, rect in window_patches.items():
                window = self.windows[wid]
                rect.set_xy((window["x"], window["y"]))
            # 所有窗口稳定后停止动画
            if stable and frame > 10:
                anim.event_source.stop()
            return list(window_patches.values())
        
        # 创建动画
        anim = animation.FuncAnimation(
            fig, update_frame,
            frames=total_frames,
            interval=1000/self.fps,
            blit=True
        )
        
        plt.show()

# ------------------------------
# 示例：模拟Zoom主窗口切换场景
# ------------------------------
if __name__ == "__main__":
    # 1. 初始化弹簧模型（模拟手机屏幕：1080x1920）
    spring_model = ZoomSpringModel(screen_width=1080, screen_height=1920)
    
    # 2. 添加窗口（1个主窗口 + 2个缩略窗口）
    spring_model.add_window("main", init_x=440, init_y=810, width=200, height=300, is_main=True)  # 初始居中
    spring_model.add_window("thumb1", init_x=100, init_y=300, width=150, height=225, is_main=False)
    spring_model.add_window("thumb2", init_x=830, init_y=300, width=150, height=225, is_main=False)
    
    # 3. 设置主窗口目标位置（从中心移动到左侧，模拟发言者切换）
    spring_model.set_window_target("main", target_x=100, target_y=810, device_level="high")
    
    # 4. 可视化移动过程
    spring_model.visualize(total_frames=200)