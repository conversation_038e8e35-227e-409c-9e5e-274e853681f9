#!/usr/bin/env python
"""
Video Frame Viewer 使用示例

这个示例展示了如何使用 video_frame_viewer.py 来查看视频文件
"""

import os
import sys
from video_frame_viewer import VideoFrameViewer

def example_usage():
    """演示如何使用VideoFrameViewer类"""
    
    # 示例1: 基本使用 - 从第0帧开始
    print("示例1: 基本使用")
    video_path = "example_video.mp4"  # 替换为你的视频文件路径
    
    if os.path.exists(video_path):
        viewer = VideoFrameViewer(video_path)
        viewer.run()
    else:
        print(f"视频文件不存在: {video_path}")
    
    # 示例2: 从指定帧开始
    print("\n示例2: 从第100帧开始")
    if os.path.exists(video_path):
        viewer = VideoFrameViewer(video_path, start_frame=100)
        viewer.run()
    else:
        print(f"视频文件不存在: {video_path}")

def create_test_video():
    """创建一个测试视频文件（可选）"""
    import cv2 as cv
    import numpy as np
    
    # 创建一个简单的测试视频
    fourcc = cv.VideoWriter_fourcc(*'mp4v')
    out = cv.VideoWriter('test_video.mp4', fourcc, 30.0, (640, 480))
    
    for i in range(300):  # 创建300帧，10秒的视频
        # 创建一个彩色帧
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 添加帧号文本
        cv.putText(frame, f'Frame {i}', (50, 240), 
                  cv.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # 添加一个移动的圆
        center_x = int(320 + 200 * np.sin(i * 0.1))
        center_y = int(240 + 100 * np.cos(i * 0.1))
        cv.circle(frame, (center_x, center_y), 30, (0, 255, 0), -1)
        
        out.write(frame)
    
    out.release()
    print("测试视频 'test_video.mp4' 已创建")

if __name__ == '__main__':
    print(__doc__)
    
    # 询问是否创建测试视频
    create_test = input("是否创建测试视频? (y/n): ").lower().strip()
    if create_test == 'y':
        create_test_video()
    
    # 运行示例
    example_usage()
