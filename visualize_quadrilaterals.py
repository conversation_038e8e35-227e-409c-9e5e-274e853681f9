import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Sim<PERSON>ei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 定义两个四边形的顶点
quad1 = np.array([
    [-663.40788856, -416.93399977, 1241.72878607],
    [817.47550169, -512.12748461, 1448.39459962],
    [911.65503184, 392.34151613, 1322.65532539],
    [-727.93803401, 316.05002203, 1121.55349277]
])

quad2 = np.array([
    [-371.96439105, -306.48398539, 1262.69683101],
    [440.2381145, -358.69362418, 1376.04437253],
    [494.75465923, 289.95606478, 1284.17905177],
    [-317.44784632, 342.16570357, 1170.83151024]
])

def plot_quadrilaterals(quad1, quad2):
    """绘制两个3D四边形"""
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制第一个四边形
    # 创建闭合的四边形（连接最后一个点到第一个点）
    quad1_closed = np.vstack([quad1, quad1[0]])
    ax.plot(quad1_closed[:, 0], quad1_closed[:, 1], quad1_closed[:, 2], 
            'b-', linewidth=2, label='四边形1')
    ax.scatter(quad1[:, 0], quad1[:, 1], quad1[:, 2], 
               c='blue', s=50, alpha=0.8)
    
    # 绘制第二个四边形
    quad2_closed = np.vstack([quad2, quad2[0]])
    ax.plot(quad2_closed[:, 0], quad2_closed[:, 1], quad2_closed[:, 2], 
            'r-', linewidth=2, label='四边形2')
    ax.scatter(quad2[:, 0], quad2[:, 1], quad2[:, 2], 
               c='red', s=50, alpha=0.8)
    
    # 添加点的标签
    for i, point in enumerate(quad1):
        ax.text(point[0], point[1], point[2], f'Q1-{i+1}', fontsize=8)
    
    for i, point in enumerate(quad2):
        ax.text(point[0], point[1], point[2], f'Q2-{i+1}', fontsize=8)
    
    # 设置坐标轴标签
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.legend()
    ax.set_title('3D Space - Two Quadrilaterals')  # 使用英文避免字体问题

    plt.tight_layout()
    plt.savefig('quadrilaterals_3d.png', dpi=300, bbox_inches='tight')
    print("3D图像已保存为 quadrilaterals_3d.png")
    plt.show()

def calculate_edge_lengths(quad):
    """计算四边形各边的长度"""
    edges = []
    for i in range(4):
        p1 = quad[i]
        p2 = quad[(i + 1) % 4]  # 下一个点，最后一个点连接到第一个点
        edge_length = np.linalg.norm(p2 - p1)
        edges.append(edge_length)
    return edges

def calculate_diagonal_lengths(quad):
    """计算四边形对角线的长度"""
    diag1 = np.linalg.norm(quad[2] - quad[0])  # 点0到点2
    diag2 = np.linalg.norm(quad[3] - quad[1])  # 点1到点3
    return diag1, diag2

def calculate_angles(quad):
    """计算四边形各个内角"""
    angles = []
    for i in range(4):
        # 当前顶点
        vertex = quad[i]
        # 前一个点和后一个点
        prev_point = quad[(i - 1) % 4]
        next_point = quad[(i + 1) % 4]
        
        # 计算两个向量
        vec1 = prev_point - vertex
        vec2 = next_point - vertex
        
        # 计算角度
        cos_angle = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
        # 防止数值误差导致的域错误
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle = np.arccos(cos_angle)
        angles.append(np.degrees(angle))
    
    return angles

def is_rectangle(quad, tolerance=1e-6):
    """判断四边形是否为矩形"""
    print(f"\n=== 判断四边形是否为矩形 ===")
    
    # 计算边长
    edges = calculate_edge_lengths(quad)
    print(f"四边长度: {[f'{e:.6f}' for e in edges]}")
    
    # 计算对角线长度
    diag1, diag2 = calculate_diagonal_lengths(quad)
    print(f"对角线长度: {diag1:.6f}, {diag2:.6f}")
    
    # 计算内角
    angles = calculate_angles(quad)
    print(f"内角 (度): {[f'{a:.2f}' for a in angles]}")
    
    # 矩形的判断条件：
    # 1. 对边相等
    opposite_sides_equal = (abs(edges[0] - edges[2]) < tolerance and 
                           abs(edges[1] - edges[3]) < tolerance)
    
    # 2. 对角线相等
    diagonals_equal = abs(diag1 - diag2) < tolerance
    
    # 3. 所有内角都是90度
    all_angles_90 = all(abs(angle - 90.0) < 0.1 for angle in angles)
    
    print(f"\n判断结果:")
    print(f"对边相等: {opposite_sides_equal}")
    print(f"对角线相等: {diagonals_equal}")
    print(f"所有角都是90度: {all_angles_90}")
    
    is_rect = opposite_sides_equal and diagonals_equal and all_angles_90
    print(f"是否为矩形: {is_rect}")
    
    return is_rect

if __name__ == "__main__":
    print("四边形1的坐标:")
    for i, point in enumerate(quad1):
        print(f"点{i+1}: [{point[0]:.8f}, {point[1]:.8f}, {point[2]:.8f}]")
    
    print("\n四边形2的坐标:")
    for i, point in enumerate(quad2):
        print(f"点{i+1}: [{point[0]:.8f}, {point[1]:.8f}, {point[2]:.8f}]")
    
    # 绘制四边形
    plot_quadrilaterals(quad1, quad2)
    
    # 判断第二个四边形是否为矩形
    is_rectangle(quad2)
