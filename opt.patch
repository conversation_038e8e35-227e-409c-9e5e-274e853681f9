diff --git a/lib/src/center_stage/czcv_center_stage.cpp b/lib/src/center_stage/czcv_center_stage.cpp
index c0a535e..53f08f1 100644
--- a/lib/src/center_stage/czcv_center_stage.cpp
+++ b/lib/src/center_stage/czcv_center_stage.cpp
@@ -3672,6 +3672,39 @@ namespace czcv_camera
             TrackerInputOutput tracks;
             fetch_tracks_with_gesture(tracks);
 
+            std::vector<BboxF> in_bbox = tracks.in_bbox();
+
+            for (auto & box: in_bbox)
+            {
+                box.xmin(box.xmin() * _down_scale);
+                box.xmax(box.xmax() * _down_scale);
+                box.ymin(box.ymin() * _down_scale);
+                box.ymax(box.ymax() * _down_scale);
+            }  
+            
+            tracks.in_bbox(in_bbox);
+
+            std::vector<BboxF> tracked_bbox = tracks.tracked_bbox();
+
+            for (auto & box: tracked_bbox)
+            {
+                box.xmin(box.xmin() * _down_scale);
+                box.xmax(box.xmax() * _down_scale);
+                box.ymin(box.ymin() * _down_scale);
+                box.ymax(box.ymax() * _down_scale);
+            }  
+            
+            tracks.tracked_bbox(tracked_bbox);
+
+            std::vector<stGestureRecResult>& gestureResults = tracks.gestureResults();
+            for (auto & result: gestureResults)
+            {
+                result.rectf.x0 *= _down_scale;
+                result.rectf.x1 *= _down_scale;
+                result.rectf.y0 *= _down_scale;
+                result.rectf.y1 *= _down_scale;
+            }
+
             //fetch_tracks(tracks);
             //std::vector<BboxF> windowsRaw = dets.bbox();
                                                                                                                                                            
@@ -4533,7 +4566,7 @@ namespace czcv_camera
                     {
                         if (idinfor->frame_count > _frame_interval_rejudge)
                         {
-                            idinfor->flag = _biClassificationPtr->run(rga_mat.phyaddr, crop_roi,bgr_biclass.cols,bgr_biclass.rows);
+                            idinfor->flag = true;  //_biClassificationPtr->run(rga_mat.phyaddr, crop_roi,bgr_biclass.cols,bgr_biclass.rows);
                             box.flag(idinfor->flag);
                             idinfor->frame_count = 0;
                         }
@@ -4541,7 +4574,7 @@ namespace czcv_camera
                 }
                 if (find(id_list.begin(), id_list.end(), box.instance_id()) == id_list.end())
                 {
-                    bool f = _biClassificationPtr->run(rga_mat.phyaddr, crop_roi,bgr_biclass.cols,bgr_biclass.rows);
+                    bool f = true;//_biClassificationPtr->run(rga_mat.phyaddr, crop_roi,bgr_biclass.cols,bgr_biclass.rows);
                     id_infor.push_back(id_biClassification_infor{ box.instance_id(), 0, f });
                     box.flag(f);
                 }
@@ -4611,8 +4644,15 @@ namespace czcv_camera
                 return;
             }
 
+            int down_scale = _down_scale;
+            int debug_mode = property_get_int32("vendor.czcv.camera.debug2", 0);
+            if (debug_mode)
+            {
+                down_scale = 1;
+            }
+
             czcv_camera::RgaMat rga_mat;
-            rga_mat.handle = _rgaops->malloc_rga(&rga_mat.viraddr,&rga_mat.phyaddr,_final_w, _final_h, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
+            rga_mat.handle = _rgaops->malloc_rga(&rga_mat.viraddr,&rga_mat.phyaddr,_final_w / down_scale, _final_h / down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
             if (rga_mat.handle == NULL)
             {   
                 LOGE("thread_loop_gesture rga_mat malloc failed\n");
@@ -4661,10 +4701,10 @@ namespace czcv_camera
                         cv::Mat bgr;
                         {
                             std::unique_lock<std::mutex> ulk(_muMatVec);                             
-                            int ret = _rgaops->cropScaleRgb(_rga_mat.phyaddr,rga_mat.phyaddr,_final_w,_final_h,0,0,_final_w,_final_h,_final_w,_final_h,0,0,_final_w,_final_h);		                           
+                            int ret = _rgaops->cropScaleRgb(_rga_mat.phyaddr,rga_mat.phyaddr,_final_w / down_scale,_final_h / down_scale,0,0,_final_w / down_scale,_final_h / down_scale,_final_w / down_scale,_final_h / down_scale,0,0,_final_w / down_scale,_final_h / down_scale);		                           
                         }
 
-                        bgr = cv::Mat(_final_h, _final_w, CV_8UC3, rga_mat.viraddr);
+                        bgr = cv::Mat(_final_h / down_scale, _final_w / down_scale, CV_8UC3, rga_mat.viraddr);
                         tracks.ref_frame_to(bgr);                       
                         tracks.input_phyadrr(rga_mat.phyaddr);
                       
@@ -4730,6 +4770,17 @@ namespace czcv_camera
 
             std::shared_ptr<Yolov10RKNN> _gestureRecognitiongPtr;
 
+            int down_scale = _down_scale;
+            if(false == primary)
+            {
+                down_scale = _down_scale_sub;
+            }
+            int debug_mode = property_get_int32("vendor.czcv.camera.debug2", 0);
+            if (debug_mode)
+            {
+                down_scale = 1;
+            }
+
             if (!_use_4k)
             {
                 Status s;
@@ -4832,8 +4883,8 @@ namespace czcv_camera
                     LOGE("class model init failed!\n");
                 }
 
-                rga_mat.handle = _rgaops->malloc_rga(&rga_mat.viraddr,&rga_mat.phyaddr,_final_w, _final_h, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
-                rgamat_clone.handle = _rgaops->malloc_rga(&rgamat_clone.viraddr,&rgamat_clone.phyaddr, _final_w, _final_h / 2, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
+                rga_mat.handle = _rgaops->malloc_rga(&rga_mat.viraddr,&rga_mat.phyaddr,_final_w / down_scale, _final_h / down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
+                rgamat_clone.handle = _rgaops->malloc_rga(&rgamat_clone.viraddr,&rgamat_clone.phyaddr, _final_w / down_scale, _final_h / 2 / down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
                 if (rga_mat.handle == NULL || rgamat_clone.handle == NULL)
                 {
                     LOGE("malloc_rga faild");
@@ -4845,7 +4896,7 @@ namespace czcv_camera
                 if (primary)
                 {
                     std::unique_lock<std::mutex> ulk(_muMatVec); 
-                    _rga_mat.handle = _rgaops->malloc_rga(&_rga_mat.viraddr,&_rga_mat.phyaddr,_final_w, _final_h, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);                
+                    _rga_mat.handle = _rgaops->malloc_rga(&_rga_mat.viraddr,&_rga_mat.phyaddr,_final_w / down_scale, _final_h / down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);                
                 }
                 
                 //_gestureRecognitiongPtr.reset(new czcv_camera::Yolov10RKNN());
@@ -4884,11 +4935,11 @@ namespace czcv_camera
 
                             if (primary)
                             {
-                                get_frame(rgamat_clone.phyaddr, phy_addr(), _rgaops, true);
+                                get_frame(rgamat_clone.phyaddr, phy_addr(), _rgaops, true, down_scale);
                             }
                             else
                             {
-                                get_frame(rgamat_clone.phyaddr, phy_addr_sub(), _rgaops, true);
+                                get_frame(rgamat_clone.phyaddr, phy_addr_sub(), _rgaops, true, down_scale);
                             }
                             
                             //cv::Mat frame_vir;
@@ -4921,12 +4972,12 @@ namespace czcv_camera
                             if (_bnv12 == true)
                             {
                                 dets.format(NV12_format);
-                                int ret = _rgaops->yuv2rgb(rgamat_clone.phyaddr,rga_mat.phyaddr,_final_w,_final_h,_final_w,_final_h);
+                                int ret = _rgaops->yuv2rgb(rgamat_clone.phyaddr,rga_mat.phyaddr,_final_w / down_scale,_final_h / down_scale,_final_w / down_scale,_final_h / down_scale);
                                 if (ret != 0)
                                 {
                                     LOGE("yuv2rgb faild");
                                 }
-                                bgr = cv::Mat(_final_h, _final_w, CV_8UC3, rga_mat.viraddr);
+                                bgr = cv::Mat(_final_h / down_scale, _final_w / down_scale, CV_8UC3, rga_mat.viraddr);
                                 //cvtColor(frame_vir, bgr_vir, cv::COLOR_YUV2BGR_NV12);
                             }
                             else
@@ -5094,7 +5145,7 @@ namespace czcv_camera
                             //int maxMS = 1000 / _maxFPS;
 
                             LOGI("==> [Async_Preview_Runner] fps:%d, primary: %d\n", ms, primary);
-                            std::this_thread::sleep_for(std::chrono::milliseconds(100));
+                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                         }
                         else
                         {
@@ -5714,7 +5765,10 @@ float _tmpdoa = 0;
         enZoomMoveState _zoom_move_state_sub = EN_ZOOM_MOVE_STATE_STOP;
 
         std::mutex _muMatVec;
-        czcv_camera::RgaMat _rga_mat;    
+        czcv_camera::RgaMat _rga_mat;
+
+        int _down_scale = 3;   
+        int _down_scale_sub = 12;
     };
 
     Status PersonCenterStager::start()
diff --git a/lib/src/detector/detail/rknn_yolox.cpp b/lib/src/detector/detail/rknn_yolox.cpp
index ab69716..2799f61 100644
--- a/lib/src/detector/detail/rknn_yolox.cpp
+++ b/lib/src/detector/detail/rknn_yolox.cpp
@@ -308,6 +308,8 @@ namespace czcv_camera
 		int frame_w = inputOutput.get_frame_w();
 		int frame_channels = inputOutput.get_frame_channels();
 
+		LOGE("YoloxRKNN::run: %d %d\n", frame_h, frame_w);
+
 		int img_height = frame_h;
 		if (frame_channels == 1)
 		{
diff --git a/lib/src/hand/rknn_yolov10.cpp b/lib/src/hand/rknn_yolov10.cpp
index bf9ff79..1b9dbb6 100644
--- a/lib/src/hand/rknn_yolov10.cpp
+++ b/lib/src/hand/rknn_yolov10.cpp
@@ -642,8 +642,12 @@ namespace czcv_camera
         return CZCV_OK;
 	}
 
+	bool save_flag = false;
+	int saveid = 0;
     Status Yolov10RKNN::run(TrackerInputOutput &inputOutput)
     {
+		czcv_camera::RgaMat rgamat_det_pad;
+		rgamat_det_pad.handle = _rgaInterfacePtr->malloc_rga(&rgamat_det_pad.viraddr, &rgamat_det_pad.phyaddr,_inputWidth, _inputHeight, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
 		inputOutput.gestureResults().clear();
 		cv::Mat bgr;
         inputOutput.ref_frame_to(bgr);
@@ -660,65 +664,157 @@ namespace czcv_camera
 		int img_width = frame_w;
 		
 		inputOutput.gestureResults().clear();
-	
+		
+		int person_num = 0;
 		for (auto box: inputOutput.in_bbox())
 		{
 			if (box.class_id() != 0)
 			{
 				continue;
 			}
+			person_num++;
+		}
 
-			box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
+		std::vector<DetectionResult> results_filtered;
 
-			std::vector<DetectionResult> results_filtered;
+		if (person_num > 0)
+		{
+			int row_num = ceil(sqrt(person_num / 2.5f));
+			int col_num = (person_num + row_num - 1) / row_num;
+			int dstw = _inputWidth / col_num;
+			int dsth = _inputHeight / row_num;
+			float ratio = (float)dstw / dsth;
+			int box_id = 0;
+			std::vector<float> scales;
+			int ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);		
+			ret = _rgaInterfacePtr->mat_fill(rgamat_det_pad.phyaddr,_inputWidth,_inputHeight,0,0,0);
 
-			if (_rga_flag)
+			//LOGE("Yolov10RKNN::run: person_num %d %d %d %d %d",person_num, row_num, col_num, dstw, dsth);	
+			for (size_t i = 0; i < inputOutput.in_bbox().size(); i++)
 			{
+				auto box = inputOutput.in_bbox()[i];
+				if (box.class_id() != 0)
+				{
+					continue;
+				}
+
+				box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
+				
+				int col_id = box_id % col_num;
+				int row_id = box_id / col_num;
+				int dstx = col_id * _inputWidth / col_num;
+				int dsty = row_id * _inputHeight / row_num;
+
 				int w = box.width();
 				int h = box.height();
 				float scale = 1.f;
-				if (w > h)
+				if (w > h * ratio)
 				{
-					scale = (float)_inputWidth / w;
-					w = _inputWidth;
+					scale = (float)dstw / w;
+					w = dstw;
 					h = h * scale;			
 				}
 				else
 				{
-					scale = (float)_inputHeight / h;
-					h = _inputHeight;
+					scale = (float)dsth / h;
+					h = dsth;
 					w = w * scale;
 				}
+				scales.push_back(scale);
+				box_id++;	
+				
+				ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,dstx,dsty,w,h);
+				ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),rgamat_det_pad.phyaddr,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,dstx,dsty,w,h);
+				//LOGE("Yolov10RKNN::run: box_id %d %d %d %d %d",box_id, dstx, dsty, w, h);
+			}
 
-				int ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);			
-				ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,0,0,w,h);
+			// saveid++;
+			// if (false == save_flag && saveid > 15 && scales.size() > 1)
+			// {
+			// 	cv::Mat debug = cv::Mat(_inputHeight, _inputWidth, CV_8UC3, rgamat_det_pad.viraddr);
+			// 	save_flag = true;
+			// 	cv::imwrite("/mnt/img/test.jpg", debug);
+			// }
 
-				run_sub_rga(scale, results_filtered);
-			}		
-			else
-			{
-				cv::Mat roiimg = bgr.colRange(box.xmin(), box.xmax() + 1).rowRange(box.ymin(), box.ymax() + 1).clone();		
-				run_sub(roiimg, results_filtered);
-			}		
-			
-			for (auto & result: results_filtered)
+			run_sub_rga(1.0f, results_filtered);
+
+			//LOGE("Yolov10RKNN::run: %d\n", results_filtered.size());
+			for (auto result: results_filtered)
 			{
-				float x0 = (std::max)((std::min)(result.rect.x0 + box.xmin(), (float)(img_width - 1)), 0.f);
-				float y0 = (std::max)((std::min)(result.rect.y0 + box.ymin(), (float)(img_height - 1)), 0.f);
-				float x1 = (std::max)((std::min)(result.rect.x1 + box.xmin(), (float)(img_width - 1)), 0.f);
-				float y1 = (std::max)((std::min)(result.rect.y1 + box.ymin(), (float)(img_height - 1)), 0.f);
-
-				stGestureRecResult recresult;
-				recresult.rectf.x0 = x0;
-				recresult.rectf.y0 = y0;
-				recresult.rectf.x1 = x1;
-				recresult.rectf.y1 = y1;
-				recresult.palm_score = result.score;
-				recresult.clsid = result.label;
-				recresult.det_instance_id = box.instance_id();
-				inputOutput.gestureResults().push_back(recresult);
+				LOGD("Yolov10RKNN::run result: %f, %f, %f, %f, %d\n", result.rect.x0, result.rect.y0, result.rect.x1, result.rect.y1, result.label);
 			}
 		}
+		
+		if (rgamat_det_pad.handle != NULL)
+		{
+			int ret = _rgaInterfacePtr->free(rgamat_det_pad.handle);
+			rgamat_det_pad.handle = nullptr;
+		} 
+		
+		// for (auto box: inputOutput.in_bbox())
+		// {
+		// 	if (box.class_id() != 0)
+		// 	{
+		// 		continue;
+		// 	}
+
+		// 	box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
+
+		// 	std::vector<DetectionResult> results_filtered;
+
+		// 	if (_rga_flag)
+		// 	{
+		// 		int w = box.width();
+		// 		int h = box.height();
+		// 		float scale = 1.f;
+		// 		if (w > h)
+		// 		{
+		// 			scale = (float)_inputWidth / w;
+		// 			w = _inputWidth;
+		// 			h = h * scale;			
+		// 		}
+		// 		else
+		// 		{
+		// 			scale = (float)_inputHeight / h;
+		// 			h = _inputHeight;
+		// 			w = w * scale;
+		// 		}
+
+		// 		int ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);			
+		// 		ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,0,0,w,h);
+
+		// 		run_sub_rga(scale, results_filtered);
+		// 	}		
+		// 	else
+		// 	{
+		// 		cv::Mat roiimg = bgr.colRange(box.xmin(), box.xmax() + 1).rowRange(box.ymin(), box.ymax() + 1).clone();		
+		// 		run_sub(roiimg, results_filtered);
+		// 	}		
+			
+		// 	for (auto & result: results_filtered)
+		// 	{
+		// 		float x0 = (std::max)((std::min)(result.rect.x0 + box.xmin(), (float)(img_width - 1)), 0.f);
+		// 		float y0 = (std::max)((std::min)(result.rect.y0 + box.ymin(), (float)(img_height - 1)), 0.f);
+		// 		float x1 = (std::max)((std::min)(result.rect.x1 + box.xmin(), (float)(img_width - 1)), 0.f);
+		// 		float y1 = (std::max)((std::min)(result.rect.y1 + box.ymin(), (float)(img_height - 1)), 0.f);
+
+		// 		stGestureRecResult recresult;
+		// 		recresult.rectf.x0 = x0;
+		// 		recresult.rectf.y0 = y0;
+		// 		recresult.rectf.x1 = x1;
+		// 		recresult.rectf.y1 = y1;
+		// 		recresult.palm_score = result.score;
+		// 		recresult.clsid = result.label;
+		// 		recresult.det_instance_id = box.instance_id();
+		// 		inputOutput.gestureResults().push_back(recresult);
+		// 	}
+		// }
+
+		// LOGE("Yolov10RKNN::run: %d\n", inputOutput.gestureResults().size());
+		// for (auto result: inputOutput.gestureResults())
+		// {
+		// 	LOGD("Yolov10RKNN::run result: %f, %f, %f, %f, %d\n", result.rectf.x0, result.rectf.y0, result.rectf.x1, result.rectf.y1, result.clsid);
+		// }
 
 		return CZCV_OK;		
     }
diff --git a/lib/src/utils/async_runner.h b/lib/src/utils/async_runner.h
index dd8fb23..e15bee5 100644
--- a/lib/src/utils/async_runner.h
+++ b/lib/src/utils/async_runner.h
@@ -322,11 +322,11 @@ namespace czcv_camera
             return  _event;
         }
 
-        void get_frame(int &ret_phyaddr, int _frame_phyaddr, std::shared_ptr<rga_interface_t> &rgaInterfacePtr,bool withCopy = false)
+        void get_frame(int &ret_phyaddr, int _frame_phyaddr, std::shared_ptr<rga_interface_t> &rgaInterfacePtr,bool withCopy = false, int down_scale=1)
         {
             if(withCopy)
             {
-                int ret = rgaInterfacePtr->cropScale(_frame_phyaddr, ret_phyaddr, _frame.cols, _frame.rows * 2 / 3, 0, 0, _frame.cols, _frame.rows * 2 / 3, _frame.cols, _frame.rows * 2 / 3);
+                int ret = rgaInterfacePtr->cropScale(_frame_phyaddr, ret_phyaddr, _frame.cols, _frame.rows * 2 / 3, 0, 0, _frame.cols, _frame.rows * 2 / 3, _frame.cols / down_scale, _frame.rows * 2 / 3 / down_scale);
                 if(ret != 0)
                 {
                     LOGE("get_frame rga cropScale failed!\n");
