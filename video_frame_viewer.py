#!/usr/bin/env python
"""
Video Frame Viewer - 使用OpenCV逐帧查看MP4视频文件

使用方法: python video_frame_viewer.py <video_path> [--start_frame <frame_number>]

功能特性:
- 读取MP4视频文件并逐帧显示
- 按空格键切换到下一帧
- 可以设置从指定帧开始显示
- 显示当前帧号和总帧数
- 按ESC键退出程序

操作说明:
- 按 SPACE 键切换到下一帧
- 按 ESC 键退出程序
- 按 'r' 键重置到起始帧
- 按 'j' 键跳转到指定帧号
"""

import cv2 as cv
import os
import sys
import argparse


class VideoFrameViewer:
    def __init__(self, video_path, start_frame=0):
        self.video_path = video_path
        self.start_frame = start_frame
        self.current_frame = start_frame
        self.cap = None
        self.total_frames = 0
        
    def initialize_video(self):
        """初始化视频捕获对象"""
        if not os.path.exists(self.video_path):
            print(f"错误: 视频文件不存在: {self.video_path}")
            return False
            
        self.cap = cv.VideoCapture(self.video_path)
        
        if not self.cap.isOpened():
            print(f"错误: 无法打开视频文件: {self.video_path}")
            return False
            
        # 获取视频信息
        self.total_frames = int(self.cap.get(cv.CAP_PROP_FRAME_COUNT))
        fps = self.cap.get(cv.CAP_PROP_FPS)
        width = int(self.cap.get(cv.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv.CAP_PROP_FRAME_HEIGHT))
        
        print(f"视频信息:")
        print(f"  文件路径: {self.video_path}")
        print(f"  总帧数: {self.total_frames}")
        print(f"  帧率: {fps:.2f} FPS")
        print(f"  分辨率: {width}x{height}")
        print(f"  起始帧: {self.start_frame}")
        
        # 检查起始帧是否有效
        if self.start_frame >= self.total_frames:
            print(f"警告: 起始帧 {self.start_frame} 超出视频总帧数 {self.total_frames}")
            self.start_frame = 0
            self.current_frame = 0
            
        # 跳转到起始帧
        self.cap.set(cv.CAP_PROP_POS_FRAMES, self.start_frame)
        self.current_frame = self.start_frame
        
        return True
        
    def get_current_frame(self):
        """获取当前帧的图像"""
        ret, frame = self.cap.read()
        if ret:
            return frame
        else:
            return None
            
    def jump_to_frame(self, frame_number):
        """跳转到指定帧"""
        if 0 <= frame_number < self.total_frames:
            self.cap.set(cv.CAP_PROP_POS_FRAMES, frame_number)
            self.current_frame = frame_number
            return True
        else:
            print(f"错误: 帧号 {frame_number} 超出范围 [0, {self.total_frames-1}]")
            return False
            
    def next_frame(self):
        """切换到下一帧"""
        if self.current_frame < self.total_frames - 1:
            self.current_frame += 1
            return True
        else:
            print("已经是最后一帧")
            return False
            
    def reset_to_start(self):
        """重置到起始帧"""
        self.jump_to_frame(self.start_frame)
        
    def add_frame_info(self, frame):
        """在帧上添加帧号信息"""
        if frame is None:
            return None
            
        # 创建副本以避免修改原始帧
        display_frame = frame.copy()
        
        # 添加帧号信息
        info_text = f"Frame: {self.current_frame}/{self.total_frames-1}"
        
        # 设置文本属性
        font = cv.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0
        color = (0, 255, 0)  # 绿色
        thickness = 2
        
        # 获取文本大小
        text_size = cv.getTextSize(info_text, font, font_scale, thickness)[0]
        
        # 在左上角添加黑色背景
        cv.rectangle(display_frame, (10, 10), 
                    (20 + text_size[0], 40 + text_size[1]), 
                    (0, 0, 0), -1)
        
        # 添加文本
        cv.putText(display_frame, info_text, (15, 35), 
                  font, font_scale, color, thickness)
        
        return display_frame
        
    def run(self):
        """运行视频帧查看器"""
        if not self.initialize_video():
            return
            
        # 创建窗口
        cv.namedWindow("Video Frame Viewer", cv.WINDOW_NORMAL)
        
        print("\n操作说明:")
        print("  SPACE - 下一帧")
        print("  ESC   - 退出")
        print("  r     - 重置到起始帧")
        print("  j     - 跳转到指定帧")
        print()
        
        while True:
            # 获取当前帧
            frame = self.get_current_frame()
            
            if frame is None:
                print("无法读取帧或已到达视频末尾")
                break
                
            # 添加帧信息并显示
            display_frame = self.add_frame_info(frame)
            cv.imshow("Video Frame Viewer", display_frame)
            
            # 等待按键
            key = cv.waitKey(0) & 0xFF
            
            if key == 27:  # ESC键退出
                print("退出程序")
                break
            elif key == 32:  # SPACE键下一帧
                if not self.next_frame():
                    print("已到达视频末尾，按ESC退出或按r重置")
            elif key == ord('r'):  # r键重置
                self.reset_to_start()
                print(f"重置到起始帧: {self.start_frame}")
            elif key == ord('j'):  # j键跳转
                try:
                    frame_num = int(input("请输入要跳转的帧号: "))
                    if self.jump_to_frame(frame_num):
                        print(f"跳转到帧: {frame_num}")
                except ValueError:
                    print("请输入有效的帧号")
                except KeyboardInterrupt:
                    print("\n取消跳转")
        
        # 清理资源
        self.cap.release()
        cv.destroyAllWindows()


def main():
    parser = argparse.ArgumentParser(description='逐帧查看MP4视频文件')
    parser.add_argument('video_path', help='MP4视频文件路径')
    parser.add_argument('--start_frame', type=int, default=0, 
                       help='起始帧号 (默认: 0)')
    
    args = parser.parse_args()
    
    # 检查文件扩展名
    if not args.video_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
        print("警告: 文件可能不是支持的视频格式")
    
    viewer = VideoFrameViewer(args.video_path, args.start_frame)
    viewer.run()


if __name__ == '__main__':
    print(__doc__)
    main()
