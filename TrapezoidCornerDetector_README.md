# TrapezoidCornerDetector

A C++ class using OpenCV for detecting 4 white trapezoid contours arranged in a 2x2 grid and extracting their corner points.

## Overview

The `TrapezoidCornerDetector` class is designed to:
- Detect exactly 4 white trapezoid contours in an image
- Arrange them in a 2x2 grid pattern (top-left, top-right, bottom-left, bottom-right)
- Extract the outer corner points:
  - Top-left corner of the top-left trapezoid
  - Top-right corner of the top-right trapezoid
  - Bottom-left corner of the bottom-left trapezoid
  - Bottom-right corner of the bottom-right trapezoid

## Features

- **Robust Detection**: Uses contour detection with area filtering and shape validation
- **Flexible Parameters**: Configurable detection parameters for different image conditions
- **Grid Arrangement**: Automatically arranges detected trapezoids in 2x2 grid based on position
- **Corner Extraction**: Precisely extracts the outer corner points of the grid
- **Visualization**: Built-in drawing functions to visualize detection results
- **Error Handling**: Comprehensive validation and error checking

## Requirements

- OpenCV 4.x or later
- C++11 or later

## File Structure

```
trapezoid_corner_detector.hpp    # Header file with class declaration
trapezoid_corner_detector.cpp    # Implementation file
trapezoid_corner_detector_example.cpp  # Usage example
```

## Usage

### Basic Usage

```cpp
#include "trapezoid_corner_detector.hpp"

// Create detector with default parameters
TrapezoidCornerDetector detector;

// Load image
cv::Mat image = cv::imread("trapezoids.jpg");

// Detect trapezoids and extract corners
if (detector.detectTrapezoidCorners(image)) {
    // Get corner points
    const TrapezoidGridCorners& corners = detector.getGridCorners();
    
    std::cout << "Top-Left: (" << corners.topLeft.x << ", " << corners.topLeft.y << ")" << std::endl;
    std::cout << "Top-Right: (" << corners.topRight.x << ", " << corners.topRight.y << ")" << std::endl;
    std::cout << "Bottom-Left: (" << corners.bottomLeft.x << ", " << corners.bottomLeft.y << ")" << std::endl;
    std::cout << "Bottom-Right: (" << corners.bottomRight.x << ", " << corners.bottomRight.y << ")" << std::endl;
    
    // Visualize results
    cv::Mat result = detector.drawResults(image);
    cv::imshow("Results", result);
    cv::waitKey(0);
}
```

### Constructor Parameters

```cpp
TrapezoidCornerDetector(double minArea = 100.0, 
                       double maxArea = 50000.0,
                       double approxEpsilon = 0.02,
                       double cannyLow = 50.0,
                       double cannyHigh = 150.0);
```

- `minArea`: Minimum area for trapezoid detection (pixels²)
- `maxArea`: Maximum area for trapezoid detection (pixels²)
- `approxEpsilon`: Epsilon for contour approximation (0.01-0.05 recommended)
- `cannyLow`: Lower threshold for Canny edge detection (not currently used)
- `cannyHigh`: Upper threshold for Canny edge detection (not currently used)

### Main Methods

#### Detection
```cpp
bool detectTrapezoidCorners(const cv::Mat& image);
```
Returns `true` if exactly 4 trapezoids were detected and corners extracted successfully.

#### Get Results
```cpp
const TrapezoidGridCorners& getGridCorners() const;
const std::vector<DetectedTrapezoid>& getDetectedTrapezoids() const;
bool isDetectionValid() const;
```

#### Visualization
```cpp
cv::Mat drawResults(const cv::Mat& image, bool drawTrapezoids = true, bool drawCorners = true) const;
```

#### Utility
```cpp
void reset();  // Clear previous detection results
```

## Data Structures

### TrapezoidGridCorners
```cpp
struct TrapezoidGridCorners {
    cv::Point2f topLeft;      // Top-left corner of top-left trapezoid
    cv::Point2f topRight;     // Top-right corner of top-right trapezoid
    cv::Point2f bottomLeft;   // Bottom-left corner of bottom-left trapezoid
    cv::Point2f bottomRight;  // Bottom-right corner of bottom-right trapezoid
    bool isValid;             // Flag indicating if corners are valid
};
```

### DetectedTrapezoid
```cpp
struct DetectedTrapezoid {
    std::vector<cv::Point2f> corners;  // Four corner points of the trapezoid
    cv::Point2f center;                // Center point of the trapezoid
    double area;                       // Area of the trapezoid
};
```

## Algorithm Overview

1. **Preprocessing**: Convert to grayscale, apply Gaussian blur, threshold, and morphological operations
2. **Contour Detection**: Find external contours using `cv::findContours`
3. **Filtering**: Filter contours by area and validate as 4-vertex convex shapes
4. **Grid Arrangement**: Sort trapezoids by position into 2x2 grid
5. **Corner Extraction**: Extract specific corner points from each trapezoid based on position

## Image Requirements

- Image should contain exactly 4 white trapezoid shapes
- Trapezoids should be arranged in a 2x2 grid pattern
- Good contrast between trapezoids and background
- Trapezoids should be clearly separated (not touching)

## Compilation Example

```bash
g++ -std=c++11 trapezoid_corner_detector.cpp trapezoid_corner_detector_example.cpp -o trapezoid_detector `pkg-config --cflags --libs opencv4`
```

## Troubleshooting

- **No trapezoids detected**: Adjust `minArea` and `maxArea` parameters
- **Wrong number of trapezoids**: Check image quality and contour filtering parameters
- **Incorrect corner extraction**: Verify trapezoid arrangement and adjust `approxEpsilon`
- **Poor detection**: Improve image preprocessing or lighting conditions

## Differences from SquareCornerDetector

- Relaxed angle constraints (trapezoids don't require 90-degree angles)
- Adapted corner finding logic for trapezoid shapes
- Modified validation criteria for quadrilateral shapes
- Enhanced robustness for non-rectangular shapes
