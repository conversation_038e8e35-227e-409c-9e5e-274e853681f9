#!/usr/bin/env python
"""
图像查重工具 - 使用感知哈希算法检测并过滤重复图像

使用方法: python image_deduplicator.py <source_directory> [--threshold 5]

功能特性:
- 支持常见图像格式 (jpg, jpeg, png, bmp, tiff)
- 使用感知哈希算法检测相似图像
- 可调节相似度阈值
- 自动创建输出目录
- 显示处理进度和统计信息
"""

import cv2 as cv
import numpy as np
import os
import sys
import glob
import argparse
import shutil
from pathlib import Path

class ImageDeduplicator:
    def __init__(self, threshold=5):
        self.threshold = threshold
        self.processed_hashes = {}
        self.duplicate_count = 0
        
    def calculate_phash(self, image_path):
        """计算图像的感知哈希值"""
        try:
            # 读取图像
            img = cv.imread(image_path, cv.IMREAD_GRAYSCALE)
            if img is None:
                return None
                
            # 缩放到8x8
            img_resized = cv.resize(img, (8, 8))
            
            # 计算DCT
            img_float = np.float32(img_resized)
            dct = cv.dct(img_float)
            
            # 取左上角8x8区域
            dct_low = dct[:8, :8]
            
            # 计算平均值（除去DC分量）
            avg = np.mean(dct_low[1:])
            
            # 生成哈希
            hash_bits = dct_low > avg
            return hash_bits.flatten()
            
        except Exception as e:
            print(f"处理图像 {image_path} 时出错: {e}")
            return None
    
    def hamming_distance(self, hash1, hash2):
        """计算两个哈希值的汉明距离"""
        return np.sum(hash1 != hash2)
    
    def is_duplicate(self, image_hash, image_path):
        """检查图像是否为重复"""
        for stored_path, stored_hash in self.processed_hashes.items():
            distance = self.hamming_distance(image_hash, stored_hash)
            if distance <= self.threshold:
                print(f"发现重复图像: {image_path} (与 {stored_path} 相似度: {64-distance}/64)")
                return True
        return False
    
    def process_directory(self, source_dir, dst_dir="./dst"):
        """处理目录中的所有图像"""
        # 创建输出目录
        Path(dst_dir).mkdir(exist_ok=True)
        
        # 支持的图像格式
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF']
        
        # 获取所有图像文件
        image_files = []
        for ext in extensions:
            image_files.extend(glob.glob(os.path.join(source_dir, ext)))

        #image_files = [fn for fn in image_files if "roi_" in fn]
        
        if not image_files:
            print(f"在目录 {source_dir} 中未找到图像文件")
            return
        
        print(f"找到 {len(image_files)} 个图像文件")
        print(f"相似度阈值: {self.threshold}/64")
        print("开始处理...")
        
        copied_count = 0
        
        for i, image_path in enumerate(image_files):
            print(f"处理进度: {i+1}/{len(image_files)} - {os.path.basename(image_path)}")
            
            # 计算图像哈希
            image_hash = self.calculate_phash(image_path)
            if image_hash is None:
                continue
            
            # 检查是否重复
            if not self.is_duplicate(image_hash, image_path):
                # 不是重复图像，复制到目标目录
                filename = os.path.basename(image_path)
                dst_path = os.path.join(dst_dir, filename)
                
                # 处理文件名冲突
                counter = 1
                base_name, ext = os.path.splitext(filename)
                while os.path.exists(dst_path):
                    new_filename = f"{base_name}_{counter}{ext}"
                    dst_path = os.path.join(dst_dir, new_filename)
                    counter += 1
                
                shutil.copy2(image_path, dst_path)
                self.processed_hashes[image_path] = image_hash
                copied_count += 1
                print(f"  -> 已复制到: {dst_path}")
            else:
                self.duplicate_count += 1
        
        print(f"\n处理完成!")
        print(f"原始图像: {len(image_files)} 个")
        print(f"复制图像: {copied_count} 个")
        print(f"重复图像: {self.duplicate_count} 个")
        print(f"输出目录: {os.path.abspath(dst_dir)}")

def main():
    parser = argparse.ArgumentParser(description='图像查重工具')
    parser.add_argument('source_directory', help='源图像目录路径')
    parser.add_argument('--threshold', type=int, default=10, 
                       help='相似度阈值 (0-64, 值越小越严格, 默认: 25)')
    parser.add_argument('--dst', default='./dst', 
                       help='输出目录路径 (默认: ./dst)')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.source_directory):
        print(f"错误: {args.source_directory} 不是有效的目录")
        sys.exit(1)
    
    if args.threshold < 0 or args.threshold > 64:
        print("错误: 阈值必须在 0-64 之间")
        sys.exit(1)
    
    deduplicator = ImageDeduplicator(threshold=args.threshold)
    deduplicator.process_directory(args.source_directory, os.path.join(args.source_directory,args.dst))

if __name__ == '__main__':
    print(__doc__)
    main()

    # srcdir = r"D:\lib64\gesture\imgs"
    # dstdir = os.path.join(srcdir, "dst")
    # deduplicator = ImageDeduplicator(threshold=10)
    
    # # 获取所有子目录
    # subdirs = [x[0] for x in os.walk(srcdir)]
    
    # # 遍历每个子目录
    # counter = 1
    # for subdir in subdirs:
    #     # 获取所有图像文件
    #     image_files = []
    #     for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF']:
    #         image_files.extend(glob.glob(os.path.join(subdir, ext)))
            
    #     # 创建目标目录
    #     Path(dstdir).mkdir(exist_ok=True)
        
    #     # 复制并重命名文件
    #     for image_path in image_files:
    #         _, ext = os.path.splitext(image_path)
    #         dst_path = os.path.join(dstdir, f"{counter:04d}{ext}")
    #         shutil.copy2(image_path, dst_path)
    #         counter += 1
