import cv2
import numpy as np

def find_homography(src_points, dst_points, method=cv2.RANSAC, ransacReprojThreshold=3.0):
    """
    计算两组点之间的单应性矩阵（Homography Matrix）
    
    参数:
        src_points (np.ndarray): 源图像中的点坐标，形状为 (N, 1, 2) 或 (N, 2)
        dst_points (np.ndarray): 目标图像中的点坐标，形状同上
        method (int): 估计方法 
                      cv2.RANSAC (默认), cv2.LMEDS, cv2.RHO
        ransacReprojThreshold (float): RANSAC 的重投影阈值（像素单位）
    
    返回:
        H (np.ndarray): 3x3 单应性矩阵
        mask (np.ndarray): 二进制掩码，1 表示内点(inlier)，0 表示外点(outlier)
    """
    # 确保输入点格式正确 (N, 1, 2)
    if src_points.ndim == 2:
        src_points = src_points.reshape(-1, 1, 2)
    if dst_points.ndim == 2:
        dst_points = dst_points.reshape(-1, 1, 2)
    
    # 检查点数是否足够
    if len(src_points) < 4:
        raise ValueError("At least 4 point correspondences are required")
    
    # 调用 OpenCV 的 findHomography
    H, mask = cv2.findHomography(
        src_points, 
        dst_points, 
        method=method,
        ransacReprojThreshold=ransacReprojThreshold
    )
    
    return H, mask

# ======================
# 使用示例
# ======================

if __name__ == "__main__":
    # 1. 准备匹配点 (至少4对)
    # 格式: [[x1, y1], [x2, y2], ...]
    src_pts = np.array([
        [1865,719],
        [2075,728],
        [1858,868],
        [2067,876],
        [2267,890],
        [2257, 1033],
        [2412, 1028], 
        [2392,825] # 多余的点用于演示 RANSAC
    ], dtype=np.float32)

    # 目标点 (模拟一个平移+旋转的变换)
    dst_pts = np.array([
        [2166,367],
        [2730,395],
        [2149,767],
        [2708,790],
        [3234,820],
        [3205, 1204],
        [3606, 1188], 
        [3543, 655]  # 故意添加一个错误匹配点
    ], dtype=np.float32)

    # 2. 计算单应性矩阵
    H, mask = find_homography(
        src_pts, 
        dst_pts,
        method=cv2.RANSAC,
        ransacReprojThreshold=5.0  # 重投影阈值设为5像素
    )

    # 3. 分析结果
    print("Homography Matrix (3x3):")
    print(H)
    
    print("\nInlier Mask (1=inlier, 0=outlier):")
    print(mask.ravel())  # 将二维掩码展平为一维
    
    # 4. 验证变换 (用前4个点验证)
    pts_src = np.array([[2391, 937], [1633, 703], [1096, 789], [1337, 846]], dtype=np.float32)
    dst_pts = np.array([[3538, 952], [1503, 314], [73, 549], [704, 701]], dtype=np.float32)
    pts_dst_transformed = cv2.perspectiveTransform(pts_src.reshape(1, -1, 2), H).squeeze()
    
    print("\nOriginal Points vs Transformed Points:")
    for i, (src, dst_trans) in enumerate(zip(pts_src, pts_dst_transformed)):
        print(f"Point {i+1}: {src} -> {dst_trans.round(1)} (Actual: {dst_pts[i]})")