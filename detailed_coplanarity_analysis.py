import numpy as np

# 定义8个3维点
all_points = np.array([
    # 四边形1的4个点
    [-663.40788856, -416.93399977, 1241.72878607],
    [817.47550169, -512.12748461, 1448.39459962],
    [911.65503184, 392.34151613, 1322.65532539],
    [-727.93803401, 316.05002203, 1121.55349277],
    # 四边形2的4个点
    [-371.96439105, -306.48398539, 1262.69683101],
    [440.2381145, -358.69362418, 1376.04437253],
    [494.75465923, 289.95606478, 1284.17905177],
    [-317.44784632, 342.16570357, 1170.83151024]
])

quad1 = all_points[:4]
quad2 = all_points[4:]

def check_quadrilateral_coplanarity(points, name):
    """检查单个四边形的4个点是否共面"""
    print(f"\n=== {name} 共面性分析 ===")
    
    # 方法1: 使用前3个点定义平面，检查第4个点
    p1, p2, p3, p4 = points
    
    # 计算平面法向量
    v1 = p2 - p1
    v2 = p3 - p1
    normal = np.cross(v1, v2)
    
    if np.linalg.norm(normal) == 0:
        print("前三个点共线，无法定义平面")
        return False
    
    normal = normal / np.linalg.norm(normal)
    
    # 计算第4个点到平面的距离
    d = -np.dot(normal, p1)
    distance_p4 = abs(np.dot(normal, p4) + d)
    
    print(f"平面法向量: [{normal[0]:.6f}, {normal[1]:.6f}, {normal[2]:.6f}]")
    print(f"第4个点到平面的距离: {distance_p4:.10f}")
    
    # 方法2: 计算体积
    matrix = np.array([p2-p1, p3-p1, p4-p1])
    volume = abs(np.linalg.det(matrix)) / 6
    print(f"四面体体积: {volume:.10f}")
    
    # 判断共面性
    tolerance = 1e-8
    is_coplanar = distance_p4 < tolerance and volume < tolerance
    
    print(f"是否共面: {'是' if is_coplanar else '否'}")
    
    return is_coplanar

def calculate_plane_angle(quad1, quad2):
    """计算两个四边形所在平面的夹角"""
    print(f"\n=== 两个平面的夹角分析 ===")
    
    # 计算四边形1的平面法向量
    p1, p2, p3 = quad1[:3]
    v1 = p2 - p1
    v2 = p3 - p1
    normal1 = np.cross(v1, v2)
    normal1 = normal1 / np.linalg.norm(normal1)
    
    # 计算四边形2的平面法向量
    p1, p2, p3 = quad2[:3]
    v1 = p2 - p1
    v2 = p3 - p1
    normal2 = np.cross(v1, v2)
    normal2 = normal2 / np.linalg.norm(normal2)
    
    # 计算两个法向量的夹角
    cos_angle = np.dot(normal1, normal2)
    cos_angle = np.clip(cos_angle, -1.0, 1.0)
    angle_rad = np.arccos(abs(cos_angle))  # 使用绝对值确保锐角
    angle_deg = np.degrees(angle_rad)
    
    print(f"四边形1平面法向量: [{normal1[0]:.6f}, {normal1[1]:.6f}, {normal1[2]:.6f}]")
    print(f"四边形2平面法向量: [{normal2[0]:.6f}, {normal2[1]:.6f}, {normal2[2]:.6f}]")
    print(f"两平面夹角: {angle_deg:.2f}度")
    
    return angle_deg

def calculate_plane_distance(quad1, quad2):
    """计算两个平面之间的距离"""
    print(f"\n=== 两个平面的距离分析 ===")
    
    # 使用四边形1定义平面
    p1, p2, p3 = quad1[:3]
    v1 = p2 - p1
    v2 = p3 - p1
    normal = np.cross(v1, v2)
    normal = normal / np.linalg.norm(normal)
    
    # 平面方程: normal · (x - p1) = 0
    d = -np.dot(normal, p1)
    
    # 计算四边形2各点到四边形1平面的距离
    distances = []
    for i, point in enumerate(quad2):
        distance = abs(np.dot(normal, point) + d)
        distances.append(distance)
        print(f"四边形2的点{i+1} 到四边形1平面的距离: {distance:.6f}")
    
    avg_distance = np.mean(distances)
    max_distance = max(distances)
    min_distance = min(distances)
    
    print(f"平均距离: {avg_distance:.6f}")
    print(f"最大距离: {max_distance:.6f}")
    print(f"最小距离: {min_distance:.6f}")
    
    return avg_distance, max_distance, min_distance

if __name__ == "__main__":
    print("详细共面性分析")
    print("=" * 50)
    
    # 检查每个四边形内部的共面性
    coplanar1 = check_quadrilateral_coplanarity(quad1, "四边形1")
    coplanar2 = check_quadrilateral_coplanarity(quad2, "四边形2")
    
    # 计算两个平面的夹角
    angle = calculate_plane_angle(quad1, quad2)
    
    # 计算两个平面的距离
    avg_dist, max_dist, min_dist = calculate_plane_distance(quad1, quad2)
    
    print(f"\n" + "=" * 50)
    print("总结:")
    print(f"四边形1内部共面: {'是' if coplanar1 else '否'}")
    print(f"四边形2内部共面: {'是' if coplanar2 else '否'}")
    print(f"两个平面的夹角: {angle:.2f}度")
    print(f"两个平面的平均距离: {avg_dist:.6f}")
    
    if angle < 1.0:  # 如果夹角小于1度，认为平行
        print("两个平面几乎平行")
        if max_dist < 1e-6:
            print("两个平面几乎重合，8个点接近共面")
        else:
            print("两个平面平行但不重合，8个点不共面")
    else:
        print("两个平面不平行，8个点不共面")
    
    print(f"\n最终结论: 这8个3维点 {'共面' if (coplanar1 and coplanar2 and angle < 1.0 and max_dist < 1e-6) else '不共面'}")
