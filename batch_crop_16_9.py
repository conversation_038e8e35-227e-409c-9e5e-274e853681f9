#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理图像裁剪为16:9比例并转换YOLO标注的脚本
实现以下功能：
1. 批量处理指定目录下的所有图像文件
2. 将图像裁剪为16:9比例（高度上保留中间部分）
3. 同时转换对应的YOLO标注文件，调整标注坐标到裁剪后的图像
4. 保存裁剪后的图像和转换后的标注文件
"""

import cv2
import numpy as np
import os
import sys
import glob
from pathlib import Path

def parse_yolo_detection(line):
    """
    解析YOLO检测结果的一行
    格式: class_id center_x center_y width height
    返回: (class_id, center_x, center_y, width, height)
    """
    parts = line.strip().split()
    if len(parts) != 5:
        return None
    
    class_id = int(parts[0])
    center_x = float(parts[1])
    center_y = float(parts[2])
    width = float(parts[3])
    height = float(parts[4])
    
    return class_id, center_x, center_y, width, height

def yolo_to_bbox(center_x, center_y, width, height, img_width, img_height):
    """
    将YOLO格式的归一化坐标转换为边界框坐标
    """
    # 转换为像素坐标
    center_x_px = center_x * img_width
    center_y_px = center_y * img_height
    width_px = width * img_width
    height_px = height * img_height
    
    # 计算边界框的左上角和右下角坐标
    x1 = int(center_x_px - width_px / 2)
    y1 = int(center_y_px - height_px / 2)
    x2 = int(center_x_px + width_px / 2)
    y2 = int(center_y_px + height_px / 2)
    
    return x1, y1, x2, y2

def bbox_to_yolo(x1, y1, x2, y2, img_width, img_height):
    """
    将边界框坐标转换为YOLO格式的归一化坐标
    """
    center_x = (x1 + x2) / 2.0 / img_width
    center_y = (y1 + y2) / 2.0 / img_height
    width = (x2 - x1) / img_width
    height = (y2 - y1) / img_height
    
    return center_x, center_y, width, height

def calculate_16_9_crop_region(img_width, img_height):
    """
    计算16:9裁剪区域，高度上保留中间部分
    返回: (crop_x1, crop_y1, crop_x2, crop_y2, new_width, new_height)
    """
    target_ratio = 16.0 / 9.0
    current_ratio = img_width / img_height
    
    if current_ratio > target_ratio:
        # 当前图像太宽，需要裁剪宽度（左右裁剪）
        new_height = img_height
        new_width = int(img_height * target_ratio)
        
        # 从中间开始裁剪
        crop_x1 = (img_width - new_width) // 2
        crop_y1 = 0
        crop_x2 = crop_x1 + new_width
        crop_y2 = img_height
    else:
        # 当前图像太高，需要裁剪高度（上下裁剪，保留中间部分）
        new_width = img_width
        new_height = int(img_width / target_ratio)
        
        # 从中间开始裁剪
        crop_x1 = 0
        crop_y1 = (img_height - new_height) // 2
        crop_x2 = img_width
        crop_y2 = crop_y1 + new_height
    
    return crop_x1, crop_y1, crop_x2, crop_y2, new_width, new_height

def transform_yolo_annotations(annotations, crop_x1, crop_y1, crop_x2, crop_y2, 
                              orig_width, orig_height, new_width, new_height):
    """
    转换YOLO标注到裁剪后的图像坐标系
    """
    transformed_annotations = []
    
    for annotation in annotations:
        class_id, center_x, center_y, width, height = annotation
        
        # 转换为原图像的像素坐标
        x1, y1, x2, y2 = yolo_to_bbox(center_x, center_y, width, height, orig_width, orig_height)
        
        # 检查边界框是否与裁剪区域有交集
        if x2 < crop_x1 or x1 > crop_x2 or y2 < crop_y1 or y1 > crop_y2:
            # 没有交集，跳过这个标注
            continue
        
        # 计算交集区域
        intersect_x1 = max(x1, crop_x1)
        intersect_y1 = max(y1, crop_y1)
        intersect_x2 = min(x2, crop_x2)
        intersect_y2 = min(y2, crop_y2)
        
        # 转换到裁剪后的坐标系
        new_x1 = intersect_x1 - crop_x1
        new_y1 = intersect_y1 - crop_y1
        new_x2 = intersect_x2 - crop_x1
        new_y2 = intersect_y2 - crop_y1
        
        # 确保坐标在新图像范围内
        new_x1 = max(0, min(new_x1, new_width - 1))
        new_y1 = max(0, min(new_y1, new_height - 1))
        new_x2 = max(0, min(new_x2, new_width - 1))
        new_y2 = max(0, min(new_y2, new_height - 1))
        
        # 检查裁剪后的边界框是否有效
        if new_x2 <= new_x1 or new_y2 <= new_y1:
            continue
        
        # 转换回YOLO格式
        new_center_x, new_center_y, new_width_norm, new_height_norm = bbox_to_yolo(
            new_x1, new_y1, new_x2, new_y2, new_width, new_height)
        
        transformed_annotations.append((class_id, new_center_x, new_center_y, new_width_norm, new_height_norm))
    
    return transformed_annotations

def process_single_image(image_path, txt_path, output_dir):
    """
    处理单张图像和对应的标注文件
    """
    print(f"处理图像: {os.path.basename(image_path)}")
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"  ❌ 错误: 无法读取图像文件 {image_path}")
        return False
    
    orig_height, orig_width = image.shape[:2]
    print(f"  📐 原始尺寸: {orig_width} x {orig_height}")
    
    # 计算16:9裁剪区域
    crop_x1, crop_y1, crop_x2, crop_y2, new_width, new_height = calculate_16_9_crop_region(orig_width, orig_height)
    print(f"  ✂️  裁剪区域: ({crop_x1}, {crop_y1}) -> ({crop_x2}, {crop_y2})")
    print(f"  📐 裁剪后尺寸: {new_width} x {new_height}")
    
    # 裁剪图像
    cropped_image = image[crop_y1:crop_y2, crop_x1:crop_x2]
    
    # 保存裁剪后的图像
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    output_image_path = os.path.join(output_dir, f"{image_name}_16_9.jpg")
    
    success = cv2.imwrite(output_image_path, cropped_image)
    if not success:
        print(f"  ❌ 错误: 保存图像失败 {output_image_path}")
        return False
    
    print(f"  ✅ 图像保存成功: {os.path.basename(output_image_path)}")
    
    # 处理标注文件（如果存在）
    if os.path.exists(txt_path):
        print(f"  📋 处理标注文件: {os.path.basename(txt_path)}")
        
        # 读取原始标注
        annotations = []
        with open(txt_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                detection = parse_yolo_detection(line)
                if detection is None:
                    print(f"    ⚠️  警告: 第{line_num}行格式错误: {line}")
                    continue
                
                annotations.append(detection)
        
        print(f"    📊 原始标注数量: {len(annotations)}")
        
        # 转换标注到裁剪后的坐标系
        transformed_annotations = transform_yolo_annotations(
            annotations, crop_x1, crop_y1, crop_x2, crop_y2,
            orig_width, orig_height, new_width, new_height)
        
        print(f"    📊 转换后标注数量: {len(transformed_annotations)}")
        
        # 保存转换后的标注文件
        output_txt_path = os.path.join(output_dir, f"{image_name}_16_9.txt")
        with open(output_txt_path, 'w') as f:
            for class_id, center_x, center_y, width, height in transformed_annotations:
                f.write(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}\n")
        
        print(f"  ✅ 标注保存成功: {os.path.basename(output_txt_path)}")
    else:
        print(f"  ⚠️  标注文件不存在: {os.path.basename(txt_path)}")
    
    return True

def batch_process_images(input_dir, output_dir):
    """
    批量处理目录下的所有图像
    """
    print("=" * 80)
    print("批量图像16:9裁剪和YOLO标注转换")
    print("=" * 80)
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print()
    
    # 检查输入目录是否存在
    if not os.path.exists(input_dir):
        print(f"❌ 错误: 输入目录不存在 {input_dir}")
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有图像文件
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif']
    image_files = []
    
    for ext in image_extensions:
        pattern = os.path.join(input_dir, ext)
        image_files.extend(glob.glob(pattern))
        # 也查找大写扩展名
        pattern = os.path.join(input_dir, ext.upper())
        image_files.extend(glob.glob(pattern))
    
    image_files = sorted(list(set(image_files)))  # 去重并排序
    
    print(f"📁 找到 {len(image_files)} 个图像文件")
    
    if len(image_files) == 0:
        print("❌ 没有找到图像文件")
        return False
    
    print()
    
    # 处理每个图像文件
    success_count = 0
    for i, image_path in enumerate(image_files, 1):
        print(f"🔄 [{i}/{len(image_files)}] ", end="")
        
        # 构造对应的txt文件路径
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        txt_path = os.path.join(input_dir, f"{image_name}.txt")
        
        try:
            success = process_single_image(image_path, txt_path, output_dir)
            if success:
                success_count += 1
            print()
        except Exception as e:
            print(f"  💥 处理出错: {str(e)}")
            print()
    
    print("=" * 80)
    print(f"🎉 批量处理完成!")
    print(f"📊 成功处理: {success_count}/{len(image_files)} 个文件")
    print(f"📁 输出目录: {output_dir}")
    print("=" * 80)
    
    return success_count > 0

def main():
    """
    主函数
    """
    # 指定输入和输出目录
    input_dir = r"D:\Download\62+125"
    output_dir = r"D:\Download\62+125_16_9"
    
    try:
        success = batch_process_images(input_dir, output_dir)
        if success:
            print("\n✅ 程序执行成功!")
            return 0
        else:
            print("\n❌ 程序执行失败!")
            return 1
    except Exception as e:
        print(f"\n💥 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
