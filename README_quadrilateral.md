# 四边形角度计算工具

这个工具可以根据给定的四个二维点坐标，计算四边形的四个内角。

## 文件说明

- `quadrilateral_angles.py` - 核心模块，包含所有计算函数
- `example_usage.py` - 使用示例，展示如何使用这个模块
- `convert.py` - 原有文件，已集成四边形角度计算功能

## 主要功能

### 1. 计算四边形角度
```python
from quadrilateral_angles import calculate_quadrilateral_angles

# 定义四个点（按顺序排列）
points = [(0, 0), (3, 0), (3, 2), (0, 2)]  # 矩形

# 计算角度
angles = calculate_quadrilateral_angles(points)
print(angles)  # [90.0, 90.0, 90.0, 90.0]
```

### 2. 打印详细信息
```python
from quadrilateral_angles import print_quadrilateral_info

points = [(0, 0), (4, 1), (3, 4), (1, 3)]
print_quadrilateral_info(points)
```

### 3. 验证四边形
```python
from quadrilateral_angles import validate_quadrilateral

points = [(0, 0), (1, 0), (1, 1), (0, 1)]
is_valid, message = validate_quadrilateral(points)
print(f"验证结果: {message}")
```

## 使用方法

### 方法1: 直接运行示例
```bash
python example_usage.py
```

### 方法2: 在你的代码中导入使用
```python
from quadrilateral_angles import calculate_quadrilateral_angles, print_quadrilateral_info

# 你的四个点坐标
my_points = [
    (10, 20),   # 点1
    (50, 25),   # 点2
    (45, 70),   # 点3
    (15, 65)    # 点4
]

# 计算角度
angles = calculate_quadrilateral_angles(my_points)
print(f"四个角度: {angles}")

# 或者打印详细信息
print_quadrilateral_info(my_points)
```

## 重要说明

1. **点的顺序**: 四个点必须按照四边形的顶点顺序排列（顺时针或逆时针都可以）
2. **坐标系**: 使用标准的二维坐标系 (x, y)
3. **角度单位**: 返回的角度以度为单位
4. **内角和**: 四边形的内角和应该等于360度

## 算法原理

使用向量夹角公式计算每个顶点的内角：
1. 对于每个顶点，构建两个向量指向相邻的两个顶点
2. 使用向量点积公式计算夹角的余弦值
3. 通过反余弦函数得到角度（弧度）
4. 转换为度数

公式：`cos(θ) = (v1 · v2) / (|v1| × |v2|)`

## 测试示例

代码包含多个测试示例：
- 正方形（所有角都是90°）
- 矩形（所有角都是90°）
- 梯形（对角相等）
- 菱形（对角相等）
- 任意四边形

运行 `python quadrilateral_angles.py` 或 `python example_usage.py` 查看测试结果。
