#include "square_corner_detector.hpp"
#include <iostream>

/**
 * @brief Example usage of SquareCornerDetector class
 * 
 * This example demonstrates how to use the SquareCornerDetector class
 * to detect 4 white squares arranged in a 2x2 grid and extract their corner points.
 */
int main() {
    // Create detector instance with default parameters
    SquareCornerDetector detector;
    
    // You can also customize parameters:
    // SquareCornerDetector detector(1000.0,   // minArea
    //                              50000.0,   // maxArea  
    //                              0.02,      // approxEpsilon
    //                              50.0,      // cannyLow
    //                              150.0);    // cannyHigh
    
    // Load image containing 4 white squares in 2x2 arrangement
    cv::Mat image = cv::imread("input_image.jpg");
    
    if (image.empty()) {
        std::cerr << "Error: Could not load image!" << std::endl;
        return -1;
    }
    
    // Detect square corners
    bool success = detector.detectSquareCorners(image);
    
    if (success) {
        std::cout << "Successfully detected 4 squares!" << std::endl;
        
        // Get the corner points
        const SquareGridCorners& corners = detector.getGridCorners();
        
        // Print corner coordinates
        std::cout << "Corner points:" << std::endl;
        std::cout << "Top-left corner: (" << corners.topLeft.x << ", " << corners.topLeft.y << ")" << std::endl;
        std::cout << "Top-right corner: (" << corners.topRight.x << ", " << corners.topRight.y << ")" << std::endl;
        std::cout << "Bottom-left corner: (" << corners.bottomLeft.x << ", " << corners.bottomLeft.y << ")" << std::endl;
        std::cout << "Bottom-right corner: (" << corners.bottomRight.x << ", " << corners.bottomRight.y << ")" << std::endl;
        
        // Get detected squares information
        const std::vector<DetectedSquare>& squares = detector.getDetectedSquares();
        std::cout << "\nDetected squares information:" << std::endl;
        for (size_t i = 0; i < squares.size(); ++i) {
            std::cout << "Square " << i + 1 << ":" << std::endl;
            std::cout << "  Center: (" << squares[i].center.x << ", " << squares[i].center.y << ")" << std::endl;
            std::cout << "  Area: " << squares[i].area << std::endl;
            std::cout << "  Corners: ";
            for (const auto& corner : squares[i].corners) {
                std::cout << "(" << corner.x << ", " << corner.y << ") ";
            }
            std::cout << std::endl;
        }
        
        // Draw results on image
        cv::Mat resultImage = detector.drawResults(image, true, true);
        
        // Save or display result
        cv::imwrite("output_with_corners.jpg", resultImage);
        std::cout << "\nResult image saved as 'output_with_corners.jpg'" << std::endl;
        
        // Optionally display the image (uncomment if you have display capability)
        // cv::imshow("Detected Squares and Corners", resultImage);
        // cv::waitKey(0);
        // cv::destroyAllWindows();
        
    } else {
        std::cerr << "Failed to detect exactly 4 squares in the image!" << std::endl;
        
        // You can still check what was detected
        const std::vector<DetectedSquare>& squares = detector.getDetectedSquares();
        std::cout << "Number of squares detected: " << squares.size() << std::endl;
        
        // Try adjusting parameters if detection failed
        std::cout << "Try adjusting detection parameters:" << std::endl;
        std::cout << "- Minimum area (current: default)" << std::endl;
        std::cout << "- Maximum area (current: default)" << std::endl;
        std::cout << "- Approximation epsilon (current: default)" << std::endl;
        std::cout << "- Canny thresholds (current: default)" << std::endl;
        
        return -1;
    }
    
    return 0;
}

/**
 * @brief Alternative usage example with parameter customization
 */
void customParameterExample() {
    // Create detector with custom parameters
    SquareCornerDetector detector;
    
    // Adjust parameters based on your specific image characteristics
    detector.setMinArea(500.0);        // Smaller minimum area
    detector.setMaxArea(100000.0);     // Larger maximum area
    detector.setApproxEpsilon(0.015);  // More precise approximation
    detector.setCannyThresholds(30.0, 100.0);  // Different edge detection thresholds
    
    // Load and process image
    cv::Mat image = cv::imread("custom_image.jpg");
    
    if (!image.empty()) {
        bool success = detector.detectSquareCorners(image);
        
        if (success) {
            const SquareGridCorners& corners = detector.getGridCorners();
            std::cout << "Custom detection successful!" << std::endl;
            // Process corners as needed...
        }
    }
}

/**
 * @brief Example of processing multiple images
 */
void batchProcessingExample() {
    SquareCornerDetector detector;
    
    std::vector<std::string> imageFiles = {
        "image1.jpg",
        "image2.jpg", 
        "image3.jpg"
    };
    
    for (const auto& filename : imageFiles) {
        cv::Mat image = cv::imread(filename);
        
        if (image.empty()) {
            std::cerr << "Could not load " << filename << std::endl;
            continue;
        }
        
        bool success = detector.detectSquareCorners(image);
        
        if (success) {
            const SquareGridCorners& corners = detector.getGridCorners();
            std::cout << "Processed " << filename << " successfully" << std::endl;
            
            // Save result
            cv::Mat result = detector.drawResults(image);
            std::string outputName = "result_" + filename;
            cv::imwrite(outputName, result);
            
        } else {
            std::cerr << "Failed to process " << filename << std::endl;
        }
    }
}
