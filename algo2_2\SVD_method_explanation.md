# SVD方法求解3D点的数学原理

## 问题描述

在函数 `recover_3d_points` 中，我们需要根据以下两个约束方程求解3D点 X：

1. **投影仪投影方程**: `a * quad_2d_proj = G * X_hom`
2. **相机投影方程**: `b * quad_2d = K * X`

其中：
- `a` 和 `b` 是未知标量
- `X` 是未知的3D点 `[X, Y, Z]`
- `X_hom` 是齐次坐标 `[X, Y, Z, 1]`
- `G` 是投影矩阵 (3×4)
- `K` 是相机内参矩阵 (3×3)
- `quad_2d_proj` 是投影仪上的2D点（齐次坐标）
- `quad_2d` 是相机上的2D点（归一化坐标）

## 数学推导

### 1. 投影仪约束方程

投影仪投影方程：`a * [u_proj, v_proj, w_proj]^T = G * [X, Y, Z, 1]^T`

展开为：
```
a * u_proj = G[0,0]*X + G[0,1]*Y + G[0,2]*Z + G[0,3]
a * v_proj = G[1,0]*X + G[1,1]*Y + G[1,2]*Z + G[1,3]  
a * w_proj = G[2,0]*X + G[2,1]*Y + G[2,2]*Z + G[2,3]
```

为了消除未知标量 `a`，我们使用交叉乘积：
- `u_proj * (第3个方程) - w_proj * (第1个方程) = 0`
- `v_proj * (第3个方程) - w_proj * (第2个方程) = 0`

得到两个线性约束：
```
(u_proj*G[2,0] - w_proj*G[0,0])*X + (u_proj*G[2,1] - w_proj*G[0,1])*Y + 
(u_proj*G[2,2] - w_proj*G[0,2])*Z + (u_proj*G[2,3] - w_proj*G[0,3]) = 0

(v_proj*G[2,0] - w_proj*G[1,0])*X + (v_proj*G[2,1] - w_proj*G[1,1])*Y + 
(v_proj*G[2,2] - w_proj*G[1,2])*Z + (v_proj*G[2,3] - w_proj*G[1,3]) = 0
```

### 2. 相机约束方程

相机投影方程：`b * [u_cam, v_cam, 1]^T = K * [X, Y, Z]^T`

其中K是3x3相机内参矩阵：
```
K = [fx  0  cx]
    [0  fy  cy]
    [0   0   1]
```

展开相机投影方程：
```
b * u_cam = fx*X + cx*Z
b * v_cam = fy*Y + cy*Z
b * 1 = Z
```

消除标量 `b`：
```
u_cam * Z = fx*X + cx*Z  →  fx*X + (cx - u_cam)*Z = 0
v_cam * Z = fy*Y + cy*Z  →  fy*Y + (cy - v_cam)*Z = 0
```

### 3. 构造线性方程组

将所有约束组合成齐次线性方程组 `A * [X, Y, Z, 1]^T = 0`：

```
A = [
    [u_proj*G[2,0] - w_proj*G[0,0], u_proj*G[2,1] - w_proj*G[0,1], u_proj*G[2,2] - w_proj*G[0,2], u_proj*G[2,3] - w_proj*G[0,3]],
    [v_proj*G[2,0] - w_proj*G[1,0], v_proj*G[2,1] - w_proj*G[1,1], v_proj*G[2,2] - w_proj*G[1,2], v_proj*G[2,3] - w_proj*G[1,3]],
    [fx, 0, cx - u_cam, 0],
    [0, fy, cy - v_cam, 0]
]
```

### 4. SVD求解

对矩阵 A 进行奇异值分解：`A = U * Σ * V^T`

齐次线性方程组 `A * x = 0` 的解是矩阵 A 的零空间，对应于最小奇异值的右奇异向量。

因此：`X_hom = V[:, -1]`（V的最后一列）

最后进行齐次坐标归一化：`X = X_hom[:3] / X_hom[3]`

## 代码实现

```python
def recover_3d_points(quad_2d, G, K, dist):
    # 投影仪对应的四边形坐标
    quad_2d_proj = np.array([...])  # 投影仪屏幕四角
    quad_2d_proj_hom = np.concatenate((quad_2d_proj, np.ones((4,1))), axis=1)

    # 提取相机内参
    fx, fy = K[0,0], K[1,1]
    cx, cy = K[0,2], K[1,2]

    X_list = []
    for i in range(len(quad_2d)):
        u_cam, v_cam = quad_2d[i]  # 相机像素坐标
        u_proj, v_proj, w_proj = quad_2d_proj_hom[i]

        # 构造线性方程组
        A = np.array([
            # 投影仪约束方程
            [u_proj*G[2,0] - w_proj*G[0,0], u_proj*G[2,1] - w_proj*G[0,1],
             u_proj*G[2,2] - w_proj*G[0,2], u_proj*G[2,3] - w_proj*G[0,3]],
            [v_proj*G[2,0] - w_proj*G[1,0], v_proj*G[2,1] - w_proj*G[1,1],
             v_proj*G[2,2] - w_proj*G[1,2], v_proj*G[2,3] - w_proj*G[1,3]],

            # 相机约束方程（使用完整的K矩阵）
            [fx, 0, cx - u_cam, 0],
            [0, fy, cy - v_cam, 0]
        ])

        # SVD求解
        _, _, VT = np.linalg.svd(A)
        X_hom = VT[-1]

        # 齐次坐标归一化
        if abs(X_hom[3]) > 1e-8:
            X_3d = X_hom[:3] / X_hom[3]
        else:
            X_3d = X_hom[:3]

        X_list.append(X_3d)

    return np.array(X_list, dtype=np.float32)
```

## 优势

1. **数学严谨性**: 基于线性代数理论，求解过程数学上严格
2. **鲁棒性**: SVD方法对噪声具有良好的鲁棒性
3. **唯一性**: 在约束充分的情况下，能够得到唯一解
4. **数值稳定性**: SVD分解数值稳定，适合实际应用

## 测试结果

测试显示该方法能够成功恢复3D点坐标，输出数值正常，无NaN或无穷大值。
