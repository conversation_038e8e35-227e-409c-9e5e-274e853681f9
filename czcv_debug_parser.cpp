#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <regex>

// 结构体定义
struct BoundingBox {
    float xmin, ymin, xmax, ymax;
    int classid, instanceid;
    
    BoundingBox() : xmin(0), ymin(0), xmax(0), ymax(0), classid(0), instanceid(0) {}
    BoundingBox(float x1, float y1, float x2, float y2, int cid, int iid) 
        : xmin(x1), ymin(y1), xmax(x2), ymax(y2), classid(cid), instanceid(iid) {}
};

struct Gesture {
    float xmin, ymin, xmax, ymax;
    int classid, instanceid;
    
    Gesture() : xmin(0), ymin(0), xmax(0), ymax(0), classid(0), instanceid(0) {}
    Gesture(float x1, float y1, float x2, float y2, int cid, int iid) 
        : xmin(x1), ymin(y1), xmax(x2), ymax(y2), classid(cid), instanceid(iid) {}
};

struct FrameData {
    std::string timestamp;
    std::vector<BoundingBox> in_bbox;
    std::vector<BoundingBox> tracked_bbox;
    std::vector<Gesture> gesture;
    float doa;
    std::vector<BoundingBox> sub_in_bbox;
    std::vector<BoundingBox> sub_tracked_bbox;
    float doa_sub;
    
    FrameData() : doa(-1.0f), doa_sub(-1.0f) {}
};

class CZCVDebugParser {
private:
    // 解析数字序列为BoundingBox向量
    std::vector<BoundingBox> parseBoundingBoxes(const std::string& data) {
        std::vector<BoundingBox> boxes;
        if (data.empty()) return boxes;
        
        std::istringstream iss(data);
        std::vector<float> values;
        std::string token;
        
        // 分割数字
        while (iss >> token) {
            try {
                values.push_back(std::stof(token));
            } catch (const std::exception& e) {
                // 忽略无法转换的字符串
                continue;
            }
        }
        
        // 每6个数字组成一个box
        for (size_t i = 0; i + 5 < values.size(); i += 6) {
            boxes.emplace_back(values[i], values[i+1], values[i+2], 
                             values[i+3], (int)values[i+4], (int)values[i+5]);
        }
        
        return boxes;
    }
    
    // 解析数字序列为Gesture向量
    std::vector<Gesture> parseGestures(const std::string& data) {
        std::vector<Gesture> gestures;
        if (data.empty()) return gestures;
        
        std::istringstream iss(data);
        std::vector<float> values;
        std::string token;
        
        // 分割数字
        while (iss >> token) {
            try {
                values.push_back(std::stof(token));
            } catch (const std::exception& e) {
                // 忽略无法转换的字符串
                continue;
            }
        }
        
        // 每6个数字组成一个gesture
        for (size_t i = 0; i + 5 < values.size(); i += 6) {
            gestures.emplace_back(values[i], values[i+1], values[i+2], 
                                values[i+3], (int)values[i+4], (int)values[i+5]);
        }
        
        return gestures;
    }
    
    // 提取字段值
    std::string extractFieldValue(const std::string& line, const std::string& fieldName) {
        size_t pos = line.find(fieldName + ":");
        if (pos == std::string::npos) return "";
        
        pos += fieldName.length() + 1; // 跳过字段名和冒号
        
        // 找到下一个分号或行尾
        size_t endPos = line.find(';', pos);
        if (endPos == std::string::npos) {
            endPos = line.length();
        }
        
        std::string value = line.substr(pos, endPos - pos);
        
        // 去除前后空格
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);
        
        return value;
    }
    
    // 提取时间戳
    std::string extractTimestamp(const std::string& line) {
        // 时间戳格式: 09-05 13:56:09.129
        std::regex timestampRegex(R"((\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}))");
        std::smatch match;
        
        if (std::regex_search(line, match, timestampRegex)) {
            return match[1].str();
        }
        
        return "";
    }

public:
    // 解析单行数据
    FrameData parseLine(const std::string& line) {
        FrameData frame;
        
        // 提取时间戳
        frame.timestamp = extractTimestamp(line);
        
        // 提取各个字段
        std::string in_bbox_str = extractFieldValue(line, "in_bbox");
        std::string tracked_bbox_str = extractFieldValue(line, "tracked_bbox");
        std::string gesture_str = extractFieldValue(line, "gesture");
        std::string doa_str = extractFieldValue(line, "doa");
        std::string sub_in_bbox_str = extractFieldValue(line, "sub_in_bbox");
        std::string sub_tracked_bbox_str = extractFieldValue(line, "sub_tracked_bbox");
        std::string doa_sub_str = extractFieldValue(line, "doa_sub");
        
        // 解析数据
        frame.in_bbox = parseBoundingBoxes(in_bbox_str);
        frame.tracked_bbox = parseBoundingBoxes(tracked_bbox_str);
        frame.gesture = parseGestures(gesture_str);
        frame.sub_in_bbox = parseBoundingBoxes(sub_in_bbox_str);
        frame.sub_tracked_bbox = parseBoundingBoxes(sub_tracked_bbox_str);
        
        // 解析DOA值
        if (!doa_str.empty()) {
            try {
                frame.doa = std::stof(doa_str);
            } catch (const std::exception& e) {
                frame.doa = -1.0f;
            }
        }
        
        if (!doa_sub_str.empty()) {
            try {
                frame.doa_sub = std::stof(doa_sub_str);
            } catch (const std::exception& e) {
                frame.doa_sub = -1.0f;
            }
        }
        
        return frame;
    }
    
    // 解析整个文件
    std::vector<FrameData> parseFile(const std::string& filename) {
        std::vector<FrameData> frames;
        std::ifstream file(filename);
        
        if (!file.is_open()) {
            std::cerr << "无法打开文件: " << filename << std::endl;
            return frames;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            if (!line.empty()) {
                frames.push_back(parseLine(line));
            }
        }
        
        file.close();
        return frames;
    }
    
    // 打印BoundingBox信息
    void printBoundingBox(const BoundingBox& box, const std::string& prefix = "") {
        std::cout << prefix << "Box: (" << box.xmin << ", " << box.ymin 
                  << ", " << box.xmax << ", " << box.ymax 
                  << ") class=" << box.classid << " instance=" << box.instanceid << std::endl;
    }
    
    // 打印Gesture信息
    void printGesture(const Gesture& gesture, const std::string& prefix = "") {
        std::cout << prefix << "Gesture: (" << gesture.xmin << ", " << gesture.ymin 
                  << ", " << gesture.xmax << ", " << gesture.ymax 
                  << ") class=" << gesture.classid << " instance=" << gesture.instanceid << std::endl;
    }
    
    // 打印帧数据
    void printFrameData(const FrameData& frame) {
        std::cout << "=== Frame: " << frame.timestamp << " ===" << std::endl;
        
        std::cout << "in_bbox (" << frame.in_bbox.size() << " boxes):" << std::endl;
        for (const auto& box : frame.in_bbox) {
            printBoundingBox(box, "  ");
        }
        
        std::cout << "tracked_bbox (" << frame.tracked_bbox.size() << " boxes):" << std::endl;
        for (const auto& box : frame.tracked_bbox) {
            printBoundingBox(box, "  ");
        }
        
        std::cout << "gesture (" << frame.gesture.size() << " gestures):" << std::endl;
        for (const auto& gesture : frame.gesture) {
            printGesture(gesture, "  ");
        }
        
        std::cout << "doa: " << frame.doa << std::endl;
        
        std::cout << "sub_in_bbox (" << frame.sub_in_bbox.size() << " boxes):" << std::endl;
        for (const auto& box : frame.sub_in_bbox) {
            printBoundingBox(box, "  ");
        }
        
        std::cout << "sub_tracked_bbox (" << frame.sub_tracked_bbox.size() << " boxes):" << std::endl;
        for (const auto& box : frame.sub_tracked_bbox) {
            printBoundingBox(box, "  ");
        }
        
        std::cout << "doa_sub: " << frame.doa_sub << std::endl;
        std::cout << std::endl;
    }
};

int main() {
    CZCVDebugParser parser;

    // 解析文件
    std::vector<FrameData> frames = parser.parseFile("czcv_debug.txt");

    std::cout << "成功解析 " << frames.size() << " 帧数据" << std::endl << std::endl;

    // 打印前几帧数据作为示例
    int maxFramesToShow = 3;
    for (int i = 0; i < std::min(maxFramesToShow, (int)frames.size()); ++i) {
        parser.printFrameData(frames[i]);
    }

    // 统计信息
    int totalInBbox = 0, totalTrackedBbox = 0, totalGesture = 0;
    int totalSubInBbox = 0, totalSubTrackedBbox = 0;

    for (const auto& frame : frames) {
        totalInBbox += frame.in_bbox.size();
        totalTrackedBbox += frame.tracked_bbox.size();
        totalGesture += frame.gesture.size();
        totalSubInBbox += frame.sub_in_bbox.size();
        totalSubTrackedBbox += frame.sub_tracked_bbox.size();
    }

    std::cout << "=== 统计信息 ===" << std::endl;
    std::cout << "总帧数: " << frames.size() << std::endl;
    std::cout << "总 in_bbox 数量: " << totalInBbox << std::endl;
    std::cout << "总 tracked_bbox 数量: " << totalTrackedBbox << std::endl;
    std::cout << "总 gesture 数量: " << totalGesture << std::endl;
    std::cout << "总 sub_in_bbox 数量: " << totalSubInBbox << std::endl;
    std::cout << "总 sub_tracked_bbox 数量: " << totalSubTrackedBbox << std::endl;

    return 0;
}
