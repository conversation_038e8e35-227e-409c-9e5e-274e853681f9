import os
import zipfile
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from io import BytesIO

class ZipCipher:
    def __init__(self):
        self.cipher_algorithm = "AES/CBC/PKCS5Padding"
        self.key_algorithm = "AES"
        self.key = b"czur9007czur9007"  # 16 bytes key
        self.iv = b"czur9007czur9007"   # 16 bytes IV

    def create_encode_cipher(self):
        """创建解密cipher对象"""
        cipher = Cipher(
            algorithms.AES(self.key),
            modes.CBC(self.iv),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()
        return decryptor

    def create_decode_cipher(self):
        """创建加密cipher对象"""
        cipher = Cipher(
            algorithms.AES(self.key),
            modes.CBC(self.iv),
            backend=default_backend()
        )
        encryptor = cipher.encryptor()
        return encryptor

    def decode_zip_and_zip(self, src, dest):
        """
        解密源ZIP文件并重新打包到目标ZIP文件
        注意：由于AES加密解密的块大小限制，需要特殊处理
        """
        # 读取源ZIP文件
        with zipfile.ZipFile(src, 'r') as src_zip:
            # 创建目标ZIP文件
            with zipfile.ZipFile(dest, 'w', zipfile.ZIP_DEFLATED) as dest_zip:
                for file_info in src_zip.filelist:
                    if file_info.is_dir():
                        # 跳过目录，ZIP文件通常不包含目录条目
                        continue
                    else:
                        # 读取加密的文件内容
                        encrypted_data = src_zip.read(file_info.filename)
                        
                        # 解密数据
                        decrypted_data = self.decrypt_data(encrypted_data)
                        
                        # 将解密后的内容写入新的ZIP文件
                        dest_zip.writestr(file_info.filename, decrypted_data)

    def decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            # 创建解密器
            cipher = Cipher(
                algorithms.AES(self.key),
                modes.CBC(self.iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            
            # 解密数据
            decrypted_padded = decryptor.update(encrypted_data) + decryptor.finalize()
            
            # 移除PKCS7填充
            padding_length = decrypted_padded[-1]
            decrypted_data = decrypted_padded[:-padding_length]
            
            return decrypted_data
        except Exception as e:
            print(f"解密数据时出错: {e}")
            raise

    def encrypt_data(self, data):
        """加密数据"""
        try:
            # 计算需要填充的长度（PKCS7填充）
            block_size = 16
            padding_length = block_size - (len(data) % block_size)
            padded_data = data + bytes([padding_length] * padding_length)
            
            # 创建加密器
            cipher = Cipher(
                algorithms.AES(self.key),
                modes.CBC(self.iv),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            
            # 加密数据
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
            
            return encrypted_data
        except Exception as e:
            print(f"加密数据时出错: {e}")
            raise

    def encrypt_zip_and_zip(self, src, dest):
        """
        加密源ZIP文件并重新打包到目标ZIP文件
        """
        with zipfile.ZipFile(src, 'r') as src_zip:
            with zipfile.ZipFile(dest, 'w', zipfile.ZIP_DEFLATED) as dest_zip:
                for file_info in src_zip.filelist:
                    if file_info.is_dir():
                        # 跳过目录
                        continue
                    else:
                        # 读取原始文件内容
                        original_data = src_zip.read(file_info.filename)
                        
                        # 加密数据
                        encrypted_data = self.encrypt_data(original_data)
                        
                        # 将加密后的内容写入新的ZIP文件
                        # 注意：加密后的数据在ZIP中存储，需要在解密时进行处理
                        dest_zip.writestr(file_info.filename, encrypted_data)


# 使用示例
def main():
    zip_cipher = ZipCipher()
    
    # 示例：解密ZIP并重新打包
    zip_cipher.decode_zip_and_zip(r"D:/Download/25-11-14_08-32-16.zip", "D:/Download/25-11-14_08-32-16_dec.zip")
    
    # 示例：加密ZIP并重新打包
    # zip_cipher.encrypt_zip_and_zip("source.zip", "encrypted_destination.zip")


if __name__ == "__main__":
    main()