import cv2, numpy as np, glob

# ---------- 可调参数 ----------
PROJ_W, PROJ_H = 1024, 768          # 投影仪分辨率
PATTERN_FILE = 'chess_9x6.png'      # 9×6 内角点棋盘格
BOARD = (9, 6)                      # 内角点 w,h
N_POSE = 5                          # 需要摆几个姿态
# -------------------------------

# 生成一张棋盘格图案（投影仪用）
def make_chess():
    pattern = np.zeros((PROJ_H, PROJ_W), np.uint8)
    square = 80
    for i in range(BOARD[0]+1):
        for j in range(BOARD[1]+1):
            color = 255 if (i+j)%2==0 else 0
            x1, y1 = i*square, j*square
            pattern[y1:y1+square, x1:x1+square] = color
    cv2.imwrite(PATTERN_FILE, pattern)

# 检测角点，返回 list[np.ndarray(shape=(N,1,2), float32)]
def find_corners(img):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    ret, corners = cv2.findChessboardCorners(gray, BOARD,
                                             cv2.CALIB_CB_ADAPTIVE_THRESH)
    if ret:
        cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1),
                         (cv2.TERM_CRITERIA_EPS+cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
    return ret, corners

# 主标定函数
def calibrate_system(cam, win='proj'):
    make_chess()
    objp = np.zeros((BOARD[0]*BOARD[1], 3), np.float32)
    objp[:, :2] = np.mgrid[0:BOARD[0], 0:BOARD[1]].T.reshape(-1, 2)

    objpoints, imgpoints_cam, imgpoints_proj = [], [], []
    H_list = []

    print('【标定】按空格采集，q 退出，共需 %d 组' % N_POSE)
    while len(H_list) < N_POSE:
        ret, frame = cam.read()
        if not ret:
            continue
        k = cv2.waitKey(1) & 0xFF
        if k == ord('q'):
            break
        if k != ord(' '):
            continue

        ret_cam, corners_cam = find_corners(frame)
        if not ret_cam:
            print('摄像头未检测到棋盘格')
            continue

        # 投影仪投射图案
        canvas = 255*np.ones((PROJ_H, PROJ_W, 3), np.uint8)
        chess = cv2.imread(PATTERN_FILE)
        canvas[:, :] = chess[:, :, None]
        cv2.imshow(win, canvas)
        cv2.waitKey(200)  # 给相机曝光时间

        ret_proj, corners_proj = find_corners(canvas)
        if not ret_proj:
            print('投影仪图案未检测到')
            continue

        # 保存对应点
        objpoints.append(objp)
        imgpoints_cam.append(corners_cam)
        imgpoints_proj.append(corners_proj)

        # 计算单应 H_proj→cam
        H, _ = cv2.findHomography(corners_proj.reshape(-1, 2),
                                   corners_cam.reshape(-1, 2))
        H_list.append(H.astype(np.float32))
        print('已采集 %d/%d' % (len(H_list), N_POSE))

    cv2.destroyWindow(win)

    # 相机内参（已知）
    Kc = np.load('camera_intrinsic.npz')['K']  # 提前用 OpenCV 标定好
    # 本例简化：假设零畸变
    ret, Kc, _, _, _ = cv2.calibrateCamera(objpoints, imgpoints_cam,
                                           (cam.get(3), cam.get(4)), Kc, None,
                                           flags=cv2.CALIB_USE_INTRINSIC_GUESS)

    # 用两幅 H 估算极点 + 投影矩阵（论文 2.1/2.2 节简化）
    H1, H2 = H_list[0], H_list[1]
    M = np.linalg.inv(H1) @ H2
    eigvals, eigvecs = np.linalg.eig(M)
    real_idx = np.isclose(eigvals.imag, 0)
    e1 = eigvecs[:, real_idx].real[:, 0]
    e1 = e1 / e1[2]
    e2 = (H1 @ e1)
    e2 = e2 / e2[2]

    # 构造 F = [e2]_x H1
    ex = np.array([[0, -e2[2], e2[1]],
                   [e2[2], 0, -e2[0]],
                   [-e2[1], e2[0], 0]])
    F = ex @ H1

    # 分解出 E → R,t （OpenCV 自带）
    E = Kc.T @ F @ Kc
    _, R, t, _ = cv2.recoverPose(E, imgpoints_cam[0], imgpoints_proj[0], Kc)

    # 估算投影仪内参（论文 2.2 简化版：零畸变，单位像素比）
    # 用焦距平均作为初始值
    f = (Kc[0, 0] + Kc[1, 1]) / 2
    Kp = np.array([[f, 0, PROJ_W/2],
                   [0, f, PROJ_H/2],
                   [0, 0, 1]])

    # 保存结果
    np.savez('system_calib.npz', Kp=Kp, Kc=Kc, R=R, t=t)
    print('标定完成，已保存 system_calib.npz')


if __name__ == '__main__':
    cam = cv2.VideoCapture(0)
    calibrate_system(cam)
    cam.release()