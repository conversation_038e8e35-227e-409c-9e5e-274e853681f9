# Android ARM64 Build Guide for Windows

This guide explains how to build the czcv_camera project for Android ARM64 (arm64-v8a) on Windows.

## Prerequisites

### Required Tools

1. **Android NDK r23c**
   - Download from: https://developer.android.com/ndk/downloads
   - Install to: `D:\Package\android-ndk-r23c` (or update path in scripts)

2. **CMake 3.1+**
   - Download from: https://cmake.org/download/
   - Add to system PATH

3. **Make (MSYS2 or MinGW)**
   - Install MSYS2: https://www.msys2.org/
   - Add `C:\msys64\usr\bin` to system PATH

### Environment Check

Run the environment check script to verify all tools are installed:

```batch
check_build_env.bat
```

## Build Process

### Option 1: Quick Build (if third-party libraries are already built)

```batch
build_armeabi_arm64.bat
```

### Option 2: Complete Build (including third-party libraries)

1. **Build third-party libraries first:**
   ```batch
   cd third_party
   build_all_android_arm64.bat
   cd ..
   ```

2. **Build the main project:**
   ```batch
   build_armeabi_arm64.bat
   ```

### Option 3: Step-by-step Build

1. **Build individual third-party libraries:**
   ```batch
   cd third_party
   build_opencv_android.bat
   build_glog_android.bat
   build_tnn_android.bat
   cd ..
   ```

2. **Build the main project:**
   ```batch
   build_armeabi_arm64.bat
   ```

## Project Structure

```
czcv_camera/
├── build_armeabi_arm64.bat          # Main build script (Windows)
├── build_armeabi_arm64.sh           # Original build script (Linux)
├── check_build_env.bat              # Environment check script
├── CMakeLists.txt                   # Main CMake configuration
├── lib/
│   ├── CMakeLists.txt               # Library CMake configuration
│   ├── include/                     # Header files
│   └── src/                         # Source files
├── third_party/
│   ├── third_party.cmake            # Third-party library configuration
│   ├── build_all_android_arm64.bat  # Build all third-party libraries
│   ├── build_opencv_android.bat     # Build OpenCV
│   ├── build_glog_android.bat       # Build glog
│   ├── build_tnn_android.bat        # Build TNN
│   ├── source/                      # Third-party source code
│   └── prebuilt/
│       └── android_v8a/             # Built libraries for ARM64
│           ├── opencv4.5.1/
│           ├── glog/
│           └── TNN/
└── output/
    └── android_v8a/
        └── libs/
            └── libczcv_camera.so    # Final output library
```

## Configuration Details

### CMake Configuration

The project uses the following key CMake variables:

- `Dst_Platform=android_v8a`: Target platform
- `ANDROID_ABI=arm64-v8a`: Android ABI
- `ANDROID_NATIVE_API_LEVEL=24`: Minimum API level
- `ANDROID_STL=c++_shared`: C++ standard library

### Third-party Libraries

The project depends on:

1. **OpenCV 4.5.1**
   - Modules: core, imgproc, imgcodecs, calib3d
   - Built as static libraries

2. **glog 0.3.5**
   - Logging library
   - Built as static library

3. **TNN**
   - Neural network inference framework
   - Built as static library with ARM64 optimizations

## Troubleshooting

### Common Issues

1. **NDK not found**
   - Update `ANDROID_NDK` path in `build_armeabi_arm64.bat`
   - Ensure NDK is properly installed

2. **Make not found**
   - Install MSYS2 and add to PATH
   - Or install MinGW-w64

3. **CMake configuration fails**
   - Check CMake version (3.1+ required)
   - Verify NDK toolchain file exists

4. **Build fails with linking errors**
   - Ensure all third-party libraries are built
   - Check library paths in `third_party.cmake`

### Build Verification

After successful build, verify the output:

```batch
# Check if library exists
dir output\android_v8a\libs\libczcv_camera.so

# Check library architecture (requires NDK tools)
%ANDROID_NDK%\toolchains\llvm\prebuilt\windows-x86_64\bin\llvm-objdump.exe -f output\android_v8a\libs\libczcv_camera.so
```

## Key Changes from Linux Version

1. **Script Format**: Converted from bash (.sh) to batch (.bat)
2. **Path Separators**: Changed from `/` to `\`
3. **Environment Variables**: Windows-style variable syntax
4. **Build Generator**: Uses "MSYS Makefiles" instead of default
5. **Tool Paths**: Windows-specific NDK tool paths
6. **Error Handling**: Windows batch error handling

## Output

Successful build produces:
- `output/android_v8a/libs/libczcv_camera.so`: Main shared library
- Stripped and optimized for Android ARM64 deployment

The library can be integrated into Android projects using JNI or NDK.
