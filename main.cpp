#include "hough_line_detector.h"
#include <opencv2/opencv.hpp>
#include <iostream>

int main() {
    // 创建一个测试图像（白色矩形在黑色背景上）
    cv::Mat test_image = cv::Mat::zeros(400, 600, CV_8UC3);
    
    // 绘制一个白色矩形
    cv::rectangle(test_image, cv::Point(100, 80), cv::Point(500, 320), 
                  cv::Scalar(255, 255, 255), 3);
    
    // 也可以从文件加载图像
    // cv::Mat test_image = cv::imread("your_image.jpg");
    // if (test_image.empty()) {
    //     std::cerr << "无法加载图像" << std::endl;
    //     return -1;
    // }
    
    std::cout << "开始Hough直线检测..." << std::endl;
    
    // 创建检测器实例，尝试不同的边缘检测方法
    std::vector<HoughLineDetector::EdgeDetectionMethod> methods = {
        HoughLineDetector::SOBEL_GRADIENT,
        HoughLineDetector::LAPLACIAN,
        HoughLineDetector::MORPHOLOGY_GRADIENT
    };
    
    std::vector<std::string> method_names = {
        "Sobel梯度",
        "拉普拉斯算子", 
        "形态学梯度"
    };
    
    for (int i = 0; i < methods.size(); i++) {
        std::cout << "\n=== 使用 " << method_names[i] << " 进行边缘检测 ===" << std::endl;
        
        HoughLineDetector detector(80, 1.0, CV_PI/180, methods[i]);
        
        if (detector.detectLines(test_image)) {
            std::cout << "成功检测到矩形!" << std::endl;
            
            // 获取检测结果
            auto rectangle = detector.getRectangle();
            auto lines = detector.getLines();
            auto h_lines = detector.getHorizontalLines();
            auto v_lines = detector.getVerticalLines();
            
            std::cout << "检测到 " << lines.size() << " 条直线" << std::endl;
            std::cout << "水平线: " << h_lines.size() << " 条" << std::endl;
            std::cout << "垂直线: " << v_lines.size() << " 条" << std::endl;
            
            // 打印矩形顶点
            std::cout << "矩形顶点:" << std::endl;
            std::cout << "  左上角: (" << rectangle.topLeft.x << ", " << rectangle.topLeft.y << ")" << std::endl;
            std::cout << "  右上角: (" << rectangle.topRight.x << ", " << rectangle.topRight.y << ")" << std::endl;
            std::cout << "  左下角: (" << rectangle.bottomLeft.x << ", " << rectangle.bottomLeft.y << ")" << std::endl;
            std::cout << "  右下角: (" << rectangle.bottomRight.x << ", " << rectangle.bottomRight.y << ")" << std::endl;
            
            // 绘制结果
            cv::Mat result = detector.drawResults(test_image, true, true);
            
            // 显示结果
            std::string window_name = "检测结果 - " + method_names[i];
            cv::imshow(window_name, result);
            cv::imshow("原始图像", test_image);
            
            std::cout << "按任意键继续..." << std::endl;
            cv::waitKey(0);
            cv::destroyAllWindows();
            
        } else {
            std::cout << "检测失败!" << std::endl;
        }
    }
    
    // 演示参数调整
    std::cout << "\n=== 演示参数调整 ===" << std::endl;
    HoughLineDetector detector(50, 1.0, CV_PI/180, HoughLineDetector::SOBEL_GRADIENT);
    
    // 尝试不同的阈值
    std::vector<int> thresholds = {30, 50, 80, 120};
    for (int threshold : thresholds) {
        std::cout << "\n尝试阈值: " << threshold << std::endl;
        detector.setThreshold(threshold);
        
        if (detector.detectLines(test_image)) {
            std::cout << "检测成功，检测到 " << detector.getLines().size() << " 条直线" << std::endl;
            
            cv::Mat result = detector.drawResults(test_image, true, true);
            std::string window_name = "阈值 " + std::to_string(threshold);
            cv::imshow(window_name, result);
            cv::waitKey(1000); // 自动切换
            cv::destroyWindow(window_name);
        } else {
            std::cout << "检测失败" << std::endl;
        }
    }
    
    std::cout << "\n检测完成!" << std::endl;
    return 0;
}
