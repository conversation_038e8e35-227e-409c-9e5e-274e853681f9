#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析运行时间数据的脚本
从日志文件中提取 [czcv_camera]run time: 后面的数字，并统计最大值和平均值
"""

import re
import statistics

def extract_runtime_data(file_path):
    """
    从文件中提取运行时间数据
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        list: 运行时间数据列表
    """
    runtime_values = []
    pattern = r'\[czcv_camera\]run time:\s*([0-9]+\.?[0-9]*)'
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, 1):
                matches = re.findall(pattern, line)
                for match in matches:
                    try:
                        value = float(match)
                        runtime_values.append(value)
                        print(f"第{line_num}行找到运行时间: {value}")
                    except ValueError:
                        print(f"第{line_num}行无法转换为数字: {match}")
                        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []
    
    return runtime_values

def analyze_runtime_data(runtime_values):
    """
    分析运行时间数据
    
    Args:
        runtime_values (list): 运行时间数据列表
        
    Returns:
        dict: 包含统计信息的字典
    """
    if not runtime_values:
        return None
    
    max_value = max(runtime_values)
    min_value = min(runtime_values)
    avg_value = statistics.mean(runtime_values)
    median_value = statistics.median(runtime_values)
    count = len(runtime_values)
    
    return {
        'count': count,
        'max': max_value,
        'min': min_value,
        'average': avg_value,
        'median': median_value
    }

def main():
    """主函数"""
    file_path = r"D:\new 2.txt"
    
    print(f"正在分析文件: {file_path}")
    print("=" * 50)
    
    # 提取运行时间数据
    runtime_values = extract_runtime_data(file_path)
    
    if not runtime_values:
        print("没有找到任何运行时间数据")
        return
    
    print(f"\n总共找到 {len(runtime_values)} 个运行时间数据")
    print("=" * 50)
    
    # 分析数据
    stats = analyze_runtime_data(runtime_values)
    
    if stats:
        print(f"数据统计结果:")
        print(f"  数据总数: {stats['count']}")
        print(f"  最大值: {stats['max']:.6f}")
        print(f"  最小值: {stats['min']:.6f}")
        print(f"  平均值: {stats['average']:.6f}")
        print(f"  中位数: {stats['median']:.6f}")
        
        # 显示前10个和后10个数据作为样本
        print(f"\n前10个数据样本:")
        for i, value in enumerate(runtime_values[:10]):
            print(f"  {i+1}: {value}")
            
        if len(runtime_values) > 10:
            print(f"\n后10个数据样本:")
            for i, value in enumerate(runtime_values[-10:], len(runtime_values)-9):
                print(f"  {i}: {value}")

if __name__ == "__main__":
    main()
