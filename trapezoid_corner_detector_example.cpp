#include "trapezoid_corner_detector.hpp"
#include <iostream>

/**
 * @brief Example usage of TrapezoidCornerDetector class
 * 
 * This example demonstrates how to use the TrapezoidCornerDetector class
 * to detect 4 white trapezoids arranged in a 2x2 grid and extract their corner points.
 */
int main() {
    // Create detector instance with default parameters
    TrapezoidCornerDetector detector;
    
    // You can also customize parameters:
    // TrapezoidCornerDetector detector(1000.0,   // minArea
    //                                 50000.0,   // maxArea  
    //                                 0.02,      // approxEpsilon
    //                                 50.0,      // cannyLow
    //                                 150.0);    // cannyHigh
    
    // Load image containing 4 white trapezoids in 2x2 arrangement
    cv::Mat image = cv::imread("input_image.jpg");
    
    if (image.empty()) {
        std::cerr << "Error: Could not load image!" << std::endl;
        return -1;
    }
    
    // Detect trapezoid corners
    bool success = detector.detectTrapezoidCorners(image);
    
    if (success) {
        std::cout << "Successfully detected 4 trapezoids!" << std::endl;
        
        // Get the corner points
        const TrapezoidGridCorners& corners = detector.getGridCorners();
        
        // Print corner coordinates
        std::cout << "Corner Points:" << std::endl;
        std::cout << "Top-Left:     (" << corners.topLeft.x << ", " << corners.topLeft.y << ")" << std::endl;
        std::cout << "Top-Right:    (" << corners.topRight.x << ", " << corners.topRight.y << ")" << std::endl;
        std::cout << "Bottom-Left:  (" << corners.bottomLeft.x << ", " << corners.bottomLeft.y << ")" << std::endl;
        std::cout << "Bottom-Right: (" << corners.bottomRight.x << ", " << corners.bottomRight.y << ")" << std::endl;
        
        // Get detected trapezoids information
        const std::vector<DetectedTrapezoid>& trapezoids = detector.getDetectedTrapezoids();
        std::cout << "\nDetected Trapezoids:" << std::endl;
        for (size_t i = 0; i < trapezoids.size(); ++i) {
            std::cout << "Trapezoid " << i + 1 << ":" << std::endl;
            std::cout << "  Center: (" << trapezoids[i].center.x << ", " << trapezoids[i].center.y << ")" << std::endl;
            std::cout << "  Area: " << trapezoids[i].area << std::endl;
            std::cout << "  Corners: ";
            for (const auto& corner : trapezoids[i].corners) {
                std::cout << "(" << corner.x << ", " << corner.y << ") ";
            }
            std::cout << std::endl;
        }
        
        // Draw results
        cv::Mat resultImage = detector.drawResults(image, true, true);
        
        // Display results
        cv::imshow("Original Image", image);
        cv::imshow("Detection Results", resultImage);
        
        std::cout << "\nPress any key to exit..." << std::endl;
        cv::waitKey(0);
        cv::destroyAllWindows();
        
    } else {
        std::cerr << "Failed to detect 4 trapezoids in the image!" << std::endl;
        
        // Show how many trapezoids were detected
        const std::vector<DetectedTrapezoid>& trapezoids = detector.getDetectedTrapezoids();
        std::cout << "Number of trapezoids detected: " << trapezoids.size() << std::endl;
        
        // Still show the image with partial results
        cv::Mat resultImage = detector.drawResults(image, true, false);
        cv::imshow("Partial Detection Results", resultImage);
        cv::waitKey(0);
        cv::destroyAllWindows();
        
        return -1;
    }
    
    return 0;
}

/**
 * @brief Alternative usage example showing step-by-step processing
 */
void stepByStepExample() {
    TrapezoidCornerDetector detector(500.0, 10000.0, 0.015);
    
    cv::Mat image = cv::imread("trapezoids.jpg");
    if (image.empty()) return;
    
    // Reset any previous detection
    detector.reset();
    
    // Perform detection
    if (detector.detectTrapezoidCorners(image)) {
        // Check if detection is valid
        if (detector.isDetectionValid()) {
            const TrapezoidGridCorners& corners = detector.getGridCorners();
            
            // Use the corner points for further processing
            // For example, perspective correction, measurement, etc.
            
            // Create a quadrilateral from the corner points
            std::vector<cv::Point2f> srcPoints = {
                corners.topLeft,
                corners.topRight,
                corners.bottomRight,
                corners.bottomLeft
            };
            
            // Define destination points for perspective correction
            std::vector<cv::Point2f> dstPoints = {
                cv::Point2f(0, 0),
                cv::Point2f(300, 0),
                cv::Point2f(300, 300),
                cv::Point2f(0, 300)
            };
            
            // Calculate perspective transformation matrix
            cv::Mat perspectiveMatrix = cv::getPerspectiveTransform(srcPoints, dstPoints);
            
            // Apply perspective correction
            cv::Mat correctedImage;
            cv::warpPerspective(image, correctedImage, perspectiveMatrix, cv::Size(300, 300));
            
            // Display corrected image
            cv::imshow("Perspective Corrected", correctedImage);
            cv::waitKey(0);
            cv::destroyAllWindows();
        }
    }
}
