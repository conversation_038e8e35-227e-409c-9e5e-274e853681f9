#include "curve_fit.hpp"

CurveFit::FitResult CurveFit::curve_fit(
    ModelFunction model,
    const std::vector<double>& x_data,
    const std::vector<double>& y_data,
    const std::vector<double>& initial_guess,
    int max_iterations,
    double tolerance
) {
    FitResult result;
    result.parameters = initial_guess;
    result.success = false;
    
    int n_data = x_data.size();
    int n_params = initial_guess.size();
    
    if (n_data != y_data.size() || n_data < n_params) {
        return result;
    }
    
    double lambda = 0.001;  // Levenberg-Marquardt parameter
    std::vector<double> weights(n_data, 1.0);
    
    for (int iter = 0; iter < max_iterations; ++iter) {
    
        std::vector<double> residuals(n_data);
        for (int i = 0; i < n_data; ++i) {
            residuals[i] = y_data[i] - model(x_data[i], result.parameters);
        }

        if (iter > 0) {
            double epsilon = 1e-6;
            double sum_weights = 0.0;
            for (int i = 0; i < n_data; ++i) {
                weights[i] = 1.0 / (std::abs(residuals[i]) + epsilon);
                sum_weights += weights[i];
            }
            // Normalize weights
            for (int i = 0; i < n_data; ++i) {
                weights[i] = weights[i] / sum_weights * n_data;
            }
        }
        
        auto jacobian = computeJacobian(model, x_data, result.parameters);
        for (int i = 0; i < n_data; ++i) {
            double sqrt_weight = std::sqrt(weights[i]);
            residuals[i] *= sqrt_weight;
            for (int j = 0; j < n_params; ++j) {
                jacobian[i][j] *= sqrt_weight;
            }
        }
        
        auto jt = matrixTranspose(jacobian);
        
        auto jtj = matrixMultiply(jt, jacobian);
        
        for (int i = 0; i < n_params; ++i) {
            jtj[i][i] += lambda;
        }
        
        std::vector<double> jtr(n_params, 0.0);
        for (int i = 0; i < n_params; ++i) {
            for (int j = 0; j < n_data; ++j) {
                jtr[i] += jt[i][j] * residuals[j];
            }
        }
        
        if (!matrixInverse(jtj)) {
            lambda *= 10;
            continue;
        }
        
        std::vector<double> delta(n_params, 0.0);
        for (int i = 0; i < n_params; ++i) {
            for (int j = 0; j < n_params; ++j) {
                delta[i] += jtj[i][j] * jtr[j];
            }
        }
        
        std::vector<double> new_params = result.parameters;
        for (int i = 0; i < n_params; ++i) {
            new_params[i] += delta[i];
        }
        
        double old_error = 0.0, new_error = 0.0;
        for (int i = 0; i < n_data; ++i) {
            double old_res = y_data[i] - model(x_data[i], result.parameters);
            double new_res = y_data[i] - model(x_data[i], new_params);
            old_error += old_res * old_res;
            new_error += new_res * new_res;
        }
        
        if (std::abs(old_error - new_error) < tolerance) {
            result.parameters = new_params;
            result.success = true;
            break;
        }
        result.parameters = new_params;
        //if (new_error < old_error) {
        //    result.parameters = new_params;
        //    lambda /= 10;
        //} else {
        //    lambda *= 10;
        //}
    }
    
    double sum_sq = 0.0;
    for (int i = 0; i < n_data; ++i) {
        double res = y_data[i] - model(x_data[i], result.parameters);
        sum_sq += res * res;
    }
    result.rmse = std::sqrt(sum_sq / n_data);
    
    return result;
}

std::vector<std::vector<double>> CurveFit::computeJacobian(
    ModelFunction model,
    const std::vector<double>& x_data,
    const std::vector<double>& parameters,
    double h
) {
    int n_data = x_data.size();
    int n_params = parameters.size();
    std::vector<std::vector<double>> jacobian(n_data, std::vector<double>(n_params));
    
    for (int j = 0; j < n_params; ++j) {
        std::vector<double> params_plus = parameters;
        std::vector<double> params_minus = parameters;
        params_plus[j] += h;
        params_minus[j] -= h;
        
        for (int i = 0; i < n_data; ++i) {
            double f_plus = model(x_data[i], params_plus);
            double f_minus = model(x_data[i], params_minus);
            jacobian[i][j] = (f_plus - f_minus) / (2.0 * h);
        }
    }
    
    return jacobian;
}

std::vector<std::vector<double>> CurveFit::matrixMultiply(
    const std::vector<std::vector<double>>& A,
    const std::vector<std::vector<double>>& B
) {
    int rows_A = A.size();
    int cols_A = A[0].size();
    int cols_B = B[0].size();
    
    std::vector<std::vector<double>> result(rows_A, std::vector<double>(cols_B, 0.0));
    
    for (int i = 0; i < rows_A; ++i) {
        for (int j = 0; j < cols_B; ++j) {
            for (int k = 0; k < cols_A; ++k) {
                result[i][j] += A[i][k] * B[k][j];
            }
        }
    }
    
    return result;
}

std::vector<std::vector<double>> CurveFit::matrixTranspose(
    const std::vector<std::vector<double>>& matrix
) {
    int rows = matrix.size();
    int cols = matrix[0].size();
    std::vector<std::vector<double>> result(cols, std::vector<double>(rows));
    
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            result[j][i] = matrix[i][j];
        }
    }
    
    return result;
}

bool CurveFit::matrixInverse(std::vector<std::vector<double>>& matrix) {
    int n = matrix.size();
    std::vector<std::vector<double>> augmented(n, std::vector<double>(2 * n, 0.0));
    
    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < n; ++j) {
            augmented[i][j] = matrix[i][j];
        }
        augmented[i][i + n] = 1.0;
    }
    
    for (int i = 0; i < n; ++i) {
        int pivot = i;
        for (int j = i + 1; j < n; ++j) {
            if (std::abs(augmented[j][i]) > std::abs(augmented[pivot][i])) {
                pivot = j;
            }
        }
        
        if (std::abs(augmented[pivot][i]) < 1e-12) {
            return false; 
        }
        
        if (pivot != i) {
            std::swap(augmented[i], augmented[pivot]);
        }
        
        double pivot_val = augmented[i][i];
        for (int j = 0; j < 2 * n; ++j) {
            augmented[i][j] /= pivot_val;
        }
        

        for (int j = 0; j < n; ++j) {
            if (j != i) {
                double factor = augmented[j][i];
                for (int k = 0; k < 2 * n; ++k) {
                    augmented[j][k] -= factor * augmented[i][k];
                }
            }
        }
    }
    

    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < n; ++j) {
            matrix[i][j] = augmented[i][j + n];
        }
    }
    
    return true;
}