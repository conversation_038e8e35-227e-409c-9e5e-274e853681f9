# Video Frame Viewer

一个使用Python OpenCV实现的视频帧查看器，可以逐帧查看MP4等视频文件。

## 功能特性

- 📹 支持多种视频格式（MP4, AVI, MOV, MKV等）
- 🎯 可设置从指定帧开始显示
- ⌨️ 简单的键盘控制
- 📊 显示当前帧号和总帧数
- 🔄 支持重置和跳转功能

## 安装依赖

```bash
pip install opencv-python
```

## 使用方法

### 命令行使用

```bash
# 基本使用 - 从第0帧开始
python video_frame_viewer.py your_video.mp4

# 从指定帧开始（例如第100帧）
python video_frame_viewer.py your_video.mp4 --start_frame 100
```

### 代码中使用

```python
from video_frame_viewer import VideoFrameViewer

# 创建查看器实例
viewer = VideoFrameViewer("your_video.mp4", start_frame=50)

# 运行查看器
viewer.run()
```

## 操作说明

| 按键 | 功能 |
|------|------|
| `SPACE` | 切换到下一帧 |
| `ESC` | 退出程序 |
| `r` | 重置到起始帧 |
| `j` | 跳转到指定帧号 |

## 示例

### 1. 查看视频的前100帧

```bash
python video_frame_viewer.py video.mp4 --start_frame 0
```

### 2. 从第500帧开始查看

```bash
python video_frame_viewer.py video.mp4 --start_frame 500
```

### 3. 创建测试视频并查看

```bash
# 运行示例脚本创建测试视频
python video_viewer_example.py

# 查看创建的测试视频
python video_frame_viewer.py test_video.mp4
```

## 程序输出示例

```
视频信息:
  文件路径: example.mp4
  总帧数: 1500
  帧率: 30.00 FPS
  分辨率: 1920x1080
  起始帧: 100

操作说明:
  SPACE - 下一帧
  ESC   - 退出
  r     - 重置到起始帧
  j     - 跳转到指定帧
```

## 类说明

### VideoFrameViewer

主要的视频帧查看器类。

#### 构造函数

```python
VideoFrameViewer(video_path, start_frame=0)
```

- `video_path`: 视频文件路径
- `start_frame`: 起始帧号（默认为0）

#### 主要方法

- `run()`: 运行查看器
- `jump_to_frame(frame_number)`: 跳转到指定帧
- `next_frame()`: 切换到下一帧
- `reset_to_start()`: 重置到起始帧

## 注意事项

1. 确保安装了OpenCV: `pip install opencv-python`
2. 支持的视频格式取决于OpenCV的编译配置
3. 大视频文件可能需要较长的加载时间
4. 跳转功能在某些视频格式上可能不够精确

## 错误处理

程序包含完善的错误处理：

- 检查视频文件是否存在
- 验证起始帧号是否有效
- 处理视频读取失败的情况
- 处理用户输入错误

## 扩展功能

可以轻松扩展以下功能：

- 添加播放/暂停功能
- 支持快进/快退
- 添加帧导出功能
- 支持视频信息显示
- 添加缩放功能

## 许可证

此项目为开源项目，可自由使用和修改。
