# PowerShell script to build simulate_center_stage.exe on Windows
# Usage: .\Build-SimulateCenterStage.ps1

param(
    [string]$BuildType = "Release",
    [string]$Generator = "Visual Studio 16 2019",
    [switch]$DownloadOpenCV = $false,
    [switch]$Clean = $false
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Windows Build Script for simulate_center_stage" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set variables
$ProjectRoot = Get-Location
$BuildDir = "build_windows_ps"
$OutputDir = "output\windows_ps"
$OpenCVVersion = "4.5.1"
$OpenCVUrl = "https://github.com/opencv/opencv/releases/download/4.5.1/opencv-4.5.1-vc14_vc15.exe"

# Function to check if a command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Function to download and extract OpenCV
function Install-OpenCV {
    Write-Host "Downloading OpenCV $OpenCVVersion..." -ForegroundColor Yellow

    $OpenCVDir = "third_party\prebuilt\windows\opencv4.5.1"
    $TempDir = "temp_opencv"

    if (!(Test-Path $TempDir)) {
        New-Item -ItemType Directory -Path $TempDir | Out-Null
    }

    $OpenCVExe = "$TempDir\opencv-4.5.1-vc14_vc15.exe"

    try {
        Invoke-WebRequest -Uri $OpenCVUrl -OutFile $OpenCVExe -UseBasicParsing
        Write-Host "OpenCV downloaded successfully." -ForegroundColor Green

        Write-Host "Extracting OpenCV..." -ForegroundColor Yellow
        Start-Process -FilePath $OpenCVExe -ArgumentList "-o$TempDir", "-y" -Wait

        # Move extracted files to the correct location
        if (!(Test-Path "third_party\prebuilt\windows")) {
            New-Item -ItemType Directory -Path "third_party\prebuilt\windows" -Force | Out-Null
        }

        if (Test-Path "$TempDir\opencv") {
            Move-Item "$TempDir\opencv" $OpenCVDir -Force
            Write-Host "OpenCV installed to $OpenCVDir" -ForegroundColor Green
        }

        # Cleanup
        Remove-Item $TempDir -Recurse -Force
    }
    catch {
        Write-Host "Failed to download/extract OpenCV: $_" -ForegroundColor Red
        return $false
    }

    return $true
}

# Function to build glog from source
function Build-GlogFromSource {
    Write-Host "Building glog from source..." -ForegroundColor Yellow

    $GlogSourceDir = "third_party\source\glog-0.3.5"
    $GlogBuildDir = "build_glog_temp"
    $GlogInstallDir = "third_party\prebuilt\windows\glog"

    if (!(Test-Path $GlogSourceDir)) {
        Write-Host "glog source not found at: $GlogSourceDir" -ForegroundColor Red
        return $false
    }

    Write-Host "Source directory: $GlogSourceDir" -ForegroundColor Cyan
    Write-Host "Build directory: $GlogBuildDir" -ForegroundColor Cyan
    Write-Host "Install directory: $GlogInstallDir" -ForegroundColor Cyan

    # Clean previous build
    if (Test-Path $GlogBuildDir) {
        Write-Host "Cleaning previous glog build..." -ForegroundColor Yellow
        Remove-Item $GlogBuildDir -Recurse -Force
    }

    # Create build directory
    New-Item -ItemType Directory -Path $GlogBuildDir | Out-Null
    Set-Location $GlogBuildDir

    try {
        Write-Host "Configuring glog with CMake..." -ForegroundColor Yellow

        $CMakeArgs = @(
            "..\$GlogSourceDir",
            "-G", $Generator,
            "-A", "x64",
            "-DCMAKE_BUILD_TYPE=Release",
            "-DCMAKE_INSTALL_PREFIX=..\$GlogInstallDir",
            "-DBUILD_TESTING=OFF",
            "-DBUILD_SHARED_LIBS=OFF",
            "-DWITH_GFLAGS=OFF",
            "-DWITH_THREADS=ON",
            "-DWITH_TLS=ON",
            "-DCMAKE_CXX_FLAGS=/MT",
            "-DCMAKE_C_FLAGS=/MT"
        )

        & cmake @CMakeArgs
        if ($LASTEXITCODE -ne 0) {
            throw "CMake configuration failed"
        }

        Write-Host "Building glog (this may take 5-10 minutes)..." -ForegroundColor Yellow

        & cmake --build . --config Release --parallel 4
        if ($LASTEXITCODE -ne 0) {
            throw "glog build failed"
        }

        Write-Host "Installing glog..." -ForegroundColor Yellow
        & cmake --build . --config Release --target install
        if ($LASTEXITCODE -ne 0) {
            throw "glog installation failed"
        }

        Set-Location $ProjectRoot

        # Verify installation
        if (Test-Path "$GlogInstallDir\include\glog\logging.h") {
            Write-Host "glog built and installed successfully!" -ForegroundColor Green
            Write-Host "Installation location: $GlogInstallDir" -ForegroundColor Green
        } else {
            throw "glog installation verification failed"
        }

        # Clean up build directory
        Write-Host "Cleaning up temporary build directory..." -ForegroundColor Yellow
        Remove-Item $GlogBuildDir -Recurse -Force

        return $true
    }
    catch {
        Set-Location $ProjectRoot
        Write-Host "Failed to build glog from source: $_" -ForegroundColor Red
        return $false
    }
}

# Function to build OpenCV from source
function Build-OpenCVFromSource {
    Write-Host "Building OpenCV from source..." -ForegroundColor Yellow

    $OpenCVSourceDir = "third_party\source\opencv-4.5.1"
    $OpenCVBuildDir = "build_opencv_temp"
    $OpenCVInstallDir = "third_party\prebuilt\windows\opencv4.5.1"

    if (!(Test-Path $OpenCVSourceDir)) {
        Write-Host "OpenCV source not found at: $OpenCVSourceDir" -ForegroundColor Red
        return $false
    }

    Write-Host "Source directory: $OpenCVSourceDir" -ForegroundColor Cyan
    Write-Host "Build directory: $OpenCVBuildDir" -ForegroundColor Cyan
    Write-Host "Install directory: $OpenCVInstallDir" -ForegroundColor Cyan

    # Clean previous build
    if (Test-Path $OpenCVBuildDir) {
        Write-Host "Cleaning previous OpenCV build..." -ForegroundColor Yellow
        Remove-Item $OpenCVBuildDir -Recurse -Force
    }

    # Create build directory
    New-Item -ItemType Directory -Path $OpenCVBuildDir | Out-Null
    Set-Location $OpenCVBuildDir

    try {
        Write-Host "Configuring OpenCV with CMake..." -ForegroundColor Yellow

        $CMakeArgs = @(
            "..\$OpenCVSourceDir",
            "-G", $Generator,
            "-A", "x64",
            "-DCMAKE_BUILD_TYPE=Release",
            "-DCMAKE_INSTALL_PREFIX=..\$OpenCVInstallDir",
            "-DBUILD_TESTS=OFF",
            "-DBUILD_PERF_TESTS=OFF",
            "-DBUILD_EXAMPLES=OFF",
            "-DBUILD_DOCS=OFF",
            "-DBUILD_opencv_apps=OFF",
            "-DBUILD_opencv_python2=OFF",
            "-DBUILD_opencv_python3=OFF",
            "-DWITH_CUDA=OFF",
            "-DWITH_OPENCL=ON",
            "-DWITH_TBB=ON",
            "-DWITH_IPP=ON",
            "-DBUILD_SHARED_LIBS=OFF",
            "-DCMAKE_CXX_FLAGS=/MP",
            "-DCMAKE_C_FLAGS=/MP"
        )

        & cmake @CMakeArgs
        if ($LASTEXITCODE -ne 0) {
            throw "CMake configuration failed"
        }

        Write-Host "Building OpenCV (this may take 20-60 minutes)..." -ForegroundColor Yellow
        Write-Host "Please be patient..." -ForegroundColor Yellow

        & cmake --build . --config Release --parallel 4
        if ($LASTEXITCODE -ne 0) {
            throw "OpenCV build failed"
        }

        Write-Host "Installing OpenCV..." -ForegroundColor Yellow
        & cmake --build . --config Release --target install
        if ($LASTEXITCODE -ne 0) {
            throw "OpenCV installation failed"
        }

        Set-Location $ProjectRoot

        # Verify installation
        if (Test-Path "$OpenCVInstallDir\include\opencv2\opencv.hpp") {
            Write-Host "OpenCV built and installed successfully!" -ForegroundColor Green
            Write-Host "Installation location: $OpenCVInstallDir" -ForegroundColor Green
        } else {
            throw "OpenCV installation verification failed"
        }

        # Clean up build directory
        Write-Host "Cleaning up temporary build directory..." -ForegroundColor Yellow
        Remove-Item $OpenCVBuildDir -Recurse -Force

        return $true
    }
    catch {
        Set-Location $ProjectRoot
        Write-Host "Failed to build OpenCV from source: $_" -ForegroundColor Red
        return $false
    }
}

# Check prerequisites
Write-Host "Checking build environment..." -ForegroundColor Yellow

# Check CMake
if (!(Test-Command "cmake")) {
    Write-Host "[ERROR] CMake not found! Please install CMake and add it to PATH." -ForegroundColor Red
    Write-Host "Download from: https://cmake.org/download/" -ForegroundColor Yellow
    exit 1
}
Write-Host "[OK] CMake found" -ForegroundColor Green

# Check Visual Studio
$VSFound = $false
$VSPaths = @(
    "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat",
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat",
    "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat",
    "C:\Program Files\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
)

foreach ($VSPath in $VSPaths) {
    if (Test-Path $VSPath) {
        Write-Host "[OK] Visual Studio found at: $VSPath" -ForegroundColor Green
        $VSFound = $true
        break
    }
}

if (!$VSFound) {
    Write-Host "[ERROR] Visual Studio not found! Please install Visual Studio 2019 or later." -ForegroundColor Red
    Write-Host "Download from: https://visualstudio.microsoft.com/downloads/" -ForegroundColor Yellow
    exit 1
}

# Check dependencies
Write-Host ""
Write-Host "Checking dependencies..." -ForegroundColor Yellow

$OpenCVPath = "third_party\prebuilt\windows\opencv4.5.1"
if (!(Test-Path $OpenCVPath)) {
    Write-Host "[WARNING] OpenCV Windows prebuilt libraries not found at: $OpenCVPath" -ForegroundColor Yellow

    if ($DownloadOpenCV) {
        if (!(Install-OpenCV)) {
            Write-Host "[ERROR] Failed to download and install OpenCV." -ForegroundColor Red
            exit 1
        }
    } else {
        # Check if OpenCV source is available
        $OpenCVSourcePath = "third_party\source\opencv-4.5.1"
        if (Test-Path $OpenCVSourcePath) {
            Write-Host "[INFO] OpenCV source found. Building OpenCV from source..." -ForegroundColor Cyan
            if (!(Build-OpenCVFromSource)) {
                Write-Host "[ERROR] Failed to build OpenCV from source." -ForegroundColor Red
                exit 1
            }
        } else {
            Write-Host "Options:" -ForegroundColor Yellow
            Write-Host "1. Use -DownloadOpenCV switch to automatically download OpenCV" -ForegroundColor Yellow
            Write-Host "2. Manually download and extract OpenCV to: $OpenCVPath" -ForegroundColor Yellow
            Write-Host "3. Ensure OpenCV source is available at: $OpenCVSourcePath" -ForegroundColor Yellow
            exit 1
        }
    }
}

if (Test-Path $OpenCVPath) {
    Write-Host "[OK] OpenCV found" -ForegroundColor Green
}

# Check glog
$GlogPath = "third_party\prebuilt\windows\glog"
if (!(Test-Path $GlogPath)) {
    Write-Host "[WARNING] glog Windows prebuilt libraries not found at: $GlogPath" -ForegroundColor Yellow

    # Check if glog source is available
    $GlogSourcePath = "third_party\source\glog-0.3.5"
    if (Test-Path $GlogSourcePath) {
        Write-Host "[INFO] glog source found. Building glog from source..." -ForegroundColor Cyan
        if (!(Build-GlogFromSource)) {
            Write-Host "[ERROR] Failed to build glog from source." -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "[WARNING] glog source not found at: $GlogSourcePath" -ForegroundColor Yellow
        Write-Host "This may cause build issues." -ForegroundColor Yellow
    }
}

if (Test-Path $GlogPath) {
    Write-Host "[OK] glog found" -ForegroundColor Green
}

# Clean previous build if requested
if ($Clean -and (Test-Path $BuildDir)) {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    Remove-Item $BuildDir -Recurse -Force
}

# Create directories
if (!(Test-Path $BuildDir)) {
    New-Item -ItemType Directory -Path $BuildDir | Out-Null
}

if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Starting Build Process" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Configure CMake
Write-Host ""
Write-Host "Configuring CMake..." -ForegroundColor Yellow
Set-Location $BuildDir

$CMakeArgs = @(
    "..",
    "-G", $Generator,
    "-A", "x64",
    "-DCMAKE_BUILD_TYPE=$BuildType",
    "-DDst_Platform=windows",
    "-DBUILD_TESTS=ON",
    "-DBUILD_BENCHMARK=OFF",
    "-DBUILD_Release=ON",
    "-DBUILD_Shared=ON"
)

$CMakeResult = & cmake @CMakeArgs
if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] CMake configuration failed!" -ForegroundColor Red
    Set-Location $ProjectRoot
    exit 1
}

# Build the project
Write-Host ""
Write-Host "Building simulate_center_stage..." -ForegroundColor Yellow
$BuildResult = & cmake --build . --config $BuildType --target simulate_center_stage

if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] Build failed!" -ForegroundColor Red
    Set-Location $ProjectRoot
    exit 1
}

Set-Location $ProjectRoot

# Check results
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Build Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$ExePaths = @(
    "$BuildDir\test\$BuildType\simulate_center_stage.exe",
    "$OutputDir\$BuildType\simulate_center_stage.exe",
    "$BuildDir\test\simulate_center_stage.exe"
)

$ExeFound = $false
foreach ($ExePath in $ExePaths) {
    if (Test-Path $ExePath) {
        Write-Host "[SUCCESS] simulate_center_stage.exe built successfully!" -ForegroundColor Green
        Write-Host "Location: $ExePath" -ForegroundColor Green
        
        $FileInfo = Get-Item $ExePath
        $SizeMB = [math]::Round($FileInfo.Length / 1MB, 2)
        Write-Host "File size: $($FileInfo.Length) bytes (~$SizeMB MB)" -ForegroundColor Green
        
        $ExeFound = $true
        break
    }
}

if (!$ExeFound) {
    Write-Host "[ERROR] simulate_center_stage.exe not found!" -ForegroundColor Red
    Write-Host "Please check the build log for errors." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Build completed at: $(Get-Date)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
