#include <iostream>
#include <vector>
#include <opencv2/opencv.hpp>
#include "../lib/include/tracker/base_tracker.h"
#include "../lib/include/base/bbox.h"

using namespace czcv_camera;

// 简单的测试函数来验证手势过滤功能
void testGestureFilter()
{
    std::cout << "Testing gesture filter functionality..." << std::endl;
    
    // 创建测试用的TrackerInputOutput对象
    TrackerInputOutput inputOutput;
    
    // 创建一个虚拟的图像
    cv::Mat testImage = cv::Mat::zeros(480, 640, CV_8UC3);
    inputOutput.ref_frame_to(testImage);
    
    // 添加一些测试用的人体检测框（类别为1）
    BboxF personBox1(100, 100, 200, 300, 0.8f, 1, 1);  // 人体检测框
    BboxF personBox2(300, 150, 400, 350, 0.9f, 1, 2);  // 另一个人体检测框
    inputOutput.push_in_bbox(personBox1);
    inputOutput.push_in_bbox(personBox2);
    
    // 添加一些测试用的手势检测结果
    std::vector<stGestureRecResult>& gestureResults = inputOutput.gestureResults();
    
    // 手势1：类别0（PALM），与personBox1有重叠，应该被删除
    stGestureRecResult gesture1;
    gesture1.clsid = 0;  // PALM
    gesture1.rectf.x0 = 120;
    gesture1.rectf.y0 = 120;
    gesture1.rectf.x1 = 180;
    gesture1.rectf.y1 = 180;
    gesture1.palm_score = 0.7f;
    gesture1.det_instance_id = 1;
    gestureResults.push_back(gesture1);
    
    // 手势2：类别1（STOP），与personBox2有重叠，应该被删除
    stGestureRecResult gesture2;
    gesture2.clsid = 1;  // STOP
    gesture2.rectf.x0 = 320;
    gesture2.rectf.y0 = 170;
    gesture2.rectf.x1 = 380;
    gesture2.rectf.y1 = 230;
    gesture2.palm_score = 0.8f;
    gesture2.det_instance_id = 2;
    gestureResults.push_back(gesture2);
    
    // 手势3：类别0（PALM），与人体检测框没有重叠，应该被保留
    stGestureRecResult gesture3;
    gesture3.clsid = 0;  // PALM
    gesture3.rectf.x0 = 500;
    gesture3.rectf.y0 = 50;
    gesture3.rectf.x1 = 560;
    gesture3.rectf.y1 = 110;
    gesture3.palm_score = 0.6f;
    gesture3.det_instance_id = 3;
    gestureResults.push_back(gesture3);
    
    // 手势4：类别2（其他手势），即使有重叠也应该被保留
    stGestureRecResult gesture4;
    gesture4.clsid = 2;  // 其他手势类别
    gesture4.rectf.x0 = 150;
    gesture4.rectf.y0 = 150;
    gesture4.rectf.x1 = 190;
    gesture4.rectf.y1 = 190;
    gesture4.palm_score = 0.9f;
    gesture4.det_instance_id = 4;
    gestureResults.push_back(gesture4);
    
    std::cout << "Before filtering: " << gestureResults.size() << " gestures" << std::endl;
    for (size_t i = 0; i < gestureResults.size(); i++)
    {
        const auto& g = gestureResults[i];
        std::cout << "  Gesture " << i << ": class=" << g.clsid 
                  << " rect=(" << g.rectf.x0 << "," << g.rectf.y0 
                  << "," << g.rectf.x1 << "," << g.rectf.y1 << ")" << std::endl;
    }
    
    // 这里我们无法直接调用filterGesturesByPersonBoxIOU，因为它是私有方法
    // 在实际使用中，这个方法会在Yolov10RKNN::run中被调用
    
    std::cout << "Test setup completed. In actual usage, filterGesturesByPersonBoxIOU would be called." << std::endl;
    std::cout << "Expected result: gestures 0 and 1 should be removed, gestures 2 and 3 should remain." << std::endl;
}

int main()
{
    testGestureFilter();
    return 0;
}
