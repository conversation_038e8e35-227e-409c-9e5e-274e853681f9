@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Windows Build Script for simulate_center_stage
echo ========================================
echo.

REM Set project variables
set PROJECT_NAME=czcv_camera
set TARGET_PLATFORM=windows
set BUILD_TYPE=Release
set BUILD_DIR=build_windows
set OUTPUT_DIR=output\windows

REM Check for required tools
echo Checking build environment...

REM Check for CMake
cmake --version >nul 2>&1
if !errorlevel! neq 0 (
    echo [ERROR] CMake not found! Please install CMake and add it to PATH.
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)
echo [OK] CMake found

REM Check for Visual Studio
where cl >nul 2>&1
if !errorlevel! neq 0 (
    echo [WARNING] Visual Studio compiler not found in PATH
    echo Attempting to locate Visual Studio...
    
    REM Try to find and setup Visual Studio environment
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        echo [INFO] Found Visual Studio 2022 Community
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        echo [INFO] Found Visual Studio 2022 Professional
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        echo [INFO] Found Visual Studio 2019 Community
        call "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        echo [INFO] Found Visual Studio 2019 Professional
        call "C:\Program Files\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo [ERROR] Visual Studio not found! Please install Visual Studio 2019 or later.
        echo Download from: https://visualstudio.microsoft.com/downloads/
        pause
        exit /b 1
    )
)
echo [OK] Visual Studio compiler available

REM Check for required dependencies
echo.
echo Checking dependencies...

REM Check for Windows OpenCV prebuilt libraries
if not exist "third_party\prebuilt\windows\opencv4.5.1" (
    echo [WARNING] OpenCV 4.5.1 Windows prebuilt libraries not found!
    echo Expected location: third_party\prebuilt\windows\opencv4.5.1\
    echo.
    echo Checking for OpenCV source code...
    if exist "third_party\source\opencv-4.5.1" (
        echo [INFO] OpenCV source found. Will compile OpenCV from source.
        call :build_opencv_from_source
        if !errorlevel! neq 0 (
            echo [ERROR] Failed to build OpenCV from source!
            pause
            exit /b 1
        )
    ) else (
        echo [ERROR] OpenCV source not found at third_party\source\opencv-4.5.1\
        echo.
        echo Please either:
        echo   1. Download OpenCV 4.5.1 Windows prebuilt libraries and extract to:
        echo      third_party\prebuilt\windows\opencv4.5.1\
        echo   2. Or ensure OpenCV source is available at:
        echo      third_party\source\opencv-4.5.1\
        echo.
        echo You can download from: https://opencv.org/releases/
        echo.
        pause
        exit /b 1
    )
)
echo [OK] OpenCV 4.5.1 found

if not exist "third_party\prebuilt\windows\glog" (
    echo [WARNING] glog Windows libraries not found at third_party\prebuilt\windows\glog\
    echo.
    echo Checking for glog source code...
    if exist "third_party\source\glog-0.3.5" (
        echo [INFO] glog source found. Will compile glog from source.
        call :build_glog_from_source
        if !errorlevel! neq 0 (
            echo [ERROR] Failed to build glog from source!
            pause
            exit /b 1
        )
    ) else (
        echo [WARNING] glog source not found at third_party\source\glog-0.3.5\
        echo This may cause build issues.
    )
)


echo.
echo ========================================
echo Starting Build Process
echo ========================================

REM Create build directory
if exist "%BUILD_DIR%" (
    echo Cleaning previous build directory...
    rmdir /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

REM Create output directory
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
    mkdir "%OUTPUT_DIR%\bin"
    mkdir "%OUTPUT_DIR%\libs"
)

echo.
echo Configuring CMake...
cd "%BUILD_DIR%"

REM Configure with CMake
cmake .. ^
    -G "Visual Studio 16 2019" ^
    -A x64 ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DDst_Platform=%TARGET_PLATFORM% ^
    -DBUILD_TESTS=ON ^
    -DBUILD_BENCHMARK=OFF ^
    -DBUILD_Release=ON ^
    -DBUILD_Shared=ON ^
    -DCMAKE_LIBRARY_OUTPUT_DIRECTORY="%CD%\..\%OUTPUT_DIR%\libs" ^
    -DCMAKE_RUNTIME_OUTPUT_DIRECTORY="%CD%\..\%OUTPUT_DIR%\bin"

if !errorlevel! neq 0 (
    echo [ERROR] CMake configuration failed!
    cd ..
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config %BUILD_TYPE% --target simulate_center_stage

if !errorlevel! neq 0 (
    echo [ERROR] Build failed!
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo ========================================
echo Build Summary
echo ========================================

REM Check if executable was created
if exist "%OUTPUT_DIR%\bin\%BUILD_TYPE%\simulate_center_stage.exe" (
    echo [SUCCESS] simulate_center_stage.exe built successfully!
    echo.
    echo Executable location: %OUTPUT_DIR%\bin\%BUILD_TYPE%\simulate_center_stage.exe
    
    REM Get file size
    for %%A in ("%OUTPUT_DIR%\bin\%BUILD_TYPE%\simulate_center_stage.exe") do set size=%%~zA
    set /a sizeMB=!size!/1024/1024
    echo File size: !size! bytes (~!sizeMB! MB)
    
    echo.
    echo Library dependencies:
    if exist "%OUTPUT_DIR%\libs\%BUILD_TYPE%\czcv_camera.dll" (
        echo   [OK] czcv_camera.dll
    ) else if exist "%OUTPUT_DIR%\libs\%BUILD_TYPE%\czcv_camera.lib" (
        echo   [OK] czcv_camera.lib (static)
    ) else (
        echo   [WARNING] czcv_camera library not found
    )
    
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo To run the executable:
    echo   cd %OUTPUT_DIR%\bin\%BUILD_TYPE%
    echo   simulate_center_stage.exe
    echo.
    echo Note: Make sure all required DLL files are in the same directory
    echo or in your system PATH before running the executable.
    
) else if exist "%OUTPUT_DIR%\bin\simulate_center_stage.exe" (
    echo [SUCCESS] simulate_center_stage.exe built successfully!
    echo Executable location: %OUTPUT_DIR%\bin\simulate_center_stage.exe
) else (
    echo [ERROR] simulate_center_stage.exe not found!
    echo Expected locations:
    echo   %OUTPUT_DIR%\bin\%BUILD_TYPE%\simulate_center_stage.exe
    echo   %OUTPUT_DIR%\bin\simulate_center_stage.exe
    echo.
    echo Please check the build log for errors.
)

echo.
echo Build completed at: %date% %time%
echo ========================================
pause
goto :eof

REM Function to build OpenCV from source
:build_opencv_from_source
echo.
echo ========================================
echo Building OpenCV from Source
echo ========================================
echo.

set OPENCV_SOURCE_DIR=third_party\source\opencv-4.5.1
set OPENCV_BUILD_DIR=build_opencv_temp
set OPENCV_INSTALL_DIR=third_party\prebuilt\windows\opencv4.5.1

echo Source directory: %OPENCV_SOURCE_DIR%
echo Build directory: %OPENCV_BUILD_DIR%
echo Install directory: %OPENCV_INSTALL_DIR%
echo.

REM Clean previous build if exists
if exist "%OPENCV_BUILD_DIR%" (
    echo Cleaning previous OpenCV build...
    rmdir /s /q "%OPENCV_BUILD_DIR%"
)

REM Create build directory
mkdir "%OPENCV_BUILD_DIR%"
cd "%OPENCV_BUILD_DIR%"

echo Configuring OpenCV with CMake...
cmake "%CD%\..\%OPENCV_SOURCE_DIR%" ^
    -G "Visual Studio 16 2019" ^
    -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX="%CD%\..\%OPENCV_INSTALL_DIR%" ^
    -DBUILD_TESTS=OFF ^
    -DBUILD_PERF_TESTS=OFF ^
    -DBUILD_EXAMPLES=OFF ^
    -DBUILD_DOCS=OFF ^
    -DBUILD_opencv_apps=OFF ^
    -DBUILD_opencv_python2=OFF ^
    -DBUILD_opencv_python3=OFF ^
    -DWITH_CUDA=OFF ^
    -DWITH_OPENCL=OFF ^
    -DWITH_TBB=OFF ^
    -DWITH_IPP=OFF ^
    -DWITH_EIGEN=OFF ^
    -DBUILD_SHARED_LIBS=OFF ^
    -DCMAKE_CXX_FLAGS="/MP" ^
    -DCMAKE_C_FLAGS="/MP"

if !errorlevel! neq 0 (
    echo [ERROR] OpenCV CMake configuration failed!
    cd ..
    exit /b 1
)

echo.
echo Building OpenCV (this may take 20-60 minutes)...
echo Please be patient...
cmake --build . --config Release --parallel 4

if !errorlevel! neq 0 (
    echo [ERROR] OpenCV build failed!
    cd ..
    exit /b 1
)

echo.
echo Installing OpenCV...
cmake --build . --config Release --target install

if !errorlevel! neq 0 (
    echo [ERROR] OpenCV installation failed!
    cd ..
    exit /b 1
)

cd ..

REM Verify installation
if exist "%OPENCV_INSTALL_DIR%\include\opencv2\opencv.hpp" (
    echo [SUCCESS] OpenCV built and installed successfully!
    echo Installation location: %OPENCV_INSTALL_DIR%
) else (
    echo [ERROR] OpenCV installation verification failed!
    exit /b 1
)

REM Clean up build directory
echo Cleaning up temporary build directory...
rmdir /s /q "%OPENCV_BUILD_DIR%"

echo OpenCV build completed successfully.
exit /b 0

REM Function to build glog from source
:build_glog_from_source
echo.
echo ========================================
echo Building glog from Source
echo ========================================
echo.

set GLOG_SOURCE_DIR=third_party\source\glog-0.3.5
set GLOG_BUILD_DIR=build_glog_temp
set GLOG_INSTALL_DIR=third_party\prebuilt\windows\glog

echo Source directory: %GLOG_SOURCE_DIR%
echo Build directory: %GLOG_BUILD_DIR%
echo Install directory: %GLOG_INSTALL_DIR%
echo.

REM Clean previous build if exists
if exist "%GLOG_BUILD_DIR%" (
    echo Cleaning previous glog build...
    rmdir /s /q "%GLOG_BUILD_DIR%"
)

REM Create build directory
mkdir "%GLOG_BUILD_DIR%"
cd "%GLOG_BUILD_DIR%"

echo Configuring glog with CMake...
cmake "%CD%\..\%GLOG_SOURCE_DIR%" ^
    -G "Visual Studio 16 2019" ^
    -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX="%CD%\..\%GLOG_INSTALL_DIR%" ^
    -DBUILD_TESTING=OFF ^
    -DBUILD_SHARED_LIBS=OFF ^
    -DWITH_GFLAGS=OFF ^
    -DWITH_THREADS=ON ^
    -DWITH_TLS=ON ^
    -DCMAKE_CXX_FLAGS="/MT" ^
    -DCMAKE_C_FLAGS="/MT"

if !errorlevel! neq 0 (
    echo [ERROR] glog CMake configuration failed!
    cd ..
    exit /b 1
)

echo.
echo Building glog (this may take 5-10 minutes)...
cmake --build . --config Release --parallel 4

if !errorlevel! neq 0 (
    echo [ERROR] glog build failed!
    cd ..
    exit /b 1
)

echo.
echo Installing glog...
cmake --build . --config Release --target install

if !errorlevel! neq 0 (
    echo [ERROR] glog installation failed!
    cd ..
    exit /b 1
)

cd ..

REM Verify installation
if exist "%GLOG_INSTALL_DIR%\include\glog\logging.h" (
    echo [SUCCESS] glog built and installed successfully!
    echo Installation location: %GLOG_INSTALL_DIR%
) else (
    echo [ERROR] glog installation verification failed!
    exit /b 1
)

REM Clean up build directory
echo Cleaning up temporary build directory...
rmdir /s /q "%GLOG_BUILD_DIR%"

echo glog build completed successfully.
exit /b 0
