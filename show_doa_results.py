import pandas as pd
import numpy as np

def show_doa_results():
    """
    显示DOA偏移量计算结果
    """
    try:
        # 读取结果文件
        df = pd.read_csv('doa_offset_results.csv')
        
        print("=" * 60)
        print("DOA偏移量计算结果")
        print("=" * 60)
        
        print("\n【数据概览】")
        print(f"总角度数: {len(df)}")
        print(f"角度范围: {df['angle'].min()}° ~ {df['angle'].max()}°")
        print(f"样本总数: {df['sample_count'].sum()}")
        
        print("\n【偏移量统计】")
        print(f"原始偏移量范围: {df['offset'].min():.3f} ~ {df['offset'].max():.3f}")
        print(f"平滑后偏移量范围: {df['smoothed_offset'].min():.3f} ~ {df['smoothed_offset'].max():.3f}")
        print(f"原始偏移量平均值: {df['offset'].mean():.3f}")
        print(f"平滑后偏移量平均值: {df['smoothed_offset'].mean():.3f}")
        print(f"原始偏移量标准差: {df['offset'].std():.3f}")
        print(f"平滑后偏移量标准差: {df['smoothed_offset'].std():.3f}")
        
        print("\n【详细结果】")
        print("角度(°)    原始偏移量    平滑偏移量    样本数")
        print("-" * 50)
        for _, row in df.iterrows():
            print(f"{row['angle']:6.1f}    {row['offset']:10.3f}    {row['smoothed_offset']:10.3f}    {int(row['sample_count']):6d}")
        
        print("\n【偏移量变化分析】")
        # 计算相邻角度间的偏移量变化
        angle_diffs = np.diff(df['angle'].values)
        offset_diffs = np.diff(df['smoothed_offset'].values)
        
        print("角度间隔    偏移量变化")
        print("-" * 25)
        for i, (angle_diff, offset_diff) in enumerate(zip(angle_diffs, offset_diffs)):
            start_angle = df.iloc[i]['angle']
            end_angle = df.iloc[i+1]['angle']
            print(f"{start_angle:6.1f}°→{end_angle:6.1f}°    {offset_diff:10.3f}")
        
        print("\n【关键发现】")
        # 找出偏移量最大和最小的角度
        max_offset_idx = df['smoothed_offset'].idxmax()
        min_offset_idx = df['smoothed_offset'].idxmin()
        
        print(f"最大偏移量: {df.iloc[max_offset_idx]['angle']}° = {df.iloc[max_offset_idx]['smoothed_offset']:.3f}")
        print(f"最小偏移量: {df.iloc[min_offset_idx]['angle']}° = {df.iloc[min_offset_idx]['smoothed_offset']:.3f}")
        
        # 找出偏移量接近0的角度
        zero_offset_threshold = 10  # 偏移量小于10认为接近0
        near_zero = df[abs(df['smoothed_offset']) < zero_offset_threshold]
        if not near_zero.empty:
            print(f"\n偏移量接近0的角度 (±{zero_offset_threshold}):")
            for _, row in near_zero.iterrows():
                print(f"  {row['angle']}°: {row['smoothed_offset']:.3f}")
        
        print("\n=" * 60)
        print("注：偏移量 = 实际值 - 拟合值")
        print("正值表示实际值大于拟合值，负值表示实际值小于拟合值")
        print("=" * 60)
        
    except FileNotFoundError:
        print("错误: 找不到结果文件 'doa_offset_results.csv'")
        print("请先运行 'python doa_offset_calculator.py' 生成结果文件")
    except Exception as e:
        print(f"读取结果时出错: {e}")

if __name__ == "__main__":
    show_doa_results()
