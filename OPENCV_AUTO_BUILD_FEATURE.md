# 依赖库自动构建功能

## 概述

已成功为 Windows 构建脚本添加了依赖库自动构建功能。当预构建库不存在时，构建脚本会自动从源码编译相应的库，并将编译结果安装到正确的位置。

**支持的自动构建库：**
- **OpenCV 4.5.1**: 当 `third_party\prebuilt\windows\opencv4.5.1` 不存在时，从 `third_party\source\opencv-4.5.1` 自动构建
- **glog 0.3.5**: 当 `third_party\prebuilt\windows\glog` 不存在时，从 `third_party\source\glog-0.3.5` 自动构建

## 更新的文件

### 1. build_windows_simulate_center_stage.bat
- **新增功能**: 自动检测 OpenCV 和 glog 预构建库是否存在
- **自动构建**: 如果预构建库不存在但源码存在，自动编译相应库
- **新增函数**:
  - `:build_opencv_from_source` - 完整的 OpenCV 构建流程
  - `:build_glog_from_source` - 完整的 glog 构建流程

### 2. Build-SimulateCenterStage.ps1
- **新增功能**: PowerShell 版本的依赖库自动构建
- **新增函数**:
  - `Build-OpenCVFromSource` - PowerShell 实现的 OpenCV 构建
  - `Build-GlogFromSource` - PowerShell 实现的 glog 构建
- **智能检测**: 优先检查源码，然后决定是否自动构建

### 3. check_windows_build_env.bat
- **增强检查**: 不仅检查预构建库，还检查源码可用性
- **信息提示**: 明确告知用户 OpenCV 将被自动构建

### 4. BUILD_WINDOWS_README.md
- **更新文档**: 将自动构建设为推荐选项
- **新增说明**: 详细说明自动构建流程和时间预期
- **故障排除**: 添加 OpenCV 构建相关的故障排除信息

## 工作流程

### 自动构建流程
1. **检测阶段**: 脚本检查各个预构建库是否存在
   - `third_party\prebuilt\windows\opencv4.5.1`
   - `third_party\prebuilt\windows\glog`
2. **源码检查**: 如果预构建库不存在，检查相应的源码目录
   - `third_party\source\opencv-4.5.1`
   - `third_party\source\glog-0.3.5`
3. **自动构建**: 如果源码存在，自动启动相应库的构建流程
4. **配置优化**: 使用优化的 CMake 配置，禁用不必要的组件
5. **并行构建**: 使用多核并行编译加速构建过程
6. **自动安装**: 将构建结果安装到正确的目录结构
7. **清理**: 自动清理临时构建目录
8. **验证**: 验证安装是否成功
9. **继续构建**: 继续主项目的构建流程

### CMake 配置优化

#### OpenCV 构建配置：
- `BUILD_TESTS=OFF` - 禁用测试
- `BUILD_PERF_TESTS=OFF` - 禁用性能测试
- `BUILD_EXAMPLES=OFF` - 禁用示例
- `BUILD_DOCS=OFF` - 禁用文档
- `BUILD_opencv_apps=OFF` - 禁用应用程序
- `BUILD_opencv_python2=OFF` - 禁用 Python2 绑定
- `BUILD_opencv_python3=OFF` - 禁用 Python3 绑定
- `WITH_CUDA=OFF` - 禁用 CUDA（简化依赖）
- `WITH_OPENCL=ON` - 启用 OpenCL
- `WITH_TBB=ON` - 启用 TBB
- `WITH_IPP=ON` - 启用 IPP
- `BUILD_SHARED_LIBS=OFF` - 构建静态库
- `CMAKE_CXX_FLAGS="/MP"` - 启用多处理器编译
- `--parallel 4` - 使用 4 个并行进程

#### glog 构建配置：
- `BUILD_TESTING=OFF` - 禁用测试
- `BUILD_SHARED_LIBS=OFF` - 构建静态库
- `WITH_GFLAGS=OFF` - 禁用 gflags 依赖
- `WITH_THREADS=ON` - 启用线程支持
- `WITH_TLS=ON` - 启用线程本地存储
- `CMAKE_CXX_FLAGS="/MT"` - 使用静态运行时
- `CMAKE_C_FLAGS="/MT"` - 使用静态运行时
- `--parallel 4` - 使用 4 个并行进程

## 使用方法

### 自动模式（推荐）
只需运行任何构建脚本，如果检测到缺少 OpenCV 预构建库但存在源码，将自动构建：

```cmd
# 批处理脚本
build_windows_simulate_center_stage.bat

# PowerShell 脚本
.\Build-SimulateCenterStage.ps1
```

### 手动检查
使用环境检查脚本验证配置：
```cmd
check_windows_build_env.bat
```

## 时间预期

### 首次构建时间（从源码构建）
- **OpenCV**: 20-60 分钟（取决于系统性能）
- **glog**: 5-10 分钟（相对较快）
- **总计**: 25-70 分钟（如果两个库都需要构建）

### 后续构建时间
- **主项目**: 几分钟（使用缓存的预构建库）
- **增量构建**: 更快（只重新编译修改的部分）

### 性能优化
- **并行构建**: 使用多核 CPU 可显著减少构建时间
- **SSD 硬盘**: 使用 SSD 可加速 I/O 操作
- **内存**: 充足的内存可提高编译效率

## 目录结构

### 构建前
```
third_party/
├── source/
│   ├── opencv-4.5.1/          # OpenCV 源码
│   └── glog-0.3.5/            # glog 源码
└── prebuilt/
    └── windows/
        ├── tnn/
        └── Eigen/
```

### 构建后
```
third_party/
├── source/
│   ├── opencv-4.5.1/          # OpenCV 源码
│   └── glog-0.3.5/            # glog 源码
└── prebuilt/
    └── windows/
        ├── opencv4.5.1/       # 自动构建的 OpenCV 库
        │   ├── include/
        │   ├── lib/
        │   └── bin/
        ├── glog/               # 自动构建的 glog 库
        │   ├── include/
        │   └── lib/
        ├── tnn/
        └── Eigen/
```

## 错误处理

脚本包含完整的错误处理机制：
- **源码缺失**: 提供清晰的错误信息和解决方案
- **构建失败**: 详细的错误报告和建议
- **安装验证**: 确保构建结果正确安装
- **清理机制**: 构建失败时自动清理临时文件

## 优势

1. **零配置**: 用户无需手动下载或配置 OpenCV
2. **版本一致**: 确保使用正确版本的 OpenCV
3. **优化配置**: 只构建项目需要的组件
4. **自动化**: 完全自动化的构建和安装流程
5. **错误恢复**: 完善的错误处理和恢复机制
6. **缓存机制**: 一次构建，多次使用

## 注意事项

1. **首次构建时间**: 首次构建 OpenCV 需要较长时间，请耐心等待
2. **磁盘空间**: 确保有足够的磁盘空间（建议至少 5GB）
3. **网络连接**: 如果使用 PowerShell 的下载功能，需要稳定的网络连接
4. **系统资源**: 构建过程会占用较多 CPU 和内存资源
5. **Visual Studio**: 确保已正确安装和配置 Visual Studio

## 故障排除

如果自动构建失败：
1. 检查 Visual Studio 是否正确安装
2. 确保有足够的磁盘空间
3. 检查 CMake 版本是否兼容
4. 查看详细的错误日志
5. 考虑手动下载预构建库作为备选方案

这个功能大大简化了 Windows 平台上的构建流程，用户现在只需要运行构建脚本即可自动处理所有依赖项。
