﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{822EC809-0E89-3645-8943-483336CDC23D}"
	ProjectSection(ProjectDependencies) = postProject
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D} = {74EF2365-105C-36A3-AD33-0DBDF5B4175D}
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088} = {DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C} = {2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C} = {4A05F871-9D8E-3871-A8CB-6F1DC586679C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{74EF2365-105C-36A3-AD33-0DBDF5B4175D}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "czcv_camera", "lib\czcv_camera.vcxproj", "{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}"
	ProjectSection(ProjectDependencies) = postProject
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D} = {74EF2365-105C-36A3-AD33-0DBDF5B4175D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "simulate_center_stage", "test\simulate_center_stage.vcxproj", "{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}"
	ProjectSection(ProjectDependencies) = postProject
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D} = {74EF2365-105C-36A3-AD33-0DBDF5B4175D}
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088} = {DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_center_stage", "test\test_center_stage.vcxproj", "{4A05F871-9D8E-3871-A8CB-6F1DC586679C}"
	ProjectSection(ProjectDependencies) = postProject
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D} = {74EF2365-105C-36A3-AD33-0DBDF5B4175D}
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088} = {DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{822EC809-0E89-3645-8943-483336CDC23D}.Debug|x64.ActiveCfg = Debug|x64
		{822EC809-0E89-3645-8943-483336CDC23D}.Release|x64.ActiveCfg = Release|x64
		{822EC809-0E89-3645-8943-483336CDC23D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{822EC809-0E89-3645-8943-483336CDC23D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D}.Debug|x64.ActiveCfg = Debug|x64
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D}.Debug|x64.Build.0 = Debug|x64
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D}.Release|x64.ActiveCfg = Release|x64
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D}.Release|x64.Build.0 = Release|x64
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{74EF2365-105C-36A3-AD33-0DBDF5B4175D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}.Debug|x64.ActiveCfg = Debug|x64
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}.Debug|x64.Build.0 = Debug|x64
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}.Release|x64.ActiveCfg = Release|x64
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}.Release|x64.Build.0 = Release|x64
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}.Debug|x64.ActiveCfg = Debug|x64
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}.Debug|x64.Build.0 = Debug|x64
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}.Release|x64.ActiveCfg = Release|x64
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}.Release|x64.Build.0 = Release|x64
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C}.Debug|x64.ActiveCfg = Debug|x64
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C}.Debug|x64.Build.0 = Debug|x64
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C}.Release|x64.ActiveCfg = Release|x64
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C}.Release|x64.Build.0 = Release|x64
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4A05F871-9D8E-3871-A8CB-6F1DC586679C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CE9ACDC1-3A89-31FA-A98F-E1DA302B7E2E}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
