# 手势过滤功能实现文档

## 功能描述

在 `Yolov10RKNN::run` 函数中实现了一个新的手势过滤功能，用于过滤与人体检测框有重叠的特定类别手势。

## 实现细节

### 功能需求
- 对于检测到的手势，如果手势类别是0（PALM）和1（STOP）
- 判断这些手势与 `inputOutput.in_bbox()` 中类别为1的检测框是否存在IOU大于0的情况
- 如果存在重叠则删除这个手势检测框

### 实现位置
- **文件**: `lib/src/hand/rknn_yolov10.cpp`
- **函数**: `Yolov10RKNN::filterGesturesByPersonBoxIOU(TrackerInputOutput &inputOutput)`
- **调用位置**: `Yolov10RKNN::run` 函数中，在 `filterPeaceGestures` 之后

### 代码修改

#### 1. 头文件声明 (`lib/src/hand/rknn_yolov10.h`)
```cpp
void filterGesturesByPersonBoxIOU(TrackerInputOutput &inputOutput);
```

#### 2. 函数实现 (`lib/src/hand/rknn_yolov10.cpp`)
```cpp
void Yolov10RKNN::filterGesturesByPersonBoxIOU(TrackerInputOutput &inputOutput)
{
    // 手势类别定义
    const int CZCV_GESTURE_PALM = 0;
    const int CZCV_GESTURE_STOP = 1;
    const int PERSON_CLASS_ID = 1;  // 人体检测框的类别ID

    // 获取手势结果和输入检测框
    std::vector<stGestureRecResult>& gestureResults = inputOutput.gestureResults();
    std::vector<BboxF> inBboxes = inputOutput.in_bbox();
    
    // 收集类别为1的人体检测框
    std::vector<BboxF> personBoxes;
    for (const auto& bbox : inBboxes)
    {
        if (bbox.class_id() == PERSON_CLASS_ID)
        {
            personBoxes.push_back(bbox);
        }
    }

    // 如果没有人体检测框，直接返回
    if (personBoxes.empty())
    {
        return;
    }

    // 过滤手势检测框
    std::vector<stGestureRecResult> filteredGestures;
    for (const auto& gesture : gestureResults)
    {
        bool shouldRemove = false;
        
        // 只对类别0和1的手势进行过滤
        if (gesture.clsid == CZCV_GESTURE_PALM || gesture.clsid == CZCV_GESTURE_STOP)
        {
            // 检查与所有人体检测框的IOU
            for (const auto& personBox : personBoxes)
            {
                // 计算IOU
                float iou = computeIOU(gestureRect, personRect);
                
                // 如果IOU大于0，标记为需要删除
                if (iou > 0.0f)
                {
                    shouldRemove = true;
                    break;
                }
            }
        }

        // 如果不需要删除，则保留这个手势
        if (!shouldRemove)
        {
            filteredGestures.push_back(gesture);
        }
    }

    // 更新手势结果
    gestureResults = filteredGestures;
}
```

#### 3. 调用位置 (`Yolov10RKNN::run` 函数)
```cpp
// 过滤PEACE和PEACE_INV手势，只保留最靠近图像水平中心的
filterPeaceGestures(inputOutput, img_width);

// 过滤手势类别0和1与inputOutput.in_bbox()中类别为1的box有IOU重叠的情况
filterGesturesByPersonBoxIOU(inputOutput);

return CZCV_OK;
```

## 使用的数据结构

### 手势类别定义
- `CZCV_GESTURE_PALM = 0` - 手掌手势
- `CZCV_GESTURE_STOP = 1` - 停止手势
- `PERSON_CLASS_ID = 1` - 人体检测框类别

### 相关类型
- `stGestureRecResult` - 手势识别结果结构体
- `BboxF` - 浮点型边界框类型
- `TrackerInputOutput` - 跟踪器输入输出类

## IOU计算
使用现有的 `computeIOU(const Rectf& boxA, const Rectf& boxB)` 函数计算两个矩形框的交并比。

## 测试
创建了测试文件 `test/test_gesture_filter.cpp` 用于验证功能的正确性。

## 注意事项
1. 只对手势类别0和1进行过滤
2. 只与类别为1的检测框进行IOU计算
3. 任何IOU大于0的情况都会导致手势被删除
4. 其他类别的手势不受影响
5. 如果没有人体检测框，所有手势都会被保留
