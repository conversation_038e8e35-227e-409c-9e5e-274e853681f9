# 相机标定代码更新说明

## 更新概述

原始的 `camera_calibration.py` 代码只计算相机内参（内参矩阵和畸变系数），现已更新为同时计算相机内参和外参。

## 主要更新内容

### 1. 新增外参计算和保存功能

- **旋转向量 (rvecs)**: 每张标定图像相对于世界坐标系的旋转
- **平移向量 (tvecs)**: 每张标定图像相对于世界坐标系的平移
- **旋转矩阵**: 从旋转向量转换得到的3x3旋转矩阵
- **相机位置**: 相机在世界坐标系中的实际位置

### 2. 增强的参数文件

更新后的 `camera_params.yml` 文件包含：

```yaml
camera_matrix: [[fx, 0, cx], [0, fy, cy], [0, 0, 1]]  # 内参矩阵
dist_coeffs: [k1, k2, p1, p2, k3]                     # 畸变系数
image_size: [width, height]                           # 图像尺寸
rotation_vectors: [[rx1, ry1, rz1], ...]             # 每张图像的旋转向量
translation_vectors: [[tx1, ty1, tz1], ...]          # 每张图像的平移向量
num_images: 4                                         # 标定图像数量
```

### 3. 新增功能

#### 详细的外参信息输出
- 每张图像的旋转向量和平移向量
- 旋转矩阵
- 相机在世界坐标系中的位置
- 总旋转角度

#### 标定精度评估
- 计算平均重投影误差
- 评估标定质量

#### 3D可视化功能
- 可视化相机位置和棋盘格的3D关系
- 显示相机坐标轴方向
- 保存可视化图像

### 4. 实际标定结果

使用4张1080p图像进行标定，得到以下结果：

**内参矩阵:**
```
[[849.43, 0.00, 614.92],
 [0.00, 859.13, 394.57],
 [0.00, 0.00, 1.00]]
```

**畸变系数:**
```
[0.1356, -0.1555, -0.0043, 0.0124, 0.0397]
```

**标定精度:** 0.0390 像素（平均重投影误差）

**外参示例（图像1）:**
- 旋转向量: [-0.312, -0.293, -0.059]
- 平移向量: [-736.75, -235.15, 3626.12] mm
- 相机位置: [-359.92, 1364.98, -3428.43] mm
- 旋转角度: 24.75°

## 新增文件

### 1. `test_camera_calibration.py`
- 测试脚本，验证标定结果的正确性
- 检查参数文件的完整性
- 输出外参摘要信息

### 2. `use_camera_params.py`
- 使用示例脚本
- 演示如何加载和使用标定参数
- 包含图像去畸变、3D到2D投影等功能

## 使用方法

### 运行标定
```bash
python camera_calibration.py
```

### 测试结果
```bash
python test_camera_calibration.py
```

### 使用参数
```bash
python use_camera_params.py
```

## 技术要点

### 外参的含义
- **旋转向量**: 描述相机相对于世界坐标系的旋转
- **平移向量**: 描述相机原点在世界坐标系中的位置
- **相机位置**: 通过 `camera_position = -R.T @ tvec` 计算得到

### 坐标系定义
- **世界坐标系**: 以棋盘格平面为Z=0的平面
- **相机坐标系**: 以相机光心为原点，Z轴指向前方

### 应用场景
- 机器视觉系统标定
- 3D重建
- 增强现实
- 机器人视觉导航

## 注意事项

1. 确保标定图像质量良好，角点检测准确
2. 标定图像应覆盖不同的角度和位置
3. 重投影误差应小于1像素为佳
4. 外参结果的单位为毫米（mm）
