# SquareCornerDetector

A C++ class using OpenCV for detecting 4 white square contours arranged in a 2x2 grid and extracting their corner points.

## Overview

The `SquareCornerDetector` class is designed to:
- Detect exactly 4 white square contours in an image
- Arrange them in a 2x2 grid pattern (top-left, top-right, bottom-left, bottom-right)
- Extract the outer corner points:
  - Top-left corner of the top-left square
  - Top-right corner of the top-right square
  - Bottom-left corner of the bottom-left square
  - Bottom-right corner of the bottom-right square

## Files

- `square_corner_detector.hpp` - Header file with class declaration
- `square_corner_detector.cpp` - Implementation file
- `square_corner_detector_example.cpp` - Example usage
- `square_corner_detector_CMakeLists.txt` - CMake build configuration

## Dependencies

- OpenCV 3.0 or higher
- C++11 or higher

## Key Features

### Detection Parameters
- **minArea**: Minimum area threshold for square detection (default: 1000.0)
- **maxArea**: Maximum area threshold for square detection (default: 50000.0)
- **approxEpsilon**: Epsilon for contour approximation (default: 0.02)
- **cannyLow**: Lower threshold for Canny edge detection (default: 50.0)
- **cannyHigh**: Upper threshold for Canny edge detection (default: 150.0)

### Main Methods

#### Constructor
```cpp
SquareCornerDetector(double minArea = 1000.0, 
                    double maxArea = 50000.0,
                    double approxEpsilon = 0.02,
                    double cannyLow = 50.0,
                    double cannyHigh = 150.0);
```

#### Detection
```cpp
bool detectSquareCorners(const cv::Mat& image);
```
Returns `true` if exactly 4 squares were detected and corners extracted successfully.

#### Get Results
```cpp
const SquareGridCorners& getGridCorners() const;
const std::vector<DetectedSquare>& getDetectedSquares() const;
```

#### Visualization
```cpp
cv::Mat drawResults(const cv::Mat& image, bool drawSquares = true, bool drawCorners = true) const;
```

## Usage Example

```cpp
#include "square_corner_detector.hpp"

int main() {
    // Create detector
    SquareCornerDetector detector;
    
    // Load image
    cv::Mat image = cv::imread("input.jpg");
    
    // Detect squares and corners
    if (detector.detectSquareCorners(image)) {
        // Get corner points
        const SquareGridCorners& corners = detector.getGridCorners();
        
        std::cout << "Top-left corner: (" << corners.topLeft.x << ", " << corners.topLeft.y << ")" << std::endl;
        std::cout << "Top-right corner: (" << corners.topRight.x << ", " << corners.topRight.y << ")" << std::endl;
        std::cout << "Bottom-left corner: (" << corners.bottomLeft.x << ", " << corners.bottomLeft.y << ")" << std::endl;
        std::cout << "Bottom-right corner: (" << corners.bottomRight.x << ", " << corners.bottomRight.y << ")" << std::endl;
        
        // Draw results
        cv::Mat result = detector.drawResults(image);
        cv::imwrite("output.jpg", result);
    }
    
    return 0;
}
```

## Data Structures

### SquareGridCorners
```cpp
struct SquareGridCorners {
    cv::Point2f topLeft;      // Top-left corner of top-left square
    cv::Point2f topRight;     // Top-right corner of top-right square
    cv::Point2f bottomLeft;   // Bottom-left corner of bottom-left square
    cv::Point2f bottomRight;  // Bottom-right corner of bottom-right square
    bool isValid;             // Whether all corners were successfully detected
};
```

### DetectedSquare
```cpp
struct DetectedSquare {
    std::vector<cv::Point2f> corners;  // Four corner points of the square
    cv::Point2f center;                // Center point of the square
    double area;                       // Area of the square
};
```

## Algorithm Overview

1. **Preprocessing**: Convert to grayscale, apply Gaussian blur, threshold, and morphological operations
2. **Contour Detection**: Find external contours using `cv::findContours`
3. **Filtering**: Filter contours based on area and shape (4 vertices, convex, ~90° angles)
4. **Validation**: Ensure exactly 4 valid squares are detected
5. **Arrangement**: Sort squares by position into 2x2 grid
6. **Corner Extraction**: Extract the outer corner points from each positioned square

## Parameter Tuning

If detection fails, try adjusting these parameters:

- **For smaller squares**: Decrease `minArea`
- **For larger squares**: Increase `maxArea`
- **For more precise detection**: Decrease `approxEpsilon`
- **For noisy images**: Adjust `cannyLow` and `cannyHigh` thresholds

## Building

### Using CMake
```bash
mkdir build
cd build
cmake ..
make
```

### Manual compilation
```bash
g++ -std=c++11 square_corner_detector.cpp square_corner_detector_example.cpp -o example `pkg-config --cflags --libs opencv4`
```

## Notes

- The input image should contain exactly 4 white squares on a darker background
- Squares should be arranged in a clear 2x2 grid pattern
- The class uses contour-based detection, so good contrast between squares and background is important
- All comments and documentation are in English as requested
