import numpy as np

def are_coplanar(points, tol=1e-1):
    """
    判断四个三维点是否共面。
    
    参数:
        points: shape (4, 3) 的 array，表示四个三维点
        tol: 容差阈值，默认 1e-6
    
    返回:
        bool: 共面返回 True，否则 False
    """
    points = np.array(points)
    if points.shape != (4, 3):
        raise ValueError("输入必须是 4 个三维点，形状为 (4, 3)")
    
    # 构造三个向量
    v1 = points[1] - points[0]
    v2 = points[2] - points[0]
    v3 = points[3] - points[0]
    
    # 计算混合积：v1 · (v2 × v3)
    scalar_triple_product = np.dot(v1, np.cross(v2, v3))
    print("scalar_triple_product", scalar_triple_product)
    return abs(scalar_triple_product) < tol


print(np.arctan2(14, 40) * 180 / np.pi)
quit()
# 给定的四个点,
points = np.array([
    [-686.32386643, -434.34548246, 1277.21997129],
    [ 770.37500943, -531.64785369, 1601.19735203],
 [-381.089835, -330.241747, 1315.880579],
 [395.924987, -382.143610, 1488.692703],
], dtype=np.float32)


# 判断是否共面
coplanar = are_coplanar(points)
print("四点共面：" if coplanar else "四点不共面。")