# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.25

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MSYS Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "D:/Package/android-ndk-r23c/build/cmake/android-legacy.toolchain.cmake"
  "D:/Package/android-ndk-r23c/build/cmake/android.toolchain.cmake"
  "D:/Package/android-ndk-r23c/build/cmake/flags.cmake"
  "D:/Package/android-ndk-r23c/build/cmake/hooks/pre/Android-Clang.cmake"
  "D:/Package/android-ndk-r23c/build/cmake/hooks/pre/Android-Initialize.cmake"
  "D:/Package/android-ndk-r23c/build/cmake/hooks/pre/Android.cmake"
  "D:/Package/android-ndk-r23c/build/cmake/platforms.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeCInformation.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeCXXInformation.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeCommonLanguageInclude.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeGenericSystem.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeInitializeConfigs.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeLanguageInformation.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeSystemSpecificInformation.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeSystemSpecificInitialize.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Compiler/Clang-C.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Compiler/Clang-CXX.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Compiler/Clang.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Compiler/GNU.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/FindPackageMessage.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Platform/Android-Clang-C.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Platform/Android-Clang-CXX.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Platform/Android-Clang.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Platform/Android-Initialize.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Platform/Android.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Platform/Linux.cmake"
  "D:/Program Files/CMake/share/cmake-3.25/Modules/Platform/UnixPaths.cmake"
  "D:/Program/Project/project/czcv_camera_new/CMakeLists.txt"
  "CMakeFiles/3.25.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.25.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.25.3/CMakeSystem.cmake"
  "D:/Program/Project/project/czcv_camera_new/lib/CMakeLists.txt"
  "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/jni/OpenCVConfig-version.cmake"
  "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/jni/OpenCVConfig.cmake"
  "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/jni/abi-arm64-v8a/OpenCVConfig.cmake"
  "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/jni/abi-arm64-v8a/OpenCVModules-release.cmake"
  "D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/jni/abi-arm64-v8a/OpenCVModules.cmake"
  "D:/Program/Project/project/czcv_camera_new/third_party/third_party.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "lib/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "lib/CMakeFiles/czcv_camera.dir/DependInfo.cmake"
  )
