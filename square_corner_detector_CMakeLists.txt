# CMakeLists.txt for SquareCornerDetector
# This is an example CMakeLists.txt file for building the SquareCornerDetector class

cmake_minimum_required(VERSION 3.10)
project(SquareCornerDetector)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find OpenCV package
find_package(OpenCV REQUIRED)

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})

# Add library for SquareCornerDetector
add_library(square_corner_detector
    square_corner_detector.cpp
    square_corner_detector.hpp
)

# Link OpenCV libraries to the library
target_link_libraries(square_corner_detector ${OpenCV_LIBS})

# Add executable for example usage
add_executable(square_corner_detector_example
    square_corner_detector_example.cpp
)

# Link the detector library and OpenCV to the example
target_link_libraries(square_corner_detector_example 
    square_corner_detector 
    ${OpenCV_LIBS}
)

# Optional: Add compiler flags for optimization and warnings
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    target_compile_options(square_corner_detector PRIVATE -Wall -Wextra -O2)
    target_compile_options(square_corner_detector_example PRIVATE -Wall -Wextra -O2)
endif()

# Print OpenCV information
message(STATUS "OpenCV library status:")
message(STATUS "    version: ${OpenCV_VERSION}")
message(STATUS "    libraries: ${OpenCV_LIBS}")
message(STATUS "    include path: ${OpenCV_INCLUDE_DIRS}")

# Installation (optional)
install(TARGETS square_corner_detector
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES square_corner_detector.hpp
    DESTINATION include
)

install(TARGETS square_corner_detector_example
    RUNTIME DESTINATION bin
)
