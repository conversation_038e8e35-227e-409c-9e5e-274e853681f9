import re

def find_consecutive_line_numbers(file_path):
    """
    读取gesture_analysis_results.txt文件，找出连续的行号序列
    """
    line_numbers = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                # 查找行号模式
                match = re.search(r'行号:\s*(\d+)', line)
                if match:
                    line_numbers.append(int(match.group(1)))
    
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    if not line_numbers:
        print("未找到任何行号")
        return
    
    print(f"总共找到 {len(line_numbers)} 个行号")
    
    # 找出连续的行号序列
    consecutive_sequences = []
    current_sequence = [line_numbers[0]]
    
    for i in range(1, len(line_numbers)):
        if line_numbers[i] == line_numbers[i-1] + 1:
            # 连续的行号
            current_sequence.append(line_numbers[i])
        else:
            # 不连续，保存当前序列（如果长度大于5）
            if len(current_sequence) > 5:
                consecutive_sequences.append(current_sequence.copy())
            current_sequence = [line_numbers[i]]
    
    # 检查最后一个序列
    if len(current_sequence) > 5:
        consecutive_sequences.append(current_sequence)
    
    # 输出结果
    if consecutive_sequences:
        print(f"\n找到 {len(consecutive_sequences)} 个连续值个数大于5的序列:")
        print("=" * 80)
        
        # 保存结果到文件
        output_file = "consecutive_lines_analysis.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("连续行号分析结果\n")
            f.write(f"分析文件: {file_path}\n")
            f.write(f"找到 {len(consecutive_sequences)} 个连续值个数大于5的序列\n")
            f.write("=" * 80 + "\n\n")
            
            for i, sequence in enumerate(consecutive_sequences, 1):
                start_line = sequence[0]
                end_line = sequence[-1]
                count = len(sequence)
                
                print(f"序列 {i}:")
                print(f"  起始行号: {start_line}")
                print(f"  结束行号: {end_line}")
                print(f"  连续个数: {count}")
                print(f"  完整序列: {start_line}-{end_line}")
                print(f"  详细序列: {sequence}")
                print("-" * 60)
                
                # 写入文件
                f.write(f"序列 {i}:\n")
                f.write(f"  起始行号: {start_line}\n")
                f.write(f"  结束行号: {end_line}\n")
                f.write(f"  连续个数: {count}\n")
                f.write(f"  完整序列: {start_line}-{end_line}\n")
                f.write(f"  详细序列: {sequence}\n")
                f.write("-" * 80 + "\n\n")
        
        print(f"\n详细结果已保存到文件: {output_file}")
        
        # 统计信息
        total_consecutive_lines = sum(len(seq) for seq in consecutive_sequences)
        max_sequence_length = max(len(seq) for seq in consecutive_sequences)
        min_sequence_length = min(len(seq) for seq in consecutive_sequences)
        
        print(f"\n统计信息:")
        print(f"连续序列总数: {len(consecutive_sequences)}")
        print(f"连续行号总数: {total_consecutive_lines}")
        print(f"最长连续序列长度: {max_sequence_length}")
        print(f"最短连续序列长度: {min_sequence_length}")
        
    else:
        print("未找到连续值个数大于5的序列")

if __name__ == "__main__":
    file_path = "gesture_analysis_results.txt"
    find_consecutive_line_numbers(file_path)
