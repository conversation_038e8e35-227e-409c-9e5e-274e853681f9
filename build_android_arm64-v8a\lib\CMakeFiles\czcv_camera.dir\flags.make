# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.25

# compile CXX with D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe
CXX_DEFINES = -DARM64 -DJSON_SUPPORT -DNO_GLOG -D__ANDROID_ARM64__ -D__ANDROID__ -Dczcv_camera_EXPORTS

CXX_INCLUDES = -I/D/Program/Project/project/czcv_camera_new/third_party/source/rapidjson-1.1.0/include -I/D/Program/Project/project/czcv_camera_new/third_party/source/Eigen/include -I/D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/glog/include -I/D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/TNN/include -I/D/Program/Project/project/czcv_camera_new/./runtime/Android/librknn_api/include -I/D/Program/Project/project/czcv_camera_new/./external/rga/include -I/D/Program/Project/project/czcv_camera_new/./external -I/D/Program/Project/project/czcv_camera_new/./lib/include -I/D/Program/Project/project/czcv_camera_new/./lib/src -I/D/Program/Project/project/czcv_camera_new/lib/src/libopencl-stub/include -isystem /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/jni/include

CXX_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -std=c++17 -fno-omit-frame-pointer -fasynchronous-unwind-tables   -Wall -Wextra  -Wno-unused-variable -Wno-unused-parameter -Wno-unknown-pragmas -Wno-unused-value    -Wno-unused-command-line-argument -fopenmp -frtti -fexceptions -Wno-unused-const-variable -DNDEBUG  -fPIC -fvisibility=hidden

