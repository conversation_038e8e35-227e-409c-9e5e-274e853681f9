import re
import os

def analyze_gesture_data(file_path):
    """
    读取文件，找到gesture:和;doa:之间的数字，
    将数字7个分为一组，找到第6个数字为2或3的行
    """
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    matching_lines = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, 1):
                # 查找gesture:和;doa:之间的内容
                pattern = r'gesture:(.*?);doa:'
                match = re.search(pattern, line)
                
                if match:
                    gesture_data = match.group(1)
                    # 提取数字
                    numbers = re.findall(r'-?\d+(?:\.\d+)?', gesture_data)
                    
                    if numbers:
                        # 将数字转换为浮点数或整数
                        try:
                            parsed_numbers = []
                            for num in numbers:
                                if '.' in num:
                                    parsed_numbers.append(float(num))
                                else:
                                    parsed_numbers.append(int(num))
                            
                            # 7个数字分为一组
                            for i in range(0, len(parsed_numbers), 7):
                                group = parsed_numbers[i:i+7]
                                if len(group) >= 6:  # 至少要有6个数字
                                    # 检查第6个数字（索引5）是否为2或3
                                    if group[5] == 2 or group[5] == 3:
                                        matching_lines.append({
                                            'line_number': line_num,
                                            'line_content': line.strip(),
                                            'gesture_numbers': group,
                                            'sixth_number': group[5]
                                        })
                                        break  # 找到一组符合条件的就跳出
                        except (ValueError, IndexError) as e:
                            continue
    
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 输出结果
    if matching_lines:
        print(f"找到 {len(matching_lines)} 行符合条件的数据:")
        print("=" * 80)

        # 保存结果到文件
        output_file = "gesture_analysis_results.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"手势数据分析结果\n")
            f.write(f"分析文件: {file_path}\n")
            f.write(f"找到 {len(matching_lines)} 行符合条件的数据 (第6个数字为2或3)\n")
            f.write("=" * 100 + "\n\n")

            for i, item in enumerate(matching_lines, 1):
                # 控制台输出（只显示前10条）
                if i <= 10:
                    print(f"行号: {item['line_number']}")
                    print(f"第6个数字: {item['sixth_number']}")
                    print(f"数字组: {item['gesture_numbers']}")
                    print(f"完整行内容: {item['line_content']}")
                    print("-" * 80)

                # 文件输出（所有数据）
                f.write(f"序号: {i}\n")
                f.write(f"行号: {item['line_number']}\n")
                f.write(f"第6个数字: {item['sixth_number']}\n")
                f.write(f"数字组: {item['gesture_numbers']}\n")
                f.write(f"完整行内容: {item['line_content']}\n")
                f.write("-" * 100 + "\n\n")

        if len(matching_lines) > 10:
            print(f"... 还有 {len(matching_lines) - 10} 行数据")
        print(f"\n所有结果已保存到文件: {output_file}")

        # 统计信息
        count_2 = sum(1 for item in matching_lines if item['sixth_number'] == 2)
        count_3 = sum(1 for item in matching_lines if item['sixth_number'] == 3)
        print(f"\n统计信息:")
        print(f"第6个数字为2的行数: {count_2}")
        print(f"第6个数字为3的行数: {count_3}")
        print(f"总计: {len(matching_lines)}")

    else:
        print("未找到符合条件的数据")

if __name__ == "__main__":
    file_path = r"D:\Download\25-11-14_08-32-16_dec\AlgorithmCameraLog\czcv_debug.txt"
    analyze_gesture_data(file_path)
