import pandas as pd
import numpy as np

def show_peak_results():
    """
    显示基于峰值检测的DOA偏移量计算结果
    """
    try:
        # 读取结果文件
        df = pd.read_csv('doa_offset_results_peak.csv')
        
        print("=" * 70)
        print("DOA偏移量计算结果 - 基于分布峰值检测方法")
        print("=" * 70)
        
        print("\n【方法说明】")
        print("• 统计每个角度所有实际值c的分布")
        print("• 对分布进行高斯平滑处理")
        print("• 找到分布的峰值作为代表值")
        print("• 偏移量 = 峰值 - 拟合值")
        
        print("\n【数据概览】")
        print(f"总角度数: {len(df)}")
        print(f"角度范围: {df['angle'].min()}° ~ {df['angle'].max()}°")
        print(f"样本总数: {df['sample_count'].sum()}")
        
        print("\n【偏移量统计】")
        print(f"原始偏移量范围: {df['offset'].min():.3f} ~ {df['offset'].max():.3f}")
        print(f"平滑后偏移量范围: {df['smoothed_offset'].min():.3f} ~ {df['smoothed_offset'].max():.3f}")
        print(f"原始偏移量平均值: {df['offset'].mean():.3f}")
        print(f"平滑后偏移量平均值: {df['smoothed_offset'].mean():.3f}")
        print(f"原始偏移量标准差: {df['offset'].std():.3f}")
        print(f"平滑后偏移量标准差: {df['smoothed_offset'].std():.3f}")
        
        print("\n【详细结果】")
        print("角度(°)    峰值      拟合值    原始偏移量    平滑偏移量    样本数")
        print("-" * 75)
        for _, row in df.iterrows():
            print(f"{row['angle']:6.1f}  {row['peak_value']:8.1f}  {row['fitted_value']:8.1f}  {row['offset']:10.3f}  {row['smoothed_offset']:10.3f}  {int(row['sample_count']):6d}")
        
        print("\n【峰值与拟合值对比分析】")
        print("角度(°)    峰值/拟合值比例    偏移量类型")
        print("-" * 45)
        for _, row in df.iterrows():
            ratio = row['peak_value'] / row['fitted_value'] if row['fitted_value'] != 0 else float('inf')
            offset_type = "正偏移" if row['smoothed_offset'] > 0 else "负偏移"
            if abs(row['smoothed_offset']) < 10:
                offset_type = "接近理想"
            print(f"{row['angle']:6.1f}        {ratio:8.3f}        {offset_type}")
        
        print("\n【偏移量变化分析】")
        # 计算相邻角度间的偏移量变化
        angle_diffs = np.diff(df['angle'].values)
        offset_diffs = np.diff(df['smoothed_offset'].values)
        
        print("角度间隔      偏移量变化    变化率")
        print("-" * 40)
        for i, (angle_diff, offset_diff) in enumerate(zip(angle_diffs, offset_diffs)):
            start_angle = df.iloc[i]['angle']
            end_angle = df.iloc[i+1]['angle']
            change_rate = offset_diff / angle_diff if angle_diff != 0 else 0
            print(f"{start_angle:6.1f}°→{end_angle:6.1f}°  {offset_diff:10.3f}  {change_rate:8.3f}/°")
        
        print("\n【关键发现】")
        # 找出偏移量最大和最小的角度
        max_offset_idx = df['smoothed_offset'].idxmax()
        min_offset_idx = df['smoothed_offset'].idxmin()
        
        print(f"最大正偏移: {df.iloc[max_offset_idx]['angle']}° = {df.iloc[max_offset_idx]['smoothed_offset']:.3f}")
        print(f"最大负偏移: {df.iloc[min_offset_idx]['angle']}° = {df.iloc[min_offset_idx]['smoothed_offset']:.3f}")
        
        # 找出偏移量接近0的角度
        zero_offset_threshold = 25  # 偏移量小于25认为相对较小
        near_zero = df[abs(df['smoothed_offset']) < zero_offset_threshold]
        if not near_zero.empty:
            print(f"\n偏移量相对较小的角度 (±{zero_offset_threshold}):")
            for _, row in near_zero.iterrows():
                print(f"  {row['angle']}°: 偏移量 = {row['smoothed_offset']:.3f}, 峰值 = {row['peak_value']:.1f}")
        
        # 分析峰值与拟合值的关系
        print("\n【峰值检测质量分析】")
        over_fitted = df[df['peak_value'] > df['fitted_value']]
        under_fitted = df[df['peak_value'] < df['fitted_value']]
        
        print(f"峰值高于拟合值的角度数: {len(over_fitted)} ({len(over_fitted)/len(df)*100:.1f}%)")
        print(f"峰值低于拟合值的角度数: {len(under_fitted)} ({len(under_fitted)/len(df)*100:.1f}%)")
        
        if len(over_fitted) > 0:
            print(f"峰值高于拟合值的平均偏移: {over_fitted['smoothed_offset'].mean():.3f}")
        if len(under_fitted) > 0:
            print(f"峰值低于拟合值的平均偏移: {under_fitted['smoothed_offset'].mean():.3f}")
        
        print("\n【建议】")
        # 基于结果给出建议
        large_negative = df[df['smoothed_offset'] < -100]
        large_positive = df[df['smoothed_offset'] > 50]
        
        if len(large_negative) > 0:
            print("需要特别关注的大负偏移角度:")
            for _, row in large_negative.iterrows():
                print(f"  {row['angle']}°: 偏移量 = {row['smoothed_offset']:.1f}")
        
        if len(large_positive) > 0:
            print("需要特别关注的大正偏移角度:")
            for _, row in large_positive.iterrows():
                print(f"  {row['angle']}°: 偏移量 = {row['smoothed_offset']:.1f}")
        
        print("\n" + "=" * 70)
        print("注：")
        print("• 正偏移：实际峰值大于拟合值，可能需要向下调整")
        print("• 负偏移：实际峰值小于拟合值，可能需要向上调整")
        print("• 峰值检测基于所有样本的分布统计，比均值采样更稳定")
        print("=" * 70)
        
    except FileNotFoundError:
        print("错误: 找不到结果文件 'doa_offset_results_peak.csv'")
        print("请先运行 'python doa_offset_calculator.py' 生成结果文件")
    except Exception as e:
        print(f"读取结果时出错: {e}")

if __name__ == "__main__":
    show_peak_results()
