/*
 *   Stub libopencl that dlsyms into actual library based on environment variable
 *
 *   L<PERSON>OP<PERSON>CL_SO_PATH      -- Path to opencl so that will be searched first
 *   LIBOPENCL_SO_PATH_2    -- Searched second
 *   LIBOPENCL_SO_PATH_3    -- Searched third
 *   LIBOPENCL_SO_PATH_4    -- Searched fourth
 *
 *   If none of these are set, default system paths will be considered
**/

#include "libopencl.h"
#include <stdio.h>
#include "base/common.h"
namespace opencl_stub
{
#if defined(__APPLE__) || defined(__MACOSX)
static const char *default_so_paths[] = {
  "libOpenCL.so",
  "/System/Library/Frameworks/OpenCL.framework/OpenCL"
};
#elif defined(__ANDROID__)
static const char *default_so_paths[] = {
#if 1
#if defined (__aarch64__)
 "/vendor/lib64/libOpenCL.so"
#else
  "/vendor/lib/libOpenCL.so"
#endif
#else
  "/system/vendor/lib64/egl/libGLES_mali.so",
  "/system/vendor/lib/egl/libGLES_mali.so"
#endif
};
#elif defined(_WIN32)
static const char *default_so_paths[] = {
  "OpenCL.dll"
};
#elif defined(__linux__)
static const char *default_so_paths[] = {
  "/usr/lib/libOpenCL.so",
  "/usr/local/lib/libOpenCL.so",
  "/usr/local/lib/libpocl.so",
  "/usr/lib64/libOpenCL.so",
  "/usr/lib32/libOpenCL.so",
  "libOpenCL.so"
};
#endif

static void *so_handle = NULL;


static int access_file(const char *filename)
{
  struct stat buffer;
  return (stat(filename, &buffer) == 0);
}

static int open_libopencl_so()
{
  char *path = NULL, *str = NULL;
  int i;

  if((str=getenv("LIBOPENCL_SO_PATH")) && access_file(str)) {
    path = str;
  }
  else if((str=getenv("LIBOPENCL_SO_PATH_2")) && access_file(str)) {
    path = str;
  }
  else if((str=getenv("LIBOPENCL_SO_PATH_3")) && access_file(str)) {
    path = str;
  }
  else if((str=getenv("LIBOPENCL_SO_PATH_4")) && access_file(str)) {
    path = str;
  }

  if(!path)
  {
    for(i=0; i<(sizeof(default_so_paths) / sizeof(char*)); i++)
    {
      if(access_file(default_so_paths[i]))
      {
        path = (char *) default_so_paths[i];
        break;
      }
    }
  }

  if(path)
  {
    LOGE("Loading OpenCL library %s\n", path);
#ifdef _WIN32
    so_handle = LoadLibraryA(path);
    if (!so_handle) {
        LOGE("LoadLibrary failed with error %d\n", GetLastError());
        exit(EXIT_FAILURE);
    }
#else
    so_handle = dlopen(path, RTLD_LAZY);
    if (!so_handle) {
        LOGE("%s\n", dlerror());
        exit(EXIT_FAILURE);
    }
#endif

    return 0;
  }
  else
  {
    return -1;
  }
}
#ifdef _WIN32
void stubOpenclReset()
{
  if(so_handle)
    FreeLibrary((HMODULE)so_handle);

  so_handle = NULL;
}

cl_int
clGetPlatformIDs(cl_uint          num_entries,
                 cl_platform_id * platforms,
                 cl_uint *        num_platforms)
{
  return CL_INVALID_PLATFORM;
}

cl_int
clGetPlatformInfo(cl_platform_id   platform,
                  cl_platform_info param_name,
                  size_t           param_value_size,
                  void *           param_value,
                  size_t *         param_value_size_ret)
{
  return CL_INVALID_PLATFORM;
}

// ... (similar pattern for all other functions)

cl_mem 
clImportMemoryARMfunc( cl_context context,
                   cl_mem_flags flags,
                   const cl_import_properties_arm *properties,
                   void *memory,
                   size_t size,
                   cl_int *errcode_ret) {
  return NULL;
}

#else
// Original implementation for non-Windows systems
void stubOpenclReset()
{
  if(so_handle)
    dlclose(so_handle);

  so_handle = NULL;
}

// ... (rest of the original implementation)
#endif


}