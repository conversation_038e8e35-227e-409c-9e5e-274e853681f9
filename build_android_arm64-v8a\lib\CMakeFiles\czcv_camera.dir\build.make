# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = "/D/Program Files/CMake/bin/cmake.exe"

# The command to remove a file.
RM = "/D/Program Files/CMake/bin/cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /D/Program/Project/project/czcv_camera_new

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a

# Include any dependencies generated for this target.
include lib/CMakeFiles/czcv_camera.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include lib/CMakeFiles/czcv_camera.dir/compiler_depend.make

# Include the progress variables for this target.
include lib/CMakeFiles/czcv_camera.dir/progress.make

# Include the compile flags for this target's objects.
include lib/CMakeFiles/czcv_camera.dir/flags.make

lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/base/abstract_model.cpp
lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o -MF CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/base/abstract_model.cpp

lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/base/abstract_model.cpp > CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/base/abstract_model.cpp -o CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/base/aes256.cpp
lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o -MF CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/base/aes256.cpp

lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/base/aes256.cpp > CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/base/aes256.cpp -o CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/base/common.cpp
lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o -MF CMakeFiles/czcv_camera.dir/src/base/common.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/base/common.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/base/common.cpp

lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/base/common.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/base/common.cpp > CMakeFiles/czcv_camera.dir/src/base/common.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/base/common.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/base/common.cpp -o CMakeFiles/czcv_camera.dir/src/base/common.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/base/macro.cpp
lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o -MF CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/base/macro.cpp

lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/base/macro.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/base/macro.cpp > CMakeFiles/czcv_camera.dir/src/base/macro.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/base/macro.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/base/macro.cpp -o CMakeFiles/czcv_camera.dir/src/base/macro.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/base/mem_allocator.cpp
lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o -MF CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/base/mem_allocator.cpp

lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/base/mem_allocator.cpp > CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/base/mem_allocator.cpp -o CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/base/nms.cpp
lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o -MF CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/base/nms.cpp

lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/base/nms.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/base/nms.cpp > CMakeFiles/czcv_camera.dir/src/base/nms.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/base/nms.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/base/nms.cpp -o CMakeFiles/czcv_camera.dir/src/base/nms.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/base/status.cpp
lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o -MF CMakeFiles/czcv_camera.dir/src/base/status.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/base/status.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/base/status.cpp

lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/base/status.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/base/status.cpp > CMakeFiles/czcv_camera.dir/src/base/status.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/base/status.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/base/status.cpp -o CMakeFiles/czcv_camera.dir/src/base/status.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/cam_dewarper.cpp
lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o -MF CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/cam_dewarper.cpp

lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/cam_dewarper.cpp > CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/cam_dewarper.cpp -o CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_api.cpp
lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o -MF CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_api.cpp

lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_api.cpp > CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_api.cpp -o CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_capi.cpp
lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o -MF CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_capi.cpp

lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_capi.cpp > CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_capi.cpp -o CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/czcv_center_stage.cpp
lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o -MF CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/czcv_center_stage.cpp

lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/czcv_center_stage.cpp > CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/czcv_center_stage.cpp -o CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/person_viewer.cpp
lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o -MF CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/person_viewer.cpp

lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/person_viewer.cpp > CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/center_stage/person_viewer.cpp -o CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/config/config_setter.cpp
lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o -MF CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/config/config_setter.cpp

lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/config/config_setter.cpp > CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/config/config_setter.cpp -o CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/detector/base_detector.cpp
lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o -MF CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/detector/base_detector.cpp

lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/detector/base_detector.cpp > CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/detector/base_detector.cpp -o CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/detector/detail/rknn_yolox.cpp
lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o -MF CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/detector/detail/rknn_yolox.cpp

lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/detector/detail/rknn_yolox.cpp > CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/detector/detail/rknn_yolox.cpp -o CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/detector/detail/tnn_yolox.cpp
lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o -MF CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/detector/detail/tnn_yolox.cpp

lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/detector/detail/tnn_yolox.cpp > CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/detector/detail/tnn_yolox.cpp -o CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/detector/detect_white_board.cpp
lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o -MF CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/detector/detect_white_board.cpp

lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/detector/detect_white_board.cpp > CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/detector/detect_white_board.cpp -o CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/detector/detector_factory.cpp
lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o -MF CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/detector/detector_factory.cpp

lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/detector/detector_factory.cpp > CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/detector/detector_factory.cpp -o CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/detector/yolox_person_det.cpp
lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o -MF CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/detector/yolox_person_det.cpp

lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/detector/yolox_person_det.cpp > CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/detector/yolox_person_det.cpp -o CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/hand/rknn_yolov10.cpp
lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o -MF CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/hand/rknn_yolov10.cpp

lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/hand/rknn_yolov10.cpp > CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/hand/rknn_yolov10.cpp -o CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/libopencl-stub/src/libopencl.cpp
lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o -MF CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/libopencl-stub/src/libopencl.cpp

lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/libopencl-stub/src/libopencl.cpp > CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/libopencl-stub/src/libopencl.cpp -o CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/tracker/base_tracker.cpp
lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o -MF CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/tracker/base_tracker.cpp

lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/tracker/base_tracker.cpp > CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/tracker/base_tracker.cpp -o CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/tracker/byte_tracker.cpp
lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o -MF CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/tracker/byte_tracker.cpp

lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/tracker/byte_tracker.cpp > CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/tracker/byte_tracker.cpp -o CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/tracker/detail/kalmanFilter.cpp
lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o -MF CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/tracker/detail/kalmanFilter.cpp

lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/tracker/detail/kalmanFilter.cpp > CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/tracker/detail/kalmanFilter.cpp -o CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/person_assert.cpp
lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o -MF CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/person_assert.cpp

lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/person_assert.cpp > CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/person_assert.cpp -o CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/rknn_person_assert.cpp
lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o -MF CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/rknn_person_assert.cpp

lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/rknn_person_assert.cpp > CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/rknn_person_assert.cpp -o CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/tracker/tracker_factory.cpp
lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o -MF CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/tracker/tracker_factory.cpp

lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/tracker/tracker_factory.cpp > CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/tracker/tracker_factory.cpp -o CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/utils/hungarian_match.cpp
lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o -MF CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/utils/hungarian_match.cpp

lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/utils/hungarian_match.cpp > CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/utils/hungarian_match.cpp -o CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.s

lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o: lib/CMakeFiles/czcv_camera.dir/flags.make
lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o: D:/Program/Project/project/czcv_camera_new/lib/src/utils/lapjv.cpp
lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o: lib/CMakeFiles/czcv_camera.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o -MF CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o.d -o CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o -c /D/Program/Project/project/czcv_camera_new/lib/src/utils/lapjv.cpp

lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.i"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/lib/src/utils/lapjv.cpp > CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.i

lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.s"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/lib/src/utils/lapjv.cpp -o CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.s

# Object files for target czcv_camera
czcv_camera_OBJECTS = \
"CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/base/common.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/base/status.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o" \
"CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o"

# External object files for target czcv_camera
czcv_camera_EXTERNAL_OBJECTS =

D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: lib/CMakeFiles/czcv_camera.dir/build.make
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/runtime/Android/librknn_api/arm64-v8a/librknnrt.so
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_calib3d.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_core.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_features2d.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_flann.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_imgcodecs.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_imgproc.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/TNN/libTNN.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_core.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/libittnotify.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/libtegra_hal.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/liblibjpeg-turbo.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/liblibwebp.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/libcpufeatures.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/liblibpng.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/libzlib.a
D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so: D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/liblibopenjp2.a
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Linking CXX shared library /D/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so"
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && /D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -fPIC -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -std=c++17 -fno-omit-frame-pointer -fasynchronous-unwind-tables   -Wall -Wextra  -Wno-unused-variable -Wno-unused-parameter -Wno-unknown-pragmas -Wno-unused-value    -Wno-unused-command-line-argument -fopenmp -frtti -fexceptions -Wno-unused-const-variable -DNDEBUG  -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -shared -Wl,-soname,libczcv_camera.so -o /D/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so $(czcv_camera_OBJECTS) $(czcv_camera_EXTERNAL_OBJECTS)   -L/D/Program/Project/project/czcv_camera_new/./external/lib/arm64-v8a  /D/Program/Project/project/czcv_camera_new/runtime/Android/librknn_api/arm64-v8a/librknnrt.so -Wl,--whole-archive /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_calib3d.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_core.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_features2d.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_flann.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_imgcodecs.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_imgproc.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/TNN/libTNN.a -Wl,--no-whole-archive -llog -lcutils -lbase -lc++ -lomp /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/staticlibs/arm64-v8a/libopencv_core.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/libittnotify.a -ldl -llog /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/libtegra_hal.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/liblibjpeg-turbo.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/liblibwebp.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/libcpufeatures.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/liblibpng.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/libzlib.a /D/Program/Project/project/czcv_camera_new/third_party/prebuilt/android_v8a/opencv4.5.1/sdk/native/3rdparty/libs/arm64-v8a/liblibopenjp2.a -lm -pthread -latomic -lm 

# Rule to build all files generated by this target.
lib/CMakeFiles/czcv_camera.dir/build: D:/Program/Project/project/czcv_camera_new/output/android_v8a/libs/libczcv_camera.so
.PHONY : lib/CMakeFiles/czcv_camera.dir/build

lib/CMakeFiles/czcv_camera.dir/clean:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib && $(CMAKE_COMMAND) -P CMakeFiles/czcv_camera.dir/cmake_clean.cmake
.PHONY : lib/CMakeFiles/czcv_camera.dir/clean

lib/CMakeFiles/czcv_camera.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /D/Program/Project/project/czcv_camera_new /D/Program/Project/project/czcv_camera_new/lib /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib/CMakeFiles/czcv_camera.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : lib/CMakeFiles/czcv_camera.dir/depend

