# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = "/D/Program Files/CMake/bin/cmake.exe"

# The command to remove a file.
RM = "/D/Program Files/CMake/bin/cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@echo "Running CMake cache editor..."
	"/D/Program Files/CMake/bin/cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@echo "Running CMake to regenerate build system..."
	"/D/Program Files/CMake/bin/cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all:
	$(CMAKE_COMMAND) -E cmake_progress_start /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/CMakeFiles /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

#=============================================================================
# Target rules for targets named cmTC_d25e1

# Build rule for target.
cmTC_d25e1:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cmTC_d25e1
.PHONY : cmTC_d25e1

# fast build rule for target.
cmTC_d25e1/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cmTC_d25e1.dir/build.make CMakeFiles/cmTC_d25e1.dir/build
.PHONY : cmTC_d25e1/fast

src.o: src.cxx.o
.PHONY : src.o

# target to build an object file
src.cxx.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cmTC_d25e1.dir/build.make CMakeFiles/cmTC_d25e1.dir/src.cxx.o
.PHONY : src.cxx.o

src.i: src.cxx.i
.PHONY : src.i

# target to preprocess a source file
src.cxx.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cmTC_d25e1.dir/build.make CMakeFiles/cmTC_d25e1.dir/src.cxx.i
.PHONY : src.cxx.i

src.s: src.cxx.s
.PHONY : src.s

# target to generate assembly for a file
src.cxx.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cmTC_d25e1.dir/build.make CMakeFiles/cmTC_d25e1.dir/src.cxx.s
.PHONY : src.cxx.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... cmTC_d25e1"
	@echo "... src.o"
	@echo "... src.i"
	@echo "... src.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

