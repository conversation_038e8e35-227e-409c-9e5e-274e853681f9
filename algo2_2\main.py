import cv2
import numpy as np
import os
from scipy.optimize import least_squares
from sklearn.base import BaseEstimator
from sklearn.linear_model import RANSACRegressor

# -------------------------- 硬件与校准参数 --------------------------
PROJ_RES = (1706, 960)  # 投影仪分辨率 (宽, 高)
CAM_RES = (1280, 720)     # 相机分辨率 (宽, 高)
CARDBOARD_SIZE = (1920, 1080)  # 校准用硬纸板尺寸 (mm, 宽×高)
CROSS_SIZE = 20          # 投影十字线大小 (mm)

# -------------------------- 检测参数 --------------------------
GREEN_LOW = (35, 40, 40)  # HSV绿色下限 (需根据光照调参)
GREEN_HIGH = (77, 255, 255)  # HSV绿色上限
CANNY_THRESH = (50, 150)  # Canny边缘检测阈值
HOUGH_RHO = 1             # 霍夫变换步长
HOUGH_THETA = np.pi / 180 # 霍夫变换角度步长
HOUGH_THRESH = 30         # 霍夫直线检测阈值
QUAD_SIDE_MIN = 20        # 四边形最小边长 (像素)
QUAD_ANGLE_MIN = 30       # 四边形最小内角 (度)
QUAD_ANGLE_MAX = 150      # 四边形最大内角 (度)
QUAD_OVERLAP_RATIO = 0.6  # 直线与四边形边的重叠率阈值

# -------------------------- 粒子滤波参数 --------------------------
PARTICLE_NUM = 200        # 粒子数量
STATE_EPS = 0.05          # 状态随机游走不确定性 (e)
EDGE_DIST_THRESH = 5      # 边缘点距离阈值 (像素)
SAMPLE_STEP = 5           # 边缘点采样步长 (像素)

# -------------------------- 优化参数 --------------------------
COPLANAR_WEIGHT = 100     # 共面约束权重 (ω)
LM_MAX_ITER = 200          # Levenberg-Marquardt最大迭代次数
LM_TOL = 1e-6             # 优化收敛阈值


def calibrate_camera(calib_images_path, chessboard_size=(9,6)):
    """步骤1：校准相机，获取内参矩阵K和畸变系数"""
    objp = np.zeros((chessboard_size[0]*chessboard_size[1], 3), np.float32)
    objp[:, :2] = np.mgrid[0:chessboard_size[0], 0:chessboard_size[1]].T.reshape(-1, 2) * 20  # 棋盘格边长20mm

    objpoints = []  # 3D世界点
    imgpoints = []  # 2D图像点
    images = [cv2.imread(f) for f in calib_images_path if cv2.imread(f) is not None]

    for img in images:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        ret, corners = cv2.findChessboardCorners(gray, chessboard_size, None)
        if ret:
            objpoints.append(objp)
            imgpoints.append(corners)
            cv2.drawChessboardCorners(img, chessboard_size, corners, ret)
            cv2.imshow("Calibration", img)
            cv2.waitKey(500)
    cv2.destroyAllWindows()

    # 校准相机
    ret, K, dist, rvecs, tvecs = cv2.calibrateCamera(
        objpoints, imgpoints, gray.shape[::-1], None, None
    )
    print("相机内参矩阵K:\n", K)
    return K, dist


def load_camera_params(params_path):
    import yaml
    with open(params_path, "r") as f:
        params = yaml.safe_load(f)
    return (
        np.array(params["camera_matrix"]),
        np.array(params["dist_coeffs"]),
        tuple(params["image_size"])
    )

camera_matrix, dist_coeffs, img_size = load_camera_params("./camera_params.yml")


# 纸板平面方程（假设初始时纸板在相机坐标系下的平面为 Z = Z0，需手动测量或通过棋盘格校准）
# 这里通过棋盘格预先获取纸板平面：将纸板与棋盘格重合，用solvePnP求平面方程
def get_cardboard_plane():
    # 纸板4个角的世界坐标（纸板坐标系：原点在左上，X向右，Y向下，Z=0）
    cardboard_3d = np.array([
        [0, 0, 0],
        [CARDBOARD_SIZE[0], 0, 0],
        [CARDBOARD_SIZE[0], CARDBOARD_SIZE[1], 0],
        [0, CARDBOARD_SIZE[1], 0]
    ], dtype=np.float32)

    # 加载相机参数
    camera_matrix = np.load(r"D:\Program\Project\project\czcv_camera_new\algo1\Kc.npy")
    dist_coeffs = np.zeros(5)  # 假设无畸变或已去畸变

    corners_2d = np.array([[415, 242], [807, 240],[821, 462],[395, 461]], dtype=np.float32)

    # 用solvePnP求纸板平面方程 ax + by + cz + d = 0
    ret, rvec, tvec = cv2.solvePnP(cardboard_3d, corners_2d, camera_matrix, dist_coeffs)
    R, _ = cv2.Rodrigues(rvec)  # 旋转矩阵
    # 平面法向量 = R的第三列（纸板坐标系Z轴→相机坐标系）
    n = R[:, 2]
    a, b, c = n
    d = -np.dot(n, tvec.flatten())  # 平面方程：aX + bY + cZ + d = 0
    return np.array([a, b, c, d])

def collect_correspondences():
    """步骤2：采集3D-2D对应点（硬纸板+十字线）"""

    correspondences = []  # 存储 (X^c, x^p)，X^c:相机3D点，x^p:投影仪2D点
    NUM_CALIB_POINTS = 18
    pc_list = []  # 3D点：[Xc, Yc, Zc]
    ip_list = []  # 2D点：[Up, Vp]（投影仪坐标）

    ic_list = [[467, 293], [522, 293], [580, 293], [636, 292], [695, 293], [753, 292],
        [463, 348], [520, 345], [579, 346], [636, 346], [696, 346], [755, 346],
        [459, 403], [518, 403], [576, 403], [636, 403], [697, 403], [757, 403]]

    # 加载相机内参
    camera_matrix = np.load(r"D:\Program\Project\project\czcv_camera_new\algo1\Kc.npy")
    plane_eq = get_cardboard_plane()
    print(f"开始收集{NUM_CALIB_POINTS}个校准点...")
    for i in range(NUM_CALIB_POINTS):
        ip_x = (i % 6 * 4 + 6) * 54 + 95
        ip_y = (i // 6 * 4 + 5) * 54 + 52
        ip = (ip_x, ip_y)
        ip_list.append(ip)
        ic_x, ic_y = ic_list[i]
        # 3. 根据相机内参和纸板平面方程计算Pc（相机坐标系3D点）
        # 相机内参：fx, fy, cx, cy
        fx, fy = camera_matrix[0, 0], camera_matrix[1, 1]
        cx, cy = camera_matrix[0, 2], camera_matrix[1, 2]
        
        # 由相机投影模型：u = (fx*X + cx*Z)/Z → X = Z*(u - cx)/fx
        #                  v = (fy*Y + cy*Z)/Z → Y = Z*(v - cy)/fy
        # 代入平面方程 aX + bY + cZ + d = 0 → 解Z
        u, v = ic_x, ic_y
        A = plane_eq[0] * (u - cx) / fx + plane_eq[1] * (v - cy) / fy + plane_eq[2]
        B = -plane_eq[3]
        if abs(A) < 1e-6:
            print(f"第{i+1}个点Z计算失败，跳过...")
            continue
        Zc = B / A
        Xc = Zc * (u - cx) / fx
        Yc = Zc * (v - cy) / fy
        Pc = np.array([Xc, Yc, Zc])

        print(f"第{i+1}个3D点：{Pc}")
        pc_list.append(Pc)

    for i in range(NUM_CALIB_POINTS):
        X_c = pc_list[i]
        x_p = ip_list[i]
        correspondences.append((X_c, x_p))

    # proj.setResolution(PROJ_RES)  # 设置投影仪分辨率

    # while len(correspondences) < 42:  # 论文采集42个对应点
    #     # 1. 投影十字线到硬纸板
    #     cross_img = np.zeros((PROJ_RES[1], PROJ_RES[0], 3), dtype=np.uint8)
    #     cross_center = (PROJ_RES[0]//2, PROJ_RES[1]//2)
    #     cv2.line(cross_img, (cross_center[0]-CROSS_SIZE, cross_center[1]), 
    #              (cross_center[0]+CROSS_SIZE, cross_center[1]), (0,255,0), 2)
    #     cv2.line(cross_img, (cross_center[0], cross_center[1]-CROSS_SIZE), 
    #              (cross_center[0], cross_center[1]+CROSS_SIZE), (0,255,0), 2)
    #     proj.project(cross_img)  # 投影仪投影十字线（需根据投影仪SDK实现proj.project）

    #     # 2. 相机捕获图像并检测硬纸板与十字线
    #     ret, frame = cap.read()
    #     if not ret:
    #         continue
    #     undistorted = cv2.undistort(frame, K, dist)  # 去畸变
    #     hsv = cv2.cvtColor(undistorted, cv2.COLOR_BGR2HSV)
    #     green_mask = cv2.inRange(hsv, GREEN_LOW, GREEN_HIGH)
    #     cross_contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    #     # 3. 检测硬纸板（四边形）
    #     gray = cv2.cvtColor(undistorted, cv2.COLOR_BGR2GRAY)
    #     edges = cv2.Canny(gray, *CANNY_THRESH)
    #     lines = cv2.HoughLinesP(edges, HOUGH_RHO, HOUGH_THETA, HOUGH_THRESH, 
    #                             minLineLength=QUAD_SIDE_MIN, maxLineGap=10)
    #     if lines is None or len(cross_contours) == 0:
    #         continue

    #     # 4. 提取硬纸板3D坐标（P4P算法）
    #     cardboard_corners = detect_quadrilateral(edges, lines)  # 后续实现的四边形检测函数
    #     if cardboard_corners is None:
    #         continue
    #     # P4P求解硬纸板中心的3D坐标（硬纸板中心=十字线中心）
    #     cross_center_cam = cv2.minEnclosingCircle(max(cross_contours, key=cv2.contourArea))[0]
    #     # 假设硬纸板中心为世界原点，计算其在相机坐标系下的3D坐标（简化版P4P）
    #     # 完整P4P需调用cv2.solvePnP，此处省略硬纸板角点检测，直接用简化示例
    #     _, rvec, tvec = cv2.solvePnP(
    #         np.array([[0,0,0], [CARDBOARD_SIZE[0],0,0], [0,CARDBOARD_SIZE[1],0], [CARDBOARD_SIZE[0],CARDBOARD_SIZE[1],0]], dtype=np.float32),
    #         cardboard_corners, K, dist
    #     )
    #     X_c = tvec.flatten()  # 硬纸板中心（十字线中心）的3D坐标
    #     x_p = cross_center    # 十字线在投影仪上的2D坐标

    #     correspondences.append((X_c, x_p))
    #     print(f"已采集 {len(correspondences)}/42 个对应点")
    #     cv2.drawContours(undistorted, [np.int32(cardboard_corners)], 0, (0,0,255), 2)
    #     cv2.circle(undistorted, np.int32(cross_center_cam), 5, (255,0,0), -1)
    #     cv2.imshow("Correspondence Collection", undistorted)
    #     cv2.waitKey(1000)
    # cv2.destroyAllWindows()
    return correspondences


def estimate_proj_matrix(correspondences):
    """步骤3：用SVD+RANSAC估计投影矩阵G"""
    # 构造线性方程组 A·g = 0，其中g是G的列向量展开（12维）
    A = []
    for X_c, x_p in correspondences:
        X, Y, Z = X_c
        u, v = x_p
        # 每个对应点生成2行方程（来自 λ·u = G1·X^c, λ·v = G2·X^c, λ = G3·X^c）
        A.append([X, Y, Z, 1, 0, 0, 0, 0, -u*X, -u*Y, -u*Z, -u])
        A.append([0, 0, 0, 0, X, Y, Z, 1, -v*X, -v*Y, -v*Z, -v])
    A = np.array(A, dtype=np.float32)

    # 创建自定义估计器用于RANSAC
    class ProjectionMatrixEstimator(BaseEstimator):
        def __init__(self):
            self.coef_ = None

        def fit(self, X, y=None):
            # 使用SVD求解投影矩阵
            U, S, VT = np.linalg.svd(X, full_matrices=False)
            self.coef_ = VT[-1].reshape(3, 4)  # 最小奇异值对应的特征向量即G
            return self

        def predict(self, X):
            # 计算重投影误差用于RANSAC
            if self.coef_ is None:
                raise ValueError("Model not fitted yet")
            # 这里返回零向量，因为我们主要关心的是fit过程
            return np.zeros(X.shape[0])

        def score(self, X, y=None):
            # 返回负的重投影误差作为得分
            if self.coef_ is None:
                return -np.inf
            # 简单返回0，RANSAC会使用residual_threshold来判断内点
            return 0.0

    # RANSAC鲁棒估计
    estimator = ProjectionMatrixEstimator()
    ransac = RANSACRegressor(
        base_estimator=estimator,
        residual_threshold=2.0,  # 重投影误差阈值（像素）
        random_state=42
    )

    # 适配RANSAC输入格式（X为A，y为零向量）
    ransac.fit(A, np.zeros(A.shape[0]))
    G = ransac.estimator_.coef_
    
    return G


def detect_quadrilateral(edges, lines):
    """从检测到的直线中找到四边形"""
    if lines is None or len(lines) < 4:
        return None

    # 将线段转换为直线方程 ax + by + c = 0
    line_equations = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        # 计算直线方程系数
        a = y2 - y1
        b = x1 - x2
        c = x2 * y1 - x1 * y2
        # 归一化
        norm = np.sqrt(a*a + b*b)
        if norm > 0:
            line_equations.append((a/norm, b/norm, c/norm))

    if len(line_equations) < 4:
        return None

    # 寻找四条最佳直线组成四边形
    best_quad = None
    max_area = 0

    # 简化版本：取前四条线段的交点
    if len(line_equations) >= 4:
        # 计算相邻直线的交点
        intersections = []
        for i in range(4):
            j = (i + 1) % 4
            a1, b1, c1 = line_equations[i]
            a2, b2, c2 = line_equations[j]

            # 求交点
            det = a1 * b2 - a2 * b1
            if abs(det) > 1e-6:
                x = (b1 * c2 - b2 * c1) / det
                y = (a2 * c1 - a1 * c2) / det
                intersections.append([x, y])

        if len(intersections) == 4:
            quad = np.array(intersections, dtype=np.float32)
            # 检查四边形是否合理（面积大于阈值）
            area = cv2.contourArea(quad)
            if area > 1000:  # 最小面积阈值
                return quad

    return None


def recover_3d_points(quad_2d, G, K, dist):
    """步骤1：用SVD求解3D顶点（初始解）

    根据方程组：
    a * quad_2d_proj = G * X_hom  (投影仪投影方程)
    b * quad_2d = K * X           (相机投影方程)

    其中a和b是标量，X是未知的3D点，使用SVD方法求解X
    K是3x3相机内参矩阵，不能简化
    """
    # 投影仪对应的四边形坐标（假设为投影仪屏幕的四个角点）
    quad_2d_proj = np.array([
        [0, 0],
        [CARDBOARD_SIZE[0] - 1, 0],
        [CARDBOARD_SIZE[0] - 1, CARDBOARD_SIZE[1] - 1],
        [0, CARDBOARD_SIZE[1] - 1]
    ], dtype=np.float32)

    # 转换为齐次坐标
    quad_2d_proj_hom = np.concatenate((quad_2d_proj, np.ones((4,1))), axis=1)

    # 提取相机内参
    fx, fy = K[0,0], K[1,1]
    cx, cy = K[0,2], K[1,2]

    X_list = []

    G1, G2, G3 = G[0], G[1], G[2]
    K1, K2, K3 = K[0], K[1], K[2]
    
    for i in range(len(quad_2d)):
        u_cam, v_cam = quad_2d[i]  # 相机像素坐标
        u_proj, v_proj, w_proj = quad_2d_proj_hom[i]  # 投影仪齐次坐标

        # 构造线性方程组 A * X_hom = 0
        # 来自两个约束方程：
        # 1. a * [u_proj, v_proj, w_proj] = G * [X, Y, Z, 1]^T
        # 2. b * [u_cam, v_cam, 1] = K * [X, Y, Z]^T

        # 投影仪约束：消除标量a
        # u_proj * G[2,:] - w_proj * G[0,:] = 0
        # v_proj * G[2,:] - w_proj * G[1,:] = 0

        # 相机约束：相机投影方程 s * [u_cam, v_cam, 1]^T = K * [X, Y, Z]^T
        # 展开为：s * u_cam = fx*X + cx*Z, s * v_cam = fy*Y + cy*Z, s * 1 = Z
        # 消除标量s：u_cam * Z = fx*X + cx*Z, v_cam * Z = fy*Y + cy*Z
        # 重新整理：fx*X - u_cam*Z + cx*Z = 0, fy*Y - v_cam*Z + cy*Z = 0
        # 即：fx*X + (cx - u_cam)*Z = 0, fy*Y + (cy - v_cam)*Z = 0

        A = np.array([
            # 投影仪约束方程1：u_proj * G[2,:] - w_proj * G[0,:] = 0
            [v_proj * G[2, 0] - w_proj * G[1, 0], v_proj * G[2, 1] - w_proj * G[1, 1], v_proj * G[2, 2] - w_proj * G[1, 2]],

            # 投影仪约束方程2：v_proj * G[2,:] - w_proj * G[1,:] = 0
            [u_proj * G[2, 0] - w_proj * G[0, 0], u_proj * G[2, 1] - w_proj * G[0, 1], u_proj * G[2, 2] - w_proj * G[0, 2]],

            # 相机约束方程1：fx*X + (cx - u_cam)*Z = 0
            [fx, 0, cx - u_cam],

            # 相机约束方程2：fy*Y + (cy - v_cam)*Z = 0
            [0, fy, cy - v_cam]
        ], dtype=np.float32)

        D = np.array([w_proj * G[1,3] - v_proj * G[2,3], w_proj * G[0,3] - u_proj * G[2,3], 0, 0], dtype=np.float32)
        # A[0] = A[0] * 1000
        # D[0] = D[0] * 1000
        # A[1] = A[1] * 1000
        # D[1] = D[1] * 1000
        X_3d, residuals, _, _ = np.linalg.lstsq(A, D, rcond=None)
        print("residuals", residuals)
        print(A,A @ X_3d, D)
        # 使用SVD求解齐次线性方程组 A * X_hom = 0
        # 解为最小奇异值对应的右奇异向量
        # _, _, VT = np.linalg.svd(A)
        # X_hom = VT[-1]  # 最小奇异值对应的右奇异向量

        # 齐次坐标归一化得到3D点
        # if abs(X_hom[3]) > 1e-8:
        #     X_3d = X_hom[:3] / X_hom[3]
        # else:
        #     # 如果齐次坐标的最后一个分量接近0，说明点在无穷远处，使用前三个分量
        #     X_3d = X_hom[:3]
        X,Y,Z = X_3d
        u_pred = (K1 @ X_3d) / (K3 @ X_3d + 1e-6)
        v_pred = (K2 @ X_3d) / (K3 @ X_3d + 1e-6)

        Xi_hom = np.append(X_3d, 1)
        alpha_pred = (G1 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
        beta_pred = (G2 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
        print(X_3d, alpha_pred, beta_pred, quad_2d_proj_hom[i])
        X_list.append(X_3d)
    
    X_list = np.array(X_list, dtype=np.float32)
    G1, G2, G3 = G[0], G[1], G[2]
    K1, K2, K3 = K[0], K[1], K[2]

    for i in range(4):
        Xi = X_list[i]
        Xi_hom = np.append(Xi, 1)  # 转换为齐次坐标 [X, Y, Z, 1]
        alpha_pred = (G1 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
        beta_pred = (G2 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
        print(i, alpha_pred, beta_pred, quad_2d_proj[i])

    for i in range(4):
        Xi = X_list[i]
        u_pred = (K1 @ Xi) / (K3 @ Xi + 1e-6)
        v_pred = (K2 @ Xi) / (K3 @ Xi + 1e-6)
        print(i, u_pred, v_pred, quad_2d[i])
    return X_list


def coplanar_optimization(X_init, quad_2d, G, K):
    """步骤2：共面约束优化（Levenberg-Marquardt）"""
    G1, G2, G3 = G[0], G[1], G[2]
    K1, K2, K3 = K[0], K[1], K[2]
    quad_2d = quad_2d.reshape(4,2)
    quad_2d_proj = np.array([
        [0, 0],
        [CARDBOARD_SIZE[0] - 1, 0],
        [CARDBOARD_SIZE[0] - 1, CARDBOARD_SIZE[1] - 1],
        [0, CARDBOARD_SIZE[1] - 1]
    ], dtype=np.float32)

    # 转换为齐次坐标
    quad_2d_proj_hom = np.concatenate((quad_2d_proj, np.ones((4,1))), axis=1)
    
    def residuals(X_flat):
        """残差函数：重投影误差 + 共面约束"""
        X = X_flat.reshape(4,3)  # 4个3D点
        res = []
        
        # 1. 相机重投影误差（论文公式中的前两项）
        for i in range(4):
            Xi = X[i]
            u_pred = (K1 @ Xi) / (K3 @ Xi + 1e-6)
            v_pred = (K2 @ Xi) / (K3 @ Xi + 1e-6)
            res.append(np.square(u_pred - quad_2d[i][0]))
            res.append(np.square(v_pred - quad_2d[i][1]))
        
        # 2. 投影仪重投影误差（论文公式中的中间两项）
        for i in range(4):
            Xi = X[i]
            Xi_hom = np.append(Xi, 1)  # 转换为齐次坐标 [X, Y, Z, 1]
            alpha_pred = (G1 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
            beta_pred = (G2 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
            res.append(np.square(alpha_pred - quad_2d_proj[i][0]))  # 简化：投影仪对应点假设为屏幕四角
            res.append(np.square(beta_pred - quad_2d_proj[i][1]))
        
        # 3. 共面约束（论文公式中的最后一项）
        X1, X2, X3, X4 = X
        vec14 = X4 - X1
        vec12 = X2 - X1
        vec13 = X3 - X1
        coplanar_err = np.dot(vec14, np.cross(vec12, vec13))
        res.append(np.abs(COPLANAR_WEIGHT * coplanar_err))
        #print("res", res)
        return np.array(res, dtype=np.float32)
    
    # 优化：初始值为SVD解，用Levenberg-Marquardt最小化残差
    X_flat_init = X_init.flatten()
    result = least_squares(
        residuals, X_flat_init, method='lm',
        max_nfev=LM_MAX_ITER, ftol=LM_TOL
    )
    X_optimized = result.x.reshape(4,3)
    return X_optimized

def find_inscribed_rectangle(pts_3d):
    """从3D四边形X_quad中求解内接矩形（顶边与四边形顶边重合）"""
    import numpy as np
    from scipy.optimize import minimize
    from shapely.geometry import Polygon, Point

    # 步骤1: 计算平面法向量
    v1 = pts_3d[1] - pts_3d[0]
    v2 = pts_3d[3] - pts_3d[0]
    normal = np.cross(v1, v2)
    normal /= np.linalg.norm(normal)

    # 步骤2: 构建局部坐标系 (u, v)
    u_axis = pts_3d[1] - pts_3d[0]
    u_axis -= np.dot(u_axis, normal) * normal  # 去除法向分量（确保在平面内）
    u_axis /= np.linalg.norm(u_axis)
    v_axis = np.cross(normal, u_axis)
    v_axis /= np.linalg.norm(v_axis)

    # 原点
    origin = pts_3d[0]

    # 投影函数：3D -> 2D (u, v)
    def project_to_2d(p):
        vec = p - origin
        u = np.dot(vec, u_axis)
        v = np.dot(vec, v_axis)
        return np.array([u, v])

    # 将四边形投影到2D
    pts_2d = np.array([project_to_2d(p) for p in pts_3d])

    # 创建Shapely多边形用于点包含测试
    poly_2d = Polygon(pts_2d)

    # 高宽比 r = height / width = 0.8
    r = 0.8

    # 定义目标函数：最大化面积 = w * h = w * (r * w) = r * w^2 → 最小化 -r*w^2
    def objective(x):
        # x = [cx, cy, w]  # 中心x, 中心y, 宽度
        cx, cy, w = x
        h = r * w
        if w <= 0:
            return 1e6
        # 四个角点（轴对齐矩形）
        corners = [
            (cx - w/2, cy - h/2),
            (cx + w/2, cy - h/2),
            (cx + w/2, cy + h/2),
            (cx - w/2, cy + h/2)
        ]
        # 检查是否所有角点都在多边形内
        for pt in corners:
            if not poly_2d.contains(Point(pt)) and not poly_2d.touches(Point(pt)):
                return 1e6  # 惩罚项
        return -r * w * w  # 负面积（最大化）

    # 获取2D点的范围作为初始猜测和边界
    u_vals, v_vals = pts_2d[:, 0], pts_2d[:, 1]
    u_min, u_max = u_vals.min(), u_vals.max()
    v_min, v_max = v_vals.min(), v_vals.max()

    # 初始猜测：中心在多边形中心，宽度为范围的一半
    init_w = (u_max - u_min) * 0.5
    init_cx = (u_max + u_min) / 2
    init_cy = (v_max + v_min) / 2

    # 优化变量边界
    bounds = [
        (u_min, u_max),      # cx
        (v_min, v_max),      # cy
        (1e-3, u_max - u_min)  # w > 0
    ]

    # 执行优化
    result = minimize(objective, x0=[init_cx, init_cy, init_w], bounds=bounds, method='L-BFGS-B')

    if result.success:
        cx_opt, cy_opt, w_opt = result.x
        h_opt = r * w_opt
        print(f"Optimal rectangle in 2D:")
        print(f"Center: ({cx_opt:.3f}, {cy_opt:.3f})")
        print(f"Width: {w_opt:.3f}, Height: {h_opt:.3f}")
        print(f"Area: {w_opt * h_opt:.3f}")

        # 构造2D角点
        rect_2d = np.array([
            [cx_opt - w_opt/2, cy_opt - h_opt/2],
            [cx_opt + w_opt/2, cy_opt - h_opt/2],
            [cx_opt + w_opt/2, cy_opt + h_opt/2],
            [cx_opt - w_opt/2, cy_opt + h_opt/2]
        ])

        # 转换回3D
        def unproject_to_3d(p2d):
            return origin + p2d[0] * u_axis + p2d[1] * v_axis

        rect_3d = np.array([unproject_to_3d(p) for p in rect_2d])
        print("\n3D coordinates of the rectangle:")
        for i, p in enumerate(rect_3d):
            print(f"Point {i}: [{p[0]:.6f}, {p[1]:.6f}, {p[2]:.6f}]")
    else:
        print("Optimization failed:", result.message)
    return rect_3d

def pre_warp_image(orig_img, rect_3d, G):
    """预扭曲原始图像：将图像映射到内接矩形对应的投影仪区域"""
    # 1. 计算内接矩形在投影仪上的2D对应点（通过G投影）
    rect_proj = []
    for X in rect_3d:
        X_homo = np.append(X, 1)  # 齐次3D点
        proj_homo = G @ X_homo.T
        proj_xy = (proj_homo[:2] / proj_homo[2]).astype(np.float32)
        rect_proj.append(proj_xy)
    rect_proj = np.array(rect_proj)
    
    # 2. 计算原始图像到投影仪内接区域的单应矩阵
    orig_pts = np.array([[0,0], [orig_img.shape[1],0], [orig_img.shape[1],orig_img.shape[0]], [0,orig_img.shape[0]]], dtype=np.float32)
    H_warp, _ = cv2.findHomography(orig_pts, rect_proj)
    
    # 3. 预扭曲图像（添加绿色边框，论文要求）
    warped_img = cv2.warpPerspective(orig_img, H_warp, PROJ_RES)
    # 绘制绿色边框
    cv2.polylines(warped_img, [np.int32(rect_proj)], isClosed=True, color=(0,255,0), thickness=2)
    return warped_img

def are_coplanar(points, tol=1):
    """
    判断四个三维点是否共面。
    
    参数:
        points: shape (4, 3) 的 array，表示四个三维点
        tol: 容差阈值，默认 1e-6
    
    返回:
        bool: 共面返回 True，否则 False
    """
    points = np.array(points)
    if points.shape != (4, 3):
        raise ValueError("输入必须是 4 个三维点，形状为 (4, 3)")
    
    # 构造三个向量
    v1 = points[1] - points[0]
    v2 = points[2] - points[0]
    v3 = points[3] - points[0]
    
    # 计算混合积：v1 · (v2 × v3)
    scalar_triple_product = np.dot(v1, np.cross(v2, v3))
    print("scalar_triple_product", scalar_triple_product)
    return abs(scalar_triple_product) < tol

def main():
    # 1. 初始化硬件（需根据实际设备调整，此处为模拟）
   
    # proj = Projector()  # 投影仪控制（需根据投影仪SDK实现）
    
    # 2. 离线校准（仅首次运行）
    #K, dist = calibrate_camera(["chessboard1.jpg", "chessboard2.jpg"])  # 棋盘格图像路径
    correspondences = collect_correspondences()
    K= np.load(r"D:\Program\Project\project\czcv_camera_new\algo1\Kc.npy")
    dist = np.zeros(5)  # 假设无畸变或已去畸变
    G = estimate_proj_matrix(correspondences)
    np.save("K.npy", K), np.save("dist.npy", dist), np.save("G.npy", G)
    
    # 加载预校准参数（后续运行）
    K = np.load("K.npy")
    dist = np.load("dist.npy")
    G = np.load("G.npy")
    G = np.load(r"D:\Program\Project\project\czcv_camera_new\algo1_2\G.npy")
    print("投影矩阵G:\n", G)
    reproj_quad = np.array([[160,106], [1084,93], [1179,650], [62,638]], dtype=np.float32)

    reproj_quad = np.array([[174, 107], [1096, 93],[1194, 649],[76, 638]], dtype=np.float32)

    #reproj_quad = np.array([[137, 104], [1109, 94],[1207, 650],[31, 639]], dtype=np.float32)
   
    # 步骤2：恢复3D区域并优化共面性
    X_init = recover_3d_points(reproj_quad, G, K, dist)

    print("X_init", X_init)
    X_optimized = coplanar_optimization(X_init, reproj_quad, G, K)
    print("X_optimized", X_optimized)


    quad_2d_proj = np.array([
        [0, 0],
        [CARDBOARD_SIZE[0] - 1, 0],
        [CARDBOARD_SIZE[0] - 1, CARDBOARD_SIZE[1] - 1],
        [0, CARDBOARD_SIZE[1] - 1]
    ], dtype=np.float32)

    G1, G2, G3 = G[0], G[1], G[2]
    K1, K2, K3 = K[0], K[1], K[2]

    for i in range(4):
        Xi = X_optimized[i]
        Xi_hom = np.append(Xi, 1)  # 转换为齐次坐标 [X, Y, Z, 1]
        alpha_pred = (G1 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
        beta_pred = (G2 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
        print(i, alpha_pred, beta_pred, quad_2d_proj[i])

    for i in range(4):
        Xi = X_optimized[i]
        u_pred = (K1 @ Xi) / (K3 @ Xi + 1e-6)
        v_pred = (K2 @ Xi) / (K3 @ Xi + 1e-6)
        print(i, u_pred, v_pred, reproj_quad[i])

    are_coplanar(X_optimized)
    # 步骤3：求解内接矩形并预扭曲图像
    rect_3d = find_inscribed_rectangle(X_optimized)
    print("rect_3d", rect_3d)

    rect_3d_hom = np.array([np.append(X, 1) for X in rect_3d])
    rect_2d_proj = (G @ rect_3d_hom.T).T
    print(rect_3d_hom.shape, rect_2d_proj.shape)
    rect_2d_proj = rect_2d_proj[:, :2] / rect_2d_proj[:, 2:]
    print("rect_2d_proj", rect_2d_proj)
    print("quad_2d_proj", quad_2d_proj) 

    H, mask = cv2.findHomography(quad_2d_proj, rect_2d_proj, method=0) 

    img = cv2.imread(r"D:\Dataset\keystone\20251027-113839.png")
    warped = cv2.warpPerspective(img, H, (img.shape[1], img.shape[0]))
    cv2.imwrite("warped_image.jpg", warped)

    def decompose_homography_to_similarity(H):
        """
        从 3x3 单应性矩阵 H 中近似提取 2D 旋转角度、均匀缩放和平移。
        
        假设 H 近似表示一个相似变换（旋转 + 均匀缩放 + 平移）。
        
        返回:
            angle_rad: 旋转角度（弧度）
            angle_deg: 旋转角度（度）
            scale:     均匀缩放因子
            tx, ty:    平移分量
            R:         2x2 旋转矩阵
        """
        # Step 1: 归一化 H，使 H[2,2] = 1
        if abs(H[2, 2]) < 1e-8:
            raise ValueError("Invalid homography matrix: H[2,2] is zero.")
        H_norm = H / H[2, 2]

        # Step 2: 提取仿射部分（忽略透视项）
        # 如果 H 是纯仿射（即 H[2,0] = H[2,1] = 0），这很准确
        # 否则这是近似（假设透视畸变小）
        A = H_norm[:2, :2]  # 线性部分
        t = H_norm[:2, 2]   # 平移部分

        # Step 3: 对 A 进行 SVD，用于极分解
        U, S_vals, Vt = np.linalg.svd(A)
        R = U @ Vt  # 最接近 A 的旋转矩阵（Frobenius 范数意义下）

        # 强制 det(R) = 1（避免反射）
        if np.linalg.det(R) < 0:
            U[:, -1] *= -1
            R = U @ Vt

        # Step 4: 计算均匀缩放（取奇异值的平均）
        scale = np.mean(S_vals)

        # Step 5: 计算旋转角度
        angle_rad = np.arctan2(R[1, 0], R[0, 0])
        angle_deg = np.degrees(angle_rad)

        tx, ty = t[0], t[1]

        return {
            'angle_rad': angle_rad,
            'angle_deg': angle_deg,
            'scale': scale,
            'tx': tx,
            'ty': ty,
            'R': R,
            't': t
        }

    result = decompose_homography_to_similarity(H)

    print("\nDecomposed Similarity Parameters:")
    print(f"Rotation angle: {result['angle_deg']:.2f} degrees")
    print(f"Scale: {result['scale']:.4f}")
    print(f"Translation: tx = {result['tx']:.2f}, ty = {result['ty']:.2f}")
    print(f"Rotation matrix R:\n{result['R']}")

    # 创建测试图像（如果不存在）
    # test_img_path = "test_image.jpg"
    # if not os.path.exists(test_img_path):
    #     # 创建一个简单的测试图像
    #     test_img = np.zeros((480, 640, 3), dtype=np.uint8)
    #     test_img[:] = (100, 150, 200)  # 填充颜色
    #     cv2.putText(test_img, "Test Image", (200, 240), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    #     cv2.imwrite(test_img_path, test_img)
    #     print(f"创建测试图像: {test_img_path}")

    # orig_img = cv2.imread(test_img_path)
    # if orig_img is not None:
    #     warped_img = pre_warp_image(orig_img, rect_3d, G)
    #     print("成功生成预扭曲图像")
    # else:
    #     print("无法加载测试图像")

    # cv2.imwrite("warped_image.jpg", warped_img)
    
    # 3. 初始投影区域检测
    # print("初始化投影区域检测...")
    # init_quad = None
    # while init_quad is None:
    #     ret, frame = True, None
    #     if not ret:
    #         continue
    #     undistorted = cv2.undistort(frame, K, dist)
    #     hsv = cv2.cvtColor(undistorted, cv2.COLOR_BGR2HSV)
    #     green_mask = cv2.inRange(hsv, GREEN_LOW, GREEN_HIGH)
    #     edges = cv2.Canny(cv2.cvtColor(undistorted, cv2.COLOR_BGR2GRAY), *CANNY_THRESH)
    #     lines = cv2.HoughLinesP(edges, HOUGH_RHO, HOUGH_THETA, HOUGH_THRESH, 
    #                             minLineLength=QUAD_SIDE_MIN, maxLineGap=10)
    #     if lines is not None:
    #         init_quad = detect_quadrilateral(edges, lines)
    #     if init_quad is not None:
    #         cv2.drawContours(undistorted, [np.int32(init_quad)], 0, (0,255,0), 2)
    #     cv2.imshow("Initial Detection", undistorted)
    #     cv2.waitKey(100)
    # cv2.destroyAllWindows()
    
    # # 4. 初始化粒子滤波跟踪器
    # tracker = ParticleFilterTracker(init_quad, G, K)
    
    # # 5. 实时校正循环
    # print("开始实时校正...")
    # while True:
    #     ret, frame =True, None
    #     if not ret:
    #         break
    #     undistorted = cv2.undistort(frame, K, dist)
    #     gray = cv2.cvtColor(undistorted, cv2.COLOR_BGR2GRAY)
    #     edges = cv2.Canny(gray, *CANNY_THRESH)
        
    #     # 步骤1：粒子滤波跟踪投影区域
    #     best_state = tracker.update(edges)
    #     # 从最佳状态重投影四边形（用于可视化）
    #     H = tracker._compute_homography(best_state)
    #     reproj_quad = cv2.perspectiveTransform(tracker.proj_pts.reshape(-1,1,2), H).reshape(4,2)
    #     cv2.drawContours(undistorted, [np.int32(reproj_quad)], 0, (255,0,0), 2)
        

        
        
    #     # 步骤4：投影预扭曲图像（需投影仪SDK支持）
    #     # proj.project(warped_img)
        
    #     # 可视化结果
    #     cv2.imshow("Tracking Result", undistorted)
    #     cv2.imshow("Pre-Warped Image", cv2.resize(warped_img, (640, 512)))  # 缩小显示
        
    #     if cv2.waitKey(1) & 0xFF == ord('q'):
    #         break
    
    # # 释放资源
    
    # cv2.destroyAllWindows()
    # # proj.close()

if __name__ == "__main__":
    main()