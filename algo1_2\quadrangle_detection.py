import cv2
import numpy as np
from math import atan2, degrees

# -------------------------- 1. 线段处理工具函数 --------------------------
def line_length(line):
    """计算线段长度（line格式：(x1,y1,x2,y2)）"""
    x1, y1, x2, y2 = line
    return np.sqrt((x2 - x1)**2 + (y2 - y1)**2)

def line_angle(line):
    """计算线段与X轴的夹角（0-180度）"""
    x1, y1, x2, y2 = line
    dx = x2 - x1
    dy = y2 - y1
    return degrees(atan2(dy, dx)) % 180

def line_overlap(line1, line2):
    """计算两条线段的重叠长度（s3）"""
    # 将线段转换为点集（简化计算，取100个采样点）
    def line_to_points(line, num_points=100):
        x1, y1, x2, y2 = line
        xs = np.linspace(x1, x2, num_points)
        ys = np.linspace(y1, y2, num_points)
        return set(zip(xs.round().astype(int), ys.round().astype(int)))
    
    pts1 = line_to_points(line1)
    pts2 = line_to_points(line2)
    overlap_pts = pts1.intersection(pts2)
    if not overlap_pts:
        return 0.0
    # 计算重叠点的最大距离（即重叠长度）
    overlap_pts = list(overlap_pts)
    max_dist = 0.0
    for i in range(len(overlap_pts)):
        for j in range(i+1, len(overlap_pts)):
            dx = overlap_pts[i][0] - overlap_pts[j][0]
            dy = overlap_pts[i][1] - overlap_pts[j][1]
            dist = np.sqrt(dx**2 + dy**2)
            if dist > max_dist:
                max_dist = dist
    return max_dist

# -------------------------- 2. 四边形检测主函数 --------------------------
def detect_quadrangle(frame, camera_matrix, dist_coeffs, min_line_len=50, overlap_thresh=0.7):
    """
    输入：
        frame: 相机原始图像
        camera_matrix: 相机内参
        dist_coeffs: 畸变系数
        min_line_len: 线段最小长度阈值
        overlap_thresh: 重叠率阈值（≥0.7，论文参数）
    输出：
        quad_corners: 四边形4个角点（左上→右上→右下→左下，None表示未检测到）
        frame_draw: 绘制检测结果的图像
    """
    # 1. 图像预处理：畸变矫正→灰度→边缘检测
    frame_undistort = cv2.undistort(frame, camera_matrix, dist_coeffs)
    gray = cv2.cvtColor(frame_undistort, cv2.COLOR_BGR2GRAY)
    blur = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blur, 50, 150)  # Canny阈值需根据环境调整
    
    # 2. 霍夫变换提取线段（cv2.HoughLinesP）
    lines = cv2.HoughLinesP(
        edges, rho=1, theta=np.pi/180, threshold=50,
        minLineLength=min_line_len, maxLineGap=20
    )
    if lines is None:
        return None, frame_undistort
    lines = [line[0] for line in lines]  # 转换为列表格式：[(x1,y1,x2,y2), ...]
    
    # 3. 筛选线段（长度≥min_line_len）
    lines_filtered = [line for line in lines if line_length(line) >= min_line_len]
    if len(lines_filtered) < 4:
        return None, frame_undistort
    
    # 4. 组合线段为四边形（遍历所有4线段组合，验证论文4个条件）
    quad_candidates = []
    n_lines = len(lines_filtered)
    for i in range(n_lines):
        for j in range(i+1, n_lines):
            for k in range(j+1, n_lines):
                for l in range(k+1, n_lines):
                    quad_lines = [lines_filtered[i], lines_filtered[j], lines_filtered[k], lines_filtered[l]]
                    
                    # 条件1：四条线段长度均≥min_line_len（已满足）
                    # 条件2：对边长度相似（误差≤20%）
                    lengths = [line_length(line) for line in quad_lines]
                    lengths.sort()
                    if not (abs(lengths[0] - lengths[1]) / lengths[0] <= 0.2 and
                            abs(lengths[2] - lengths[3]) / lengths[2] <= 0.2):
                        continue
                    
                    # 条件3：四个角的角度在30°-150°之间
                    # 简化计算：四条线段的夹角（取相邻线段的角度差）
                    angles = [line_angle(line) for line in quad_lines]
                    angle_diffs = [abs(angles[(m+1)%4] - angles[m]) % 180 for m in range(4)]
                    if not all(30 <= diff <= 150 for diff in angle_diffs):
                        continue
                    
                    # 条件4：每条边的重叠率≥overlap_thresh（论文公式15）
                    overlap_ok = True
                    for line in quad_lines:
                        # 计算该线段与其他三条线段的最大重叠率
                        max_r1 = 0.0
                        max_r2 = 0.0
                        for other_line in quad_lines:
                            if line is other_line:
                                continue
                            s3 = line_overlap(line, other_line)
                            s1 = line_length(line)
                            s2 = line_length(other_line)
                            if s1 == 0 or s2 == 0:
                                continue
                            r1 = s3 / s1
                            r2 = s3 / s2
                            if r1 > max_r1:
                                max_r1 = r1
                            if r2 > max_r2:
                                max_r2 = r2
                        if max_r1 < overlap_thresh or max_r2 < overlap_thresh:
                            overlap_ok = False
                            break
                    if not overlap_ok:
                        continue
                    
                    # 满足所有条件，添加为候选四边形
                    quad_candidates.append(quad_lines)
    
    if not quad_candidates:
        return None, frame_undistort
    
    # 5. 选择最优四边形（面积最大）
    def quad_area(quad_lines):
        """计算四边形面积（通过线段交点）"""
        # 简化：取每条线段的中点，构成四边形并计算面积
        midpoints = []
        for line in quad_lines:
            x1, y1, x2, y2 = line
            mid_x = (x1 + x2) / 2
            mid_y = (y1 + y2) / 2
            midpoints.append((mid_x, mid_y))
        if len(midpoints) != 4:
            return 0.0
        #  shoelace公式计算面积
        x = [p[0] for p in midpoints]
        y = [p[1] for p in midpoints]
        return 0.5 * abs(sum(x[i]*y[(i+1)%4] - x[(i+1)%4]*y[i] for i in range(4)))
    
    best_quad = max(quad_candidates, key=quad_area)
    
    # 6. 计算四边形角点（线段交点）
    def line_intersection(line1, line2):
        """计算两条线段的交点（若存在）"""
        x1, y1, x2, y2 = line1
        x3, y3, x4, y4 = line2
        denom = (x1 - x2)*(y3 - y4) - (y1 - y2)*(x3 - x4)
        if denom == 0:
            return None  # 平行线，无交点
        t = ((x1 - x3)*(y3 - y4) - (y1 - y3)*(x3 - x4)) / denom
        u = -((x1 - x2)*(y1 - y3) - (y1 - y2)*(x1 - x3)) / denom
        if 0 <= t <= 1 and 0 <= u <= 1:
            x = x1 + t*(x2 - x1)
            y = y1 + t*(y2 - y1)
            return (int(x), int(y))
        return None
    
    # 计算四条线段的交点（取有效交点）
    corners = []
    for i in range(4):
        line_a = best_quad[i]
        line_b = best_quad[(i+1)%4]
        intersect = line_intersection(line_a, line_b)
        if intersect is not None:
            corners.append(intersect)
    # 确保只有4个角点（去重）
    corners = list(set(corners))
    if len(corners) != 4:
        return None, frame_undistort
    
    # 7. 角点排序（左上→右上→右下→左下，确保单应矩阵计算正确）
    corners = np.array(corners, dtype=np.float32)
    # 按x+y排序（左上最小，右下最大）
    corners = corners[np.argsort(corners.sum(axis=1))]
    # 前两个点按x排序（左上→右上）
    corners[:2] = corners[:2][np.argsort(corners[:2, 0])]
    # 后两个点按x排序（左下→右下）
    corners[2:] = corners[2:][np.argsort(corners[2:, 0])]
    # 交换左下和右下（确保顺序正确）
    corners[2], corners[3] = corners[3], corners[2]
    
    # 8. 绘制检测结果
    frame_draw = frame_undistort.copy()
    for i in range(4):
        cv2.line(frame_draw, tuple(corners[i].astype(int)), 
                 tuple(corners[(i+1)%4].astype(int)), (0, 255, 0), 2)
        cv2.circle(frame_draw, tuple(corners[i].astype(int)), 5, (0, 0, 255), -1)
    
    return corners, frame_draw

# -------------------------- 测试代码 --------------------------
if __name__ == "__main__":
    # 加载相机内参
    import yaml
    with open("./camera_params.yml", "r") as f:
        params = yaml.safe_load(f)
    camera_matrix = np.array(params["camera_matrix"])
    dist_coeffs = np.array(params["dist_coeffs"])
    
    # 实时检测纸板
    cap = cv2.VideoCapture(0)
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        quad_corners, frame_draw = detect_quadrangle(frame, camera_matrix, dist_coeffs)
        cv2.imshow("Cardboard Quadrangle Detection (Press 'q' to quit)", frame_draw)
        if cv2.waitKey(1) == ord('q'):
            break
    cap.release()
    cv2.destroyAllWindows()