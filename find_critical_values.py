import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.optimize import minimize_scalar
from scipy.optimize import curve_fit
from scipy import stats
from collections import defaultdict
import pandas as pd

plt.rcParams['font.family'] = 'SimHei'        # 或者 'Microsoft YaHei'
# 2. 解决负号“−”显示为方块的问题
plt.rcParams['axes.unicode_minus'] = False

def parse_doa_file(filename):
    """
    解析DOA文件，返回按角度分组的数据
    """
    data = defaultdict(list)
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(', ')
                if len(parts) == 2:
                    angle = float(parts[0])  # 角度a
                    actual_value = float(parts[1])  # 实际值c
                    
                    data[angle].append({
                        'actual': actual_value
                    })
    
    return data

def calculate_distribution_overlap(values1, values2, bins=100):
    """
    计算两个分布的重叠区域，找到最佳分界点
    """
    # 合并所有值来确定范围
    all_values = values1 + values2
    min_val, max_val = min(all_values), max(all_values)
    
    # 创建统一的bin边界
    bin_edges = np.linspace(min_val, max_val, bins + 1)
    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
    
    # 计算两个分布的直方图
    hist1, _ = np.histogram(values1, bins=bin_edges, density=True)
    hist2, _ = np.histogram(values2, bins=bin_edges, density=True)
    
    # 平滑分布
    hist1_smooth = signal.savgol_filter(hist1, window_length=min(11, len(hist1)//2*2+1), polyorder=2)
    hist2_smooth = signal.savgol_filter(hist2, window_length=min(11, len(hist2)//2*2+1), polyorder=2)
    
    # 确保非负
    hist1_smooth = np.maximum(hist1_smooth, 0)
    hist2_smooth = np.maximum(hist2_smooth, 0)
    
    return bin_centers, hist1_smooth, hist2_smooth

def find_critical_point_intersection(bin_centers, hist1, hist2):
    """
    通过找到两个分布曲线的交点来确定临界值
    """
    # 找到两个分布曲线的交点
    diff = hist1 - hist2
    
    # 寻找符号变化的点（交点）
    sign_changes = np.where(np.diff(np.sign(diff)))[0]
    
    if len(sign_changes) == 0:
        # 如果没有交点，使用加权平均
        mean1 = np.average(bin_centers, weights=hist1)
        mean2 = np.average(bin_centers, weights=hist2)
        return (mean1 + mean2) / 2, "weighted_average"
    
    # 如果有多个交点，选择最接近两个分布中心的那个
    mean1 = np.average(bin_centers, weights=hist1)
    mean2 = np.average(bin_centers, weights=hist2)
    center_point = (mean1 + mean2) / 2
    
    best_intersection = None
    min_distance = float('inf')
    
    for idx in sign_changes:
        intersection_point = bin_centers[idx]
        distance = abs(intersection_point - center_point)
        if distance < min_distance:
            min_distance = distance
            best_intersection = intersection_point
    
    return best_intersection, "intersection"

def find_critical_point_error_minimization(values1, values2):
    """
    通过最小化分类错误来找到最佳临界值
    """
    all_values = sorted(values1 + values2)
    min_error = float('inf')
    best_threshold = None
    
    # 尝试每个可能的阈值
    for threshold in all_values:
        # 计算分类错误
        error1 = sum(1 for v in values1 if v > threshold)  # 角度1被错误分类为角度2
        error2 = sum(1 for v in values2 if v <= threshold)  # 角度2被错误分类为角度1
        total_error = error1 + error2
        
        if total_error < min_error:
            min_error = total_error
            best_threshold = threshold
    
    return best_threshold, min_error, len(values1) + len(values2)

def calculate_critical_values(filename):
    """
    计算相邻角度之间的临界c值
    """
    print(f"正在解析文件: {filename}")
    
    # 解析文件
    data = parse_doa_file(filename)
    angles = sorted(data.keys())
    
    print(f"找到 {len(angles)} 个不同的角度: {angles}")
    
    results = []
    
    # 计算相邻角度之间的临界值
    for i in range(len(angles) - 1):
        angle1 = angles[i]
        angle2 = angles[i + 1]
        
        # 提取实际值c
        values1 = [item['actual'] for item in data[angle1]]
        values2 = [item['actual'] for item in data[angle2]]
        
        print(f"\n分析角度 {angle1}° 和 {angle2}° 之间的临界值...")
        print(f"角度 {angle1}°: {len(values1)} 个样本, 范围 [{min(values1):.1f}, {max(values1):.1f}]")
        print(f"角度 {angle2}°: {len(values2)} 个样本, 范围 [{min(values2):.1f}, {max(values2):.1f}]")
        
        # 方法1: 分布交点法
        bin_centers, hist1, hist2 = calculate_distribution_overlap(values1, values2)
        critical_intersection, method = find_critical_point_intersection(bin_centers, hist1, hist2)
        
        # 方法2: 错误最小化法
        critical_error_min, min_error, total_samples = find_critical_point_error_minimization(values1, values2)
        error_rate = min_error / total_samples * 100
        
        # 方法3: 简单中点法
        mean1 = np.mean(values1)
        mean2 = np.mean(values2)
        critical_midpoint = (mean1 + mean2) / 2
        
        # 计算重叠程度
        overlap_min = max(min(values1), min(values2))
        overlap_max = min(max(values1), max(values2))
        overlap_range = max(0, overlap_max - overlap_min)
        total_range = max(max(values1), max(values2)) - min(min(values1), min(values2))
        overlap_percentage = (overlap_range / total_range * 100) if total_range > 0 else 0
        
        result = {
            'angle1': angle1,
            'angle2': angle2,
            'critical_intersection': critical_intersection,
            'critical_error_min': critical_error_min,
            'critical_midpoint': critical_midpoint,
            'min_error_count': min_error,
            'error_rate_percent': error_rate,
            'overlap_percentage': overlap_percentage,
            'samples1': len(values1),
            'samples2': len(values2),
            'range1_min': min(values1),
            'range1_max': max(values1),
            'range2_min': min(values2),
            'range2_max': max(values2),
            'method': method
        }
        
        results.append(result)
        
        print(f"临界值 (交点法): {critical_intersection:.2f}")
        print(f"临界值 (错误最小化): {critical_error_min:.2f} (错误率: {error_rate:.1f}%)")
        print(f"临界值 (中点法): {critical_midpoint:.2f}")
        print(f"分布重叠度: {overlap_percentage:.1f}%")
    
    return pd.DataFrame(results), data

def plot_critical_analysis(df, data, output_path=None):
    """
    绘制临界值分析图表
    """
    n_pairs = len(df)
    fig, axes = plt.subplots(n_pairs, 2, figsize=(16, 4 * n_pairs))
    
    if n_pairs == 1:
        axes = axes.reshape(1, -1)
    
    for i, (_, row) in enumerate(df.iterrows()):
        angle1, angle2 = row['angle1'], row['angle2']
        
        # 获取数据
        values1 = [item['actual'] for item in data[angle1]]
        values2 = [item['actual'] for item in data[angle2]]
        
        # 计算分布
        bin_centers, hist1, hist2 = calculate_distribution_overlap(values1, values2)
        
        # 左图：分布对比
        ax1 = axes[i, 0]
        ax1.hist(values1, bins=30, alpha=0.6, label=f'{angle1}°', color='blue', density=True)
        ax1.hist(values2, bins=30, alpha=0.6, label=f'{angle2}°', color='red', density=True)
        
        # 标记临界值
        ax1.axvline(row['critical_intersection'], color='green', linestyle='--', 
                   label=f'交点法: {row["critical_intersection"]:.1f}')
        ax1.axvline(row['critical_error_min'], color='orange', linestyle='--', 
                   label=f'错误最小化: {row["critical_error_min"]:.1f}')
        ax1.axvline(row['critical_midpoint'], color='purple', linestyle='--', 
                   label=f'中点法: {row["critical_midpoint"]:.1f}')
        
        ax1.set_xlabel('C Value')
        ax1.set_ylabel('Density')
        ax1.set_title(f'Distribution: {angle1}° vs {angle2}°')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 右图：平滑分布曲线
        ax2 = axes[i, 1]
        ax2.plot(bin_centers, hist1, 'b-', label=f'{angle1}°', linewidth=2)
        ax2.plot(bin_centers, hist2, 'r-', label=f'{angle2}°', linewidth=2)
        
        # 标记交点
        ax2.axvline(row['critical_intersection'], color='green', linestyle='--', 
                   label=f'Critical: {row["critical_intersection"]:.1f}')
        
        ax2.set_xlabel('C Value')
        ax2.set_ylabel('Smoothed Density')
        ax2.set_title(f'Smoothed Distributions (Overlap: {row["overlap_percentage"]:.1f}%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"临界值分析图表已保存到: {output_path}")
    
    plt.show()

def save_critical_results(df, output_path):
    """
    保存临界值结果到CSV文件
    """
    df.to_csv(output_path, index=False, encoding='utf-8-sig')
    print(f"临界值结果已保存到: {output_path}")

def fit_270_with_multiple_angles(angle_data, available_angles):
    """
    使用多个角度的数据进行线性插值拟合270°的值
    优先使用260°、270°、280°数据进行更准确的拟合

    Parameters:
    angle_data (dict): 各角度对应的坐标值数据
    available_angles (list): 可用的角度列表

    Returns:
    float: 拟合得到的270°坐标值
    """
    # 收集260°、270°、280°的数据
    target_angles = [260.0, 270.0, 280.0]
    angle_means = {}

    for angle in target_angles:
        if angle in available_angles and len(angle_data[angle]) > 0:
            angle_means[angle] = np.mean(angle_data[angle])

    # 如果有270°数据且还有其他相邻角度数据，进行加权拟合
    if 270.0 in angle_means:
        if len(angle_means) == 1:
            # 只有270°数据
            return angle_means[270.0]
        elif len(angle_means) >= 2:
            # 有270°和其他角度数据，使用智能加权组合
            # 如果260°和280°数据都存在，可以检查线性关系
            if 260.0 in angle_means and 280.0 in angle_means:
                # 使用260°和280°数据预测270°值
                predicted_270 = (angle_means[260.0] + angle_means[280.0]) / 2
                actual_270 = angle_means[270.0]

                # 计算预测值与实际值的差异
                prediction_error = abs(predicted_270 - actual_270)

                # 如果预测误差较小，说明数据呈线性关系，可以更多地信任相邻角度
                if prediction_error < np.std(list(angle_means.values())) * 0.5:
                    # 线性关系较好，使用更平衡的权重
                    weight_270 = 0.5
                    weight_others = 0.5 / len([a for a in angle_means.keys() if a != 270.0])
                else:
                    # 非线性关系，更多地信任270°数据
                    weight_270 = 0.8
                    weight_others = 0.2 / len([a for a in angle_means.keys() if a != 270.0])
            else:
                # 只有部分相邻角度数据，使用中等权重
                weight_270 = 0.7
                weight_others = 0.3 / len([a for a in angle_means.keys() if a != 270.0])

            # 计算加权平均
            weighted_sum = weight_270 * angle_means[270.0]
            for angle, value in angle_means.items():
                if angle != 270.0:
                    weighted_sum += weight_others * value

            return weighted_sum

    # 如果没有270°数据，使用其他角度进行插值
    if len(angle_means) >= 2:
        angles = sorted(angle_means.keys())
        values = [angle_means[angle] for angle in angles]
        return np.interp(270.0, angles, values)
    elif len(angle_means) == 1:
        # 只有一个角度数据
        return list(angle_means.values())[0]

    # 如果没有任何相关角度数据，使用所有可用角度
    angles = []
    values = []
    for angle in sorted(available_angles):
        if len(angle_data[angle]) > 0:
            angles.append(angle)
            values.append(np.mean(angle_data[angle]))

    if len(angles) >= 2:
        return np.interp(270.0, angles, values)
    elif len(angles) == 1:
        return values[0]
    else:
        return 0

def fit_270_weighted_average(angle_data, available_angles):
    """
    使用多个角度的数据进行智能加权平均拟合270°的值
    优先考虑260°、270°、280°数据，使用更精确的权重策略

    Parameters:
    angle_data (dict): 各角度对应的坐标值数据
    available_angles (list): 可用的角度列表

    Returns:
    float: 拟合得到的270°坐标值
    """
    # 优先使用260°、270°、280°数据
    target_angles = [260.0, 270.0, 280.0]
    primary_data = {}

    for angle in target_angles:
        if angle in available_angles and len(angle_data[angle]) > 0:
            primary_data[angle] = np.mean(angle_data[angle])

    if len(primary_data) == 0:
        # 如果没有目标角度数据，使用所有可用数据
        primary_data = {angle: np.mean(angle_data[angle])
                       for angle in available_angles
                       if len(angle_data[angle]) > 0}

    if len(primary_data) == 0:
        return 0

    if len(primary_data) == 1:
        return list(primary_data.values())[0]

    # 使用改进的权重策略
    total_weighted_value = 0
    total_weight = 0

    for angle, mean_value in primary_data.items():
        if angle == 270.0:
            # 270°数据权重最高
            weight = 10.0
        elif angle in [260.0, 280.0]:
            # 相邻角度权重较高
            distance = abs(angle - 270.0)
            weight = 5.0 / distance  # 260°和280°权重为5.0/10=0.5
        else:
            # 其他角度权重较低
            distance = abs(angle - 270.0)
            weight = 1.0 / (distance + 1.0)

        total_weighted_value += weight * mean_value
        total_weight += weight

    return total_weighted_value / total_weight if total_weight > 0 else 0

def fit_270_polynomial_regression(angle_data, available_angles):
    """
    使用多项式回归拟合270°的值
    优先使用260°、270°、280°数据进行二次多项式拟合

    Parameters:
    angle_data (dict): 各角度对应的坐标值数据
    available_angles (list): 可用的角度列表

    Returns:
    float: 拟合得到的270°坐标值
    """
    # 收集260°、270°、280°的数据
    target_angles = [260.0, 270.0, 280.0]
    angle_means = {}

    for angle in target_angles:
        if angle in available_angles and len(angle_data[angle]) > 0:
            angle_means[angle] = np.mean(angle_data[angle])

    # 如果数据不足，回退到其他方法
    if len(angle_means) < 2:
        # 数据不足，使用简单平均或插值
        if 270.0 in angle_means:
            return angle_means[270.0]
        elif len(angle_means) == 1:
            return list(angle_means.values())[0]
        else:
            # 使用所有可用数据
            all_means = {angle: np.mean(angle_data[angle])
                        for angle in available_angles
                        if len(angle_data[angle]) > 0}
            if len(all_means) >= 2:
                angles = sorted(all_means.keys())
                values = [all_means[angle] for angle in angles]
                return np.interp(270.0, angles, values)
            elif len(all_means) == 1:
                return list(all_means.values())[0]
            else:
                return 0

    # 准备拟合数据
    angles = sorted(angle_means.keys())
    values = [angle_means[angle] for angle in angles]

    if len(angles) == 2:
        # 两个点，使用线性插值
        return np.interp(270.0, angles, values)
    elif len(angles) >= 3:
        # 三个或更多点，使用二次多项式拟合
        try:
            # 使用二次多项式拟合
            coeffs = np.polyfit(angles, values, min(2, len(angles) - 1))
            poly = np.poly1d(coeffs)
            fitted_270 = poly(270.0)

            # 验证拟合结果的合理性
            min_val = min(values)
            max_val = max(values)
            value_range = max_val - min_val

            # 如果拟合结果超出合理范围，使用线性插值作为备选
            if fitted_270 < min_val - value_range * 0.5 or fitted_270 > max_val + value_range * 0.5:
                # 拟合结果不合理，使用线性插值
                return np.interp(270.0, angles, values)

            return fitted_270
        except (np.linalg.LinAlgError, np.RankWarning):
            # 拟合失败，使用线性插值
            return np.interp(270.0, angles, values)

    return 0

def fit_270_robust_combination(angle_data, available_angles):
    """
    使用鲁棒组合方法拟合270°的值
    结合多种方法的结果，提高拟合的稳定性和准确性

    Parameters:
    angle_data (dict): 各角度对应的坐标值数据
    available_angles (list): 可用的角度列表

    Returns:
    float: 拟合得到的270°坐标值
    """
    # 收集不同方法的结果
    methods_results = []

    # 方法1: 线性拟合
    linear_result = fit_270_with_multiple_angles(angle_data, available_angles)
    methods_results.append(linear_result)

    # 方法2: 加权平均
    weighted_result = fit_270_weighted_average(angle_data, available_angles)
    methods_results.append(weighted_result)

    # 方法3: 多项式拟合
    poly_result = fit_270_polynomial_regression(angle_data, available_angles)
    methods_results.append(poly_result)

    # 如果有270°数据，也包含其直接平均值
    if 270.0 in available_angles and len(angle_data[270.0]) > 0:
        direct_result = np.mean(angle_data[270.0])
        methods_results.append(direct_result)

    # 过滤异常结果
    valid_results = [r for r in methods_results if not np.isnan(r) and not np.isinf(r)]

    if len(valid_results) == 0:
        return 0

    # 使用中位数作为鲁棒估计
    if len(valid_results) == 1:
        return valid_results[0]
    else:
        # 去除极端值后取平均
        q1 = np.percentile(valid_results, 25)
        q3 = np.percentile(valid_results, 75)
        iqr = q3 - q1

        if iqr > 0:
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            filtered_results = [r for r in valid_results if lower_bound <= r <= upper_bound]

            if len(filtered_results) > 0:
                return np.mean(filtered_results)

        # 如果过滤后没有结果，使用中位数
        return np.median(valid_results)

def fit_270_boundary_midpoint(angle_data, available_angles):
    """
    边界中点拟合：基于相邻角度边界值的中点方法

    使用相邻角度的边界值计算270°坐标：
    1. 260°/270°临界值 = (260°最大值 + 270°最小值) / 2
    2. 270°/280°临界值 = (270°最大值 + 280°最小值) / 2
    3. 270°最终值 = (260°/270°临界值 + 270°/280°临界值) / 2

    Args:
        angle_data: 角度数据字典
        available_angles: 可用角度列表

    Returns:
        float: 拟合的270°值
    """
    if not angle_data:
        return 150.0  # 默认值

    critical_values = []

    print(f"  边界中点拟合方法:")
    
    # 计算260°/270°临界值
    if 260.0 in available_angles and 270.0 in available_angles:
        if len(angle_data[260.0]) > 0 and len(angle_data[270.0]) > 0:
            max_260 = max(angle_data[260.0])
            min_270 = min(angle_data[270.0])
            critical_260_270 = (max_260 + min_270) / 2
            critical_values.append(critical_260_270)
            print(f"    260°/270°临界值: ({max_260:.3f} + {min_270:.3f}) / 2 = {critical_260_270:.3f}")

    # 计算270°/280°临界值
    if 270.0 in available_angles and 280.0 in available_angles:
        if len(angle_data[270.0]) > 0 and len(angle_data[280.0]) > 0:
            max_270 = max(angle_data[270.0])
            min_280 = min(angle_data[280.0])
            critical_270_280 = (max_270 + min_280) / 2
            critical_values.append(critical_270_280)
            print(f"    270°/280°临界值: ({max_270:.3f} + {min_280:.3f}) / 2 = {critical_270_280:.3f}")

    if len(critical_values) == 0:
        # 如果无法计算临界值，回退到其他方法
        if 270.0 in available_angles and len(angle_data[270.0]) > 0:
            result = np.mean(angle_data[270.0])
            print(f"    无法计算临界值，使用270°平均值: {result:.3f}")
            return result
        elif 260.0 in available_angles and 280.0 in available_angles:
            if len(angle_data[260.0]) > 0 and len(angle_data[280.0]) > 0:
                result = (np.mean(angle_data[260.0]) + np.mean(angle_data[280.0])) / 2
                print(f"    无法计算临界值，使用260°和280°平均值的中点: {result:.3f}")
                return result

        print(f"    无法计算临界值，使用默认值: 150.0")
        return 150.0

    elif len(critical_values) == 1:
        # 只有一个临界值
        result = critical_values[0]
        print(f"    只有一个临界值，直接使用: {result:.3f}")
        return result

    else:
        # 有两个临界值，计算中点
        result = np.mean(critical_values)
        print(f"    270°最终值: ({critical_values[0]:.3f} + {critical_values[1]:.3f}) / 2 = {result:.3f}")
        return result

def find_and_fit_270_degree_values(filename):
    """
    找到所有270°对应的坐标值，并拟合出270°对应的最佳坐标值

    Parameters:
    filename (str): 输入文件路径

    Returns:
    dict: 包含270°数据分析结果的字典
    """
    print(f"正在分析270°数据: {filename}")

    # 解析文件
    data = parse_doa_file(filename)

    # 检查并提取相关角度数据
    available_angles = []
    angle_data = {}

    for angle in [260.0, 270.0, 280.0]:
        if angle in data and len(data[angle]) > 0:
            available_angles.append(angle)
            raw_values = [item['actual'] for item in data[angle]]

            # 对数据取整后，随机选取不同值
            rounded_values = list(set(round(v) for v in raw_values))

            if angle == 270.0:
                # 270°数据选取更多样本
                max_samples = min(3000, len(rounded_values))
            else:
                # 260°和280°数据选取较少样本
                max_samples = min(1000, len(rounded_values))

            if len(rounded_values) > max_samples:
                selected_values = list(np.random.choice(rounded_values, size=max_samples, replace=False))
            else:
                selected_values = rounded_values

            angle_data[angle] = selected_values
            print(f"角度 {angle}°: 原始数据 {len(raw_values)} 个，取整去重后 {len(rounded_values)} 个，选取 {len(selected_values)} 个")

    if 270.0 not in available_angles:
        # 如果没有270°数据，但有260°和280°数据，仍然可以进行拟合
        if 260.0 in available_angles and 280.0 in available_angles:
            print("注意: 没有270°数据，将使用260°和280°数据进行插值拟合")
        else:
            print("警告: 文件中未找到270°的数据，且缺少足够的相邻角度数据")
            return None

    # 如果只有270°数据，也可以进行分析，只是无法使用多角度拟合方法
    if len(available_angles) == 1 and 270.0 in available_angles:
        print("注意: 只有270°数据，将使用单角度分析方法")

    # 处理270°数据
    if 270.0 in available_angles:
        values_270 = angle_data[270.0]

        if len(values_270) == 0:
            print("警告: 270°数据为空")
            return None

        print(f"找到 {len(values_270)} 个270°的坐标值")
        print(f"数据范围: [{min(values_270):.3f}, {max(values_270):.3f}]")

        # 基本统计信息
        mean_value = np.mean(values_270)
        median_value = np.median(values_270)
        std_value = np.std(values_270)
    else:
        # 没有270°数据，创建虚拟的270°数据用于RMSE计算
        # 使用多角度拟合的结果作为参考
        estimated_270 = fit_270_with_multiple_angles(angle_data, available_angles)
        values_270 = [estimated_270]  # 创建单点数据用于后续计算

        print(f"没有270°数据，使用多角度拟合估计值: {estimated_270:.3f}")

        # 基本统计信息
        mean_value = estimated_270
        median_value = estimated_270
        std_value = 0.0

    print(f"可用角度: {sorted(available_angles)}")

    # 使用不同方法拟合最佳坐标值

    # 方法1: 仅使用270°数据的算术平均值
    best_value_mean_270_only = mean_value

    # 方法2: 仅使用270°数据的中位数（对异常值更鲁棒）
    best_value_median_270_only = median_value

    # 方法3: 多角度线性插值拟合
    best_value_linear_fit = fit_270_with_multiple_angles(angle_data, available_angles)

    # 方法4: 多角度加权平均拟合
    best_value_weighted_fit = fit_270_weighted_average(angle_data, available_angles)

    # 方法5: 多项式回归拟合
    best_value_polynomial_fit = fit_270_polynomial_regression(angle_data, available_angles)

    # 方法6: 鲁棒组合拟合
    best_value_robust_fit = fit_270_robust_combination(angle_data, available_angles)

    # 方法7: 边界中点拟合
    best_value_boundary_midpoint = fit_270_boundary_midpoint(angle_data, available_angles)

    # 方法8: 去除异常值后的平均值（使用IQR方法）
    if len(values_270) > 1:
        q1 = np.percentile(values_270, 25)
        q3 = np.percentile(values_270, 75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        # 过滤异常值
        filtered_values = [v for v in values_270 if lower_bound <= v <= upper_bound]
        outliers_count = len(values_270) - len(filtered_values)

        if len(filtered_values) > 0:
            best_value_filtered = np.mean(filtered_values)
        else:
            best_value_filtered = mean_value
            print("警告: 所有数据都被识别为异常值，使用原始平均值")
    else:
        # 只有一个数据点，无法进行异常值检测
        filtered_values = values_270
        outliers_count = 0
        best_value_filtered = mean_value

    # 方法9: 基于270°数据的KDE密度估计
    try:
        from scipy.stats import gaussian_kde

        if len(values_270) > 1:
            kde = gaussian_kde(values_270)

            # 在数据范围内创建评估点
            eval_points = np.linspace(min(values_270), max(values_270), 1000)
            density = kde(eval_points)

            # 找到密度最高的点
            max_density_idx = np.argmax(density)
            best_value_kde = eval_points[max_density_idx]
        else:
            # 只有一个数据点，直接使用该值
            best_value_kde = values_270[0]
    except (ImportError, ValueError):
        print("警告: KDE方法不可用，使用平均值")
        best_value_kde = mean_value

    # 计算拟合质量指标
    # 计算各种方法的标准差（RMSE）
    std_from_mean_270_only = np.sqrt(np.mean([(v - best_value_mean_270_only)**2 for v in values_270]))
    std_from_median_270_only = np.sqrt(np.mean([(v - best_value_median_270_only)**2 for v in values_270]))
    std_from_linear_fit = np.sqrt(np.mean([(v - best_value_linear_fit)**2 for v in values_270]))
    std_from_weighted_fit = np.sqrt(np.mean([(v - best_value_weighted_fit)**2 for v in values_270]))
    std_from_polynomial_fit = np.sqrt(np.mean([(v - best_value_polynomial_fit)**2 for v in values_270]))
    std_from_robust_fit = np.sqrt(np.mean([(v - best_value_robust_fit)**2 for v in values_270]))
    std_from_boundary_midpoint = np.sqrt(np.mean([(v - best_value_boundary_midpoint)**2 for v in values_270]))
    std_from_filtered = np.sqrt(np.mean([(v - best_value_filtered)**2 for v in values_270]))
    std_from_kde = np.sqrt(np.mean([(v - best_value_kde)**2 for v in values_270]))

    # 构建结果字典
    result = {
        'total_samples': len(values_270),
        'available_angles': available_angles,
        'data_range_min': min(values_270),
        'data_range_max': max(values_270),
        'data_range_span': max(values_270) - min(values_270),
        'original_mean': mean_value,
        'original_median': median_value,
        'original_std': std_value,
        'outliers_count': outliers_count,
        'outliers_percentage': (outliers_count / len(values_270)) * 100,
        'filtered_samples': len(filtered_values),

        # 不同方法的最佳值
        'best_value_mean_270_only': best_value_mean_270_only,
        'best_value_median_270_only': best_value_median_270_only,
        'best_value_linear_fit': best_value_linear_fit,
        'best_value_weighted_fit': best_value_weighted_fit,
        'best_value_polynomial_fit': best_value_polynomial_fit,
        'best_value_robust_fit': best_value_robust_fit,
        'best_value_boundary_midpoint': best_value_boundary_midpoint,
        'best_value_filtered': best_value_filtered,
        'best_value_kde': best_value_kde,

        # 拟合质量指标（RMSE）
        'rmse_mean_270_only': std_from_mean_270_only,
        'rmse_median_270_only': std_from_median_270_only,
        'rmse_linear_fit': std_from_linear_fit,
        'rmse_weighted_fit': std_from_weighted_fit,
        'rmse_polynomial_fit': std_from_polynomial_fit,
        'rmse_robust_fit': std_from_robust_fit,
        'rmse_boundary_midpoint': std_from_boundary_midpoint,
        'rmse_filtered': std_from_filtered,
        'rmse_kde': std_from_kde,

        # 原始数据
        'raw_values': values_270,
        'filtered_values': filtered_values,
        'angle_data': angle_data
    }

    # 确定推荐的最佳值（选择RMSE最小的方法）
    rmse_methods = {
        'mean_270_only': std_from_mean_270_only,
        'median_270_only': std_from_median_270_only,
        'linear_fit': std_from_linear_fit,
        'weighted_fit': std_from_weighted_fit,
        'polynomial_fit': std_from_polynomial_fit,
        'robust_fit': std_from_robust_fit,
        'boundary_midpoint': std_from_boundary_midpoint,
        'filtered': std_from_filtered,
        'kde': std_from_kde
    }

    best_method = min(rmse_methods, key=rmse_methods.get)
    result['recommended_method'] = best_method
    result['recommended_value'] = result[f'best_value_{best_method}']
    result['recommended_rmse'] = rmse_methods[best_method]

    # 打印结果摘要
    print(f"\n270°坐标值分析结果:")
    print(f"样本数量: {len(values_270)}")
    print(f"可用角度: {sorted(available_angles)}")
    print(f"数据范围: [{min(values_270):.3f}, {max(values_270):.3f}] (跨度: {max(values_270) - min(values_270):.3f})")
    print(f"异常值: {outliers_count} 个 ({(outliers_count / len(values_270)) * 100:.1f}%)")
    print(f"\n不同拟合方法的结果:")
    print(f"  270°算术平均值: {best_value_mean_270_only:.3f} (RMSE: {std_from_mean_270_only:.3f})")
    print(f"  270°中位数:     {best_value_median_270_only:.3f} (RMSE: {std_from_median_270_only:.3f})")
    print(f"  多角度线性拟合: {best_value_linear_fit:.3f} (RMSE: {std_from_linear_fit:.3f})")
    print(f"  多角度加权拟合: {best_value_weighted_fit:.3f} (RMSE: {std_from_weighted_fit:.3f})")
    print(f"  多项式回归拟合: {best_value_polynomial_fit:.3f} (RMSE: {std_from_polynomial_fit:.3f})")
    print(f"  鲁棒组合拟合:   {best_value_robust_fit:.3f} (RMSE: {std_from_robust_fit:.3f})")
    print(f"  边界中点拟合:   {best_value_boundary_midpoint:.3f} (RMSE: {std_from_boundary_midpoint:.3f})")
    print(f"  过滤平均值:     {best_value_filtered:.3f} (RMSE: {std_from_filtered:.3f})")
    print(f"  KDE最大密度:   {best_value_kde:.3f} (RMSE: {std_from_kde:.3f})")
    print(f"\n推荐方法: {best_method}")
    print(f"推荐的270°最佳坐标值: {result['recommended_value']:.3f}")

    return result

def plot_270_degree_analysis(result, output_path=None):
    """
    绘制270°数据分析的可视化图表

    Parameters:
    result (dict): find_and_fit_270_degree_values函数的返回结果
    output_path (str): 可选的输出文件路径
    """
    if result is None:
        print("无法绘制图表：结果为空")
        return

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    values_270 = result['raw_values']
    filtered_values = result['filtered_values']

    # 子图1: 原始数据分布直方图
    ax1 = axes[0, 0]
    ax1.hist(values_270, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(result['best_value_mean_270_only'], color='red', linestyle='--',
               label=f'270°平均值: {result["best_value_mean_270_only"]:.3f}')
    ax1.axvline(result['best_value_median_270_only'], color='green', linestyle='--',
               label=f'270°中位数: {result["best_value_median_270_only"]:.3f}')
    ax1.axvline(result['best_value_linear_fit'], color='orange', linestyle=':',
               label=f'线性拟合: {result["best_value_linear_fit"]:.3f}')
    ax1.axvline(result['recommended_value'], color='purple', linestyle='-', linewidth=2,
               label=f'推荐值({result["recommended_method"]}): {result["recommended_value"]:.3f}')
    ax1.set_xlabel('坐标值')
    ax1.set_ylabel('频次')
    ax1.set_title('270°坐标值分布 (原始数据)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 子图2: 过滤异常值后的数据分布
    ax2 = axes[0, 1]
    if len(filtered_values) > 0:
        ax2.hist(filtered_values, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        ax2.axvline(result['best_value_filtered'], color='orange', linestyle='--',
                   label=f'过滤平均值: {result["best_value_filtered"]:.3f}')
    ax2.axvline(result['recommended_value'], color='purple', linestyle='-', linewidth=2,
               label=f'推荐值: {result["recommended_value"]:.3f}')
    ax2.set_xlabel('坐标值')
    ax2.set_ylabel('频次')
    ax2.set_title(f'270°坐标值分布 (去除{result["outliers_count"]}个异常值)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 子图3: 箱线图显示数据分布和异常值
    ax3 = axes[1, 0]
    box_plot = ax3.boxplot([values_270], labels=['270°'], patch_artist=True)
    box_plot['boxes'][0].set_facecolor('lightblue')
    ax3.axhline(result['recommended_value'], color='purple', linestyle='-', linewidth=2,
               label=f'推荐值: {result["recommended_value"]:.3f}')
    ax3.set_ylabel('坐标值')
    ax3.set_title('270°坐标值箱线图')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 子图4: 不同方法的RMSE比较
    ax4 = axes[1, 1]
    methods = ['mean_270_only', 'median_270_only', 'linear_fit', 'weighted_fit', 'polynomial_fit', 'robust_fit', 'boundary_midpoint', 'filtered', 'kde']
    method_names = ['270°平均', '270°中位', '线性拟合', '加权拟合', '多项式拟合', '鲁棒组合', '边界中点', '过滤平均', 'KDE密度']
    rmse_values = [result[f'rmse_{method}'] for method in methods]
    colors = ['red', 'green', 'orange', 'cyan', 'purple', 'brown', 'pink', 'yellow', 'blue']

    bars = ax4.bar(method_names, rmse_values, color=colors, alpha=0.7)

    # 高亮推荐方法
    try:
        recommended_idx = methods.index(result['recommended_method'])
        bars[recommended_idx].set_color('purple')
        bars[recommended_idx].set_alpha(1.0)
    except ValueError:
        pass  # 如果推荐方法不在列表中，跳过高亮

    ax4.set_ylabel('RMSE')
    ax4.set_title('不同拟合方法的RMSE比较')
    ax4.grid(True, alpha=0.3)
    plt.setp(ax4.get_xticklabels(), rotation=45, ha='right')

    # 在柱状图上添加数值标签
    for bar, rmse in zip(bars, rmse_values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{rmse:.3f}', ha='center', va='bottom')

    plt.tight_layout()

    # 添加总标题
    fig.suptitle(f'270°坐标值分析 (样本数: {result["total_samples"]}, 推荐值: {result["recommended_value"]:.3f})',
                fontsize=14, y=0.98)

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"270°分析图表已保存到: {output_path}")

    plt.show()

def save_270_degree_results(result, output_path):
    """
    保存270°分析结果到CSV文件

    Parameters:
    result (dict): find_and_fit_270_degree_values函数的返回结果
    output_path (str): 输出文件路径
    """
    if result is None:
        print("无法保存结果：结果为空")
        return

    # 创建结果摘要DataFrame
    summary_data = {
        '指标': [
            '样本总数', '可用角度', '数据范围最小值', '数据范围最大值', '数据跨度',
            '原始平均值', '原始中位数', '原始标准差',
            '异常值数量', '异常值百分比', '过滤后样本数',
            '270°算术平均值', '270°中位数', '多角度线性拟合', '多角度加权拟合', '多项式回归拟合', '鲁棒组合拟合', '边界中点拟合', '过滤平均值', 'KDE最大密度值',
            'RMSE(270°平均)', 'RMSE(270°中位)', 'RMSE(线性拟合)', 'RMSE(加权拟合)', 'RMSE(多项式)', 'RMSE(鲁棒组合)', 'RMSE(边界中点)', 'RMSE(过滤)', 'RMSE(KDE)',
            '推荐方法', '推荐值', '推荐RMSE'
        ],
        '数值': [
            result['total_samples'], str(result['available_angles']), result['data_range_min'], result['data_range_max'], result['data_range_span'],
            result['original_mean'], result['original_median'], result['original_std'],
            result['outliers_count'], result['outliers_percentage'], result['filtered_samples'],
            result['best_value_mean_270_only'], result['best_value_median_270_only'], result['best_value_linear_fit'],
            result['best_value_weighted_fit'], result['best_value_polynomial_fit'], result['best_value_robust_fit'], result['best_value_boundary_midpoint'], result['best_value_filtered'], result['best_value_kde'],
            result['rmse_mean_270_only'], result['rmse_median_270_only'], result['rmse_linear_fit'],
            result['rmse_weighted_fit'], result['rmse_polynomial_fit'], result['rmse_robust_fit'], result['rmse_boundary_midpoint'], result['rmse_filtered'], result['rmse_kde'],
            result['recommended_method'], result['recommended_value'], result['recommended_rmse']
        ]
    }

    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    print(f"270°分析结果已保存到: {output_path}")

    # 同时保存原始数据
    raw_data_path = output_path.replace('.csv', '_raw_data.csv')
    raw_df = pd.DataFrame({
        '270度坐标值': result['raw_values']
    })
    raw_df.to_csv(raw_data_path, index=False, encoding='utf-8-sig')
    print(f"270°原始数据已保存到: {raw_data_path}")

if __name__ == "__main__":
    # 配置参数
    input_file = r"D:\czcv_debug_doa.txt"

    # 计算临界值
    results_df, data = calculate_critical_values(input_file)

    if not results_df.empty:
        # 显示结果摘要
        print("\n" + "="*80)
        print("临界值计算结果摘要")
        print("="*80)

        print("\n角度对    交点法    错误最小化    中点法    错误率%    重叠度%")
        print("-" * 70)
        for _, row in results_df.iterrows():
            print(f"{row['angle1']:6.1f}°-{row['angle2']:6.1f}°  {row['critical_intersection']:8.1f}  "
                  f"{row['critical_error_min']:10.1f}  {row['critical_midpoint']:8.1f}  "
                  f"{row['error_rate_percent']:7.1f}  {row['overlap_percentage']:7.1f}")

        # 保存结果
        save_critical_results(results_df, "critical_values_results.csv")

        # 绘制分析图表
        plot_critical_analysis(results_df, data, "critical_values_analysis.png")

        print(f"\n处理完成！共分析了 {len(results_df)} 对相邻角度。")
    else:
        print("没有找到相邻角度对进行分析。")

    # 分析270°数据
    print("\n" + "="*80)
    print("270°坐标值拟合分析")
    print("="*80)

    result_270 = find_and_fit_270_degree_values(input_file)

    if result_270 is not None:
        # 保存270°分析结果
        save_270_degree_results(result_270, "270_degree_analysis_results.csv")

        # 绘制270°分析图表
        plot_270_degree_analysis(result_270, "270_degree_analysis.png")

        print(f"\n270°数据分析完成！")
        print(f"推荐的270°最佳坐标值: {result_270['recommended_value']:.3f}")
        print(f"使用方法: {result_270['recommended_method']}")
        print(f"拟合质量(RMSE): {result_270['recommended_rmse']:.3f}")
    else:
        print("270°数据分析失败。")
