lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o: \
  D:\Program\Project\project\czcv_camera_new\lib\src\detector\detect_white_board.cpp \
  D:\Program\Project\project\czcv_camera_new\lib\src\detector\detect_white_board.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\vector \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__config \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\features.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\sys\cdefs.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\android\versioning.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\android\api-level.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\get_device_api_level_inlines.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\android\ndk-version.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\pthread.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\iosfwd \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\wchar.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\local\include\wchar.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\wchar.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\stdio.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\stdio.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\sys\types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\stddef.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include\stddef.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include\__stddef_max_align_t.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__nullptr \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\stdint.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include\stdint.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\stdint.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\wchar_limits.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android\asm\types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\int-ll64.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android\asm\bitsperlong.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\bitsperlong.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\posix_types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\stddef.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\compiler_types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\compiler.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android\asm\posix_types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\posix_types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\pthread_types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include\stdarg.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\seek_constants.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\fortify\stdio.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\time.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\sys\time.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\time.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\time_types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\sys\select.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\signal.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android\asm\sigcontext.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android\asm\sve_context.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\signal_types.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\limits.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include\limits.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\limits.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\float.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include\float.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\limits.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\posix_limits.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\signal.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android\asm\signal.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\signal.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\signal-defs.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android\asm\siginfo.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\siginfo.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\timespec.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\sys\ucontext.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\sys\user.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\android\legacy_signal_inlines.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\xlocale.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\mbstate_t.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\wctype.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__bit_reference \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\bit \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\limits \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\type_traits \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cstddef \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\version \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__undef_macros \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__debug \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\algorithm \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\initializer_list \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cstring \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\string.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\string.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\strcasecmp.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\strings.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\fortify\strings.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\fortify\string.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\utility \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__tuple \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cstdint \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\memory \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\typeinfo \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\exception \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cstdlib \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\stdlib.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\local\include\stdlib.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\stdlib.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\alloca.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\wait.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\wait.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\malloc.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\fortify\stdlib.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\android\legacy_stdlib_inlines.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\math.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\local\include\math.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\math.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\new \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\iterator \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__functional_base \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\tuple \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\stdexcept \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\atomic \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__threading_support \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\chrono \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\ctime \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\ratio \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\climits \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\errno.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\errno.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\errno.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\aarch64-linux-android\asm\errno.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\errno.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\asm-generic\errno-base.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\android\legacy_errno_inlines.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\sched.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\linux\sched.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\semaphore.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\functional \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__split_buffer \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\string \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\string_view \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__string \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cstdio \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cwchar \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cwctype \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cctype \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\ctype.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\ctype.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\bits\ctype_inlines.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\wctype.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\local\include\wctype.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\wctype.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\fstream \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\ostream \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\ios \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__locale \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\mutex \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__mutex_base \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\system_error \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__errc \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cerrno \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\locale.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\local\include\locale.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\locale.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\support\android\locale_bionic.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\streambuf \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\locale \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cstdarg \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__bsd_locale_fallbacks.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\bitset \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\istream \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\filesystem \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\stack \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\deque \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\iomanip \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\opencv.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\opencv_modules.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\cvdef.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\hal\interface.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\cv_cpu_dispatch.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include\arm_neon.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\array \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\version.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\base.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\cvstd.hpp \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cmath \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\cvstd_wrapper.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\neon_utils.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\vsx_utils.hpp \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\assert.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\check.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\traits.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\matx.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\saturate.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\fast_math.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\types.hpp \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cfloat \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\mat.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\bufferpool.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\mat.inl.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\persistence.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\operations.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\cvstd.inl.hpp \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\complex \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\sstream \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\utility.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\optim.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\ovx.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\calib3d.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\features2d.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\miniflann.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\defines.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\config.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\core\affine.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\flann_base.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\general.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\matrix.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\params.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\any.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\iostream \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\map \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__tree \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__node_handle \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\optional \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\saving.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\nn_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\result_set.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\set \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\all_indices.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\kdtree_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\dynamic_bitset.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\dist.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\heap.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\allocator.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\random.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\kdtree_single_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\kmeans_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\logger.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\composite_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\linear_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\hierarchical_clustering_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\lsh_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\lsh_table.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\unordered_map \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\__hash_table \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\autotuned_index.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\ground_truth.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\index_testing.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\timer.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\flann\sampling.h \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\imgcodecs.hpp \
  D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\imgproc.hpp \
  D:\Program\Project\project\czcv_camera_new\lib\include\base\common.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\base\macro.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\android\log.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\cassert \
  D:\Program\Project\project\czcv_camera_new\lib\include\base\status.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\base\abstract_model.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\base\profile_data.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\base\dynamic_param.h \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\typeindex \
  D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\include\c++\v1\thread \
  D:\Program\Project\project\czcv_camera_new\lib\include\detector\base_detector.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\base\bbox.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\tracker\base_tracker.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\tracker\tracker_id.h \
  D:\Program\Project\project\czcv_camera_new\runtime\Android\librknn_api\include\rknn_api.h \
  D:\Program\Project\project\czcv_camera_new\lib\src\detector\yolox_person_det.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\detector\detector_factory.h \
  D:\Program\Project\project\czcv_camera_new\lib\include\detector\detector_id.h
