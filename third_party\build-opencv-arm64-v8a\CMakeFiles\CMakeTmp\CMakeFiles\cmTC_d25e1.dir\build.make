# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = "/D/Program Files/CMake/bin/cmake.exe"

# The command to remove a file.
RM = "/D/Program Files/CMake/bin/cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp

# Include any dependencies generated for this target.
include CMakeFiles/cmTC_d25e1.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/cmTC_d25e1.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/cmTC_d25e1.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/cmTC_d25e1.dir/flags.make

CMakeFiles/cmTC_d25e1.dir/src.cxx.o: CMakeFiles/cmTC_d25e1.dir/flags.make
CMakeFiles/cmTC_d25e1.dir/src.cxx.o: src.cxx
CMakeFiles/cmTC_d25e1.dir/src.cxx.o: CMakeFiles/cmTC_d25e1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/cmTC_d25e1.dir/src.cxx.o"
	/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cmTC_d25e1.dir/src.cxx.o -MF CMakeFiles/cmTC_d25e1.dir/src.cxx.o.d -o CMakeFiles/cmTC_d25e1.dir/src.cxx.o -c /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/src.cxx

CMakeFiles/cmTC_d25e1.dir/src.cxx.i: cmake_force
	@echo "Preprocessing CXX source to CMakeFiles/cmTC_d25e1.dir/src.cxx.i"
	/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/src.cxx > CMakeFiles/cmTC_d25e1.dir/src.cxx.i

CMakeFiles/cmTC_d25e1.dir/src.cxx.s: cmake_force
	@echo "Compiling CXX source to assembly CMakeFiles/cmTC_d25e1.dir/src.cxx.s"
	/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/src.cxx -o CMakeFiles/cmTC_d25e1.dir/src.cxx.s

# Object files for target cmTC_d25e1
cmTC_d25e1_OBJECTS = \
"CMakeFiles/cmTC_d25e1.dir/src.cxx.o"

# External object files for target cmTC_d25e1
cmTC_d25e1_EXTERNAL_OBJECTS =

cmTC_d25e1: CMakeFiles/cmTC_d25e1.dir/src.cxx.o
cmTC_d25e1: CMakeFiles/cmTC_d25e1.dir/build.make
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable cmTC_d25e1"
	/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android21 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security    -DNDEBUG  -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -Wl,--gc-sections   $(cmTC_d25e1_OBJECTS) $(cmTC_d25e1_EXTERNAL_OBJECTS) -o cmTC_d25e1  -latomic -lm 

# Rule to build all files generated by this target.
CMakeFiles/cmTC_d25e1.dir/build: cmTC_d25e1
.PHONY : CMakeFiles/cmTC_d25e1.dir/build

CMakeFiles/cmTC_d25e1.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/cmTC_d25e1.dir/cmake_clean.cmake
.PHONY : CMakeFiles/cmTC_d25e1.dir/clean

CMakeFiles/cmTC_d25e1.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MSYS Makefiles" /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/CMakeFiles/cmTC_d25e1.dir/DependInfo.cmake
.PHONY : CMakeFiles/cmTC_d25e1.dir/depend

