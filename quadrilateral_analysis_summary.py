import numpy as np

# 定义两个四边形的顶点
quad1 = np.array([
    [-663.40788856, -416.93399977, 1241.72878607],
    [817.47550169, -512.12748461, 1448.39459962],
    [911.65503184, 392.34151613, 1322.65532539],
    [-727.93803401, 316.05002203, 1121.55349277]
])

quad2 = np.array([
    [-371.96439105, -306.48398539, 1262.69683101],
    [440.2381145, -358.69362418, 1376.04437253],
    [494.75465923, 289.95606478, 1284.17905177],
    [-317.44784632, 342.16570357, 1170.83151024]
])

def calculate_edge_lengths(quad):
    """计算四边形各边的长度"""
    edges = []
    for i in range(4):
        p1 = quad[i]
        p2 = quad[(i + 1) % 4]
        edge_length = np.linalg.norm(p2 - p1)
        edges.append(edge_length)
    return edges

def calculate_diagonal_lengths(quad):
    """计算四边形对角线的长度"""
    diag1 = np.linalg.norm(quad[2] - quad[0])  # 点0到点2
    diag2 = np.linalg.norm(quad[3] - quad[1])  # 点1到点3
    return diag1, diag2

def calculate_angles(quad):
    """计算四边形各个内角"""
    angles = []
    for i in range(4):
        vertex = quad[i]
        prev_point = quad[(i - 1) % 4]
        next_point = quad[(i + 1) % 4]
        
        vec1 = prev_point - vertex
        vec2 = next_point - vertex
        
        cos_angle = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle = np.arccos(cos_angle)
        angles.append(np.degrees(angle))
    
    return angles

def analyze_quadrilateral(quad, name):
    """分析四边形的几何特性"""
    print(f"\n=== {name} 分析结果 ===")
    
    # 计算边长
    edges = calculate_edge_lengths(quad)
    print(f"四边长度:")
    for i, edge in enumerate(edges):
        print(f"  边{i+1}: {edge:.6f}")
    
    # 计算对角线长度
    diag1, diag2 = calculate_diagonal_lengths(quad)
    print(f"对角线长度:")
    print(f"  对角线1 (点1→点3): {diag1:.6f}")
    print(f"  对角线2 (点2→点4): {diag2:.6f}")
    
    # 计算内角
    angles = calculate_angles(quad)
    print(f"内角 (度):")
    for i, angle in enumerate(angles):
        print(f"  角{i+1}: {angle:.2f}°")
    
    # 判断是否为矩形
    tolerance = 1e-6
    opposite_sides_equal = (abs(edges[0] - edges[2]) < tolerance and 
                           abs(edges[1] - edges[3]) < tolerance)
    diagonals_equal = abs(diag1 - diag2) < tolerance
    all_angles_90 = all(abs(angle - 90.0) < 0.1 for angle in angles)
    
    print(f"\n几何特性判断:")
    print(f"  对边相等: {opposite_sides_equal}")
    print(f"  对角线相等: {diagonals_equal}")
    print(f"  所有角都是90度: {all_angles_90}")
    
    is_rectangle = opposite_sides_equal and diagonals_equal and all_angles_90
    print(f"  是否为矩形: {'是' if is_rectangle else '否'}")
    
    return is_rectangle

if __name__ == "__main__":
    print("3D空间四边形分析")
    print("=" * 50)
    
    print("\n四边形1的坐标:")
    for i, point in enumerate(quad1):
        print(f"  点{i+1}: [{point[0]:.8f}, {point[1]:.8f}, {point[2]:.8f}]")
    
    print("\n四边形2的坐标:")
    for i, point in enumerate(quad2):
        print(f"  点{i+1}: [{point[0]:.8f}, {point[1]:.8f}, {point[2]:.8f}]")
    
    # 分析两个四边形
    is_rect1 = analyze_quadrilateral(quad1, "四边形1")
    is_rect2 = analyze_quadrilateral(quad2, "四边形2")
    
    print(f"\n" + "=" * 50)
    print("总结:")
    print(f"四边形1是矩形: {'是' if is_rect1 else '否'}")
    print(f"四边形2是矩形: {'是' if is_rect2 else '否'}")
    print(f"\n3D可视化图像已保存为: quadrilaterals_3d.png")
