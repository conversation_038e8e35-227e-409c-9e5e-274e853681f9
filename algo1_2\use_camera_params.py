#!/usr/bin/env python3
"""
使用相机内参和外参的示例脚本
展示如何加载和使用标定结果
"""

import cv2
import numpy as np
import yaml

def load_camera_params(params_file="./camera_params.yml"):
    """
    加载相机标定参数
    """
    with open(params_file, 'r') as f:
        params = yaml.safe_load(f)
    
    camera_matrix = np.array(params['camera_matrix'])
    dist_coeffs = np.array(params['dist_coeffs'])
    rotation_vectors = [np.array(rvec) for rvec in params['rotation_vectors']]
    translation_vectors = [np.array(tvec) for tvec in params['translation_vectors']]
    
    return camera_matrix, dist_coeffs, rotation_vectors, translation_vectors

def undistort_image(image_path, camera_matrix, dist_coeffs, output_path=None):
    """
    使用相机内参去除图像畸变
    """
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return None
    
    h, w = img.shape[:2]
    
    # 获取最优的新相机矩阵
    new_camera_matrix, roi = cv2.getOptimalNewCameraMatrix(
        camera_matrix, dist_coeffs, (w, h), 1, (w, h)
    )
    
    # 去畸变
    undistorted_img = cv2.undistort(img, camera_matrix, dist_coeffs, None, new_camera_matrix)
    
    # 裁剪图像（可选）
    x, y, w, h = roi
    undistorted_img = undistorted_img[y:y+h, x:x+w]
    
    if output_path:
        cv2.imwrite(output_path, undistorted_img)
        print(f"去畸变图像已保存到: {output_path}")
    
    return undistorted_img

def project_3d_to_2d(points_3d, rvec, tvec, camera_matrix, dist_coeffs):
    """
    将3D点投影到2D图像平面
    """
    points_2d, _ = cv2.projectPoints(points_3d, rvec, tvec, camera_matrix, dist_coeffs)
    return points_2d.reshape(-1, 2)

def calculate_camera_pose_from_extrinsics(rvec, tvec):
    """
    从外参计算相机在世界坐标系中的位置和姿态
    """
    # 将旋转向量转换为旋转矩阵
    R, _ = cv2.Rodrigues(rvec)
    
    # 计算相机在世界坐标系中的位置
    camera_position = -R.T @ tvec
    
    # 计算相机的朝向（光轴方向）
    camera_direction = R.T @ np.array([0, 0, 1])  # 相机坐标系的Z轴
    
    return camera_position, camera_direction, R

def main():
    """
    主函数 - 演示如何使用相机参数
    """
    print("加载相机标定参数...")
    
    try:
        camera_matrix, dist_coeffs, rvecs, tvecs = load_camera_params()
        
        print("相机内参矩阵:")
        print(camera_matrix)
        print("\n畸变系数:")
        print(dist_coeffs)
        
        print(f"\n共有 {len(rvecs)} 组外参数据")
        
        # 演示外参使用
        for i, (rvec, tvec) in enumerate(zip(rvecs, tvecs)):
            print(f"\n--- 图像 {i+1} 的外参分析 ---")
            
            camera_pos, camera_dir, R = calculate_camera_pose_from_extrinsics(rvec, tvec)
            
            print(f"相机位置 (世界坐标系): [{camera_pos[0]:.2f}, {camera_pos[1]:.2f}, {camera_pos[2]:.2f}] mm")
            print(f"相机朝向向量: [{camera_dir[0]:.3f}, {camera_dir[1]:.3f}, {camera_dir[2]:.3f}]")
            
            # 计算旋转角度
            rotation_angle = np.linalg.norm(rvec) * 180 / np.pi
            print(f"总旋转角度: {rotation_angle:.2f}°")
        
        # 演示3D到2D投影
        print("\n--- 3D点投影示例 ---")
        # 定义一些3D点（棋盘格角点）
        test_points_3d = np.array([
            [0, 0, 0],      # 原点
            [160, 0, 0],    # X轴上的点
            [0, 160, 0],    # Y轴上的点
            [160, 160, 0]   # 对角点
        ], dtype=np.float32)
        
        # 使用第一组外参进行投影
        rvec, tvec = rvecs[0], tvecs[0]
        projected_points = project_3d_to_2d(test_points_3d, rvec, tvec, camera_matrix, dist_coeffs)
        
        print("3D点投影到图像平面的结果:")
        for i, (point_3d, point_2d) in enumerate(zip(test_points_3d, projected_points)):
            print(f"  3D点 {point_3d} -> 2D点 ({point_2d[0]:.1f}, {point_2d[1]:.1f})")
        
        print("\n参数加载和使用演示完成！")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
