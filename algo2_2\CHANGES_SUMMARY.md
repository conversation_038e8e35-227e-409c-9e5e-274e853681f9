# recover_3d_points函数SVD方法修改总结

## 修改概述

根据要求，我们修改了 `recover_3d_points` 函数，使用SVD方法求解以下方程组：
- `a * quad_2d_proj = G * X_hom` (投影仪投影方程)
- `b * quad_2d = K * X` (相机投影方程)

其中 `a` 和 `b` 是标量，`X` 是未知的3D点。

## 主要修改

### 1. 函数签名保持不变
```python
def recover_3d_points(quad_2d, G, K, dist):
```

### 2. 核心算法改进

**原始方法**：
- 使用简化的线性方程组
- 错误地简化了相机内参矩阵K的处理
- 每个点只构造一个约束方程

**新方法**：
- 构造完整的齐次线性方程组 `A * X_hom = 0`
- 正确使用3x3相机内参矩阵K，提取fx, fy, cx, cy参数
- 每个点构造4个约束方程：
  - 2个来自投影仪约束（消除标量a）
  - 2个来自相机约束（消除标量b，使用完整的K矩阵）
- 使用SVD的最小奇异值对应的右奇异向量作为解

### 3. 数学推导

#### 投影仪约束
```
a * [u_proj, v_proj, w_proj]^T = G * [X, Y, Z, 1]^T
```
消除标量a得到：
```
u_proj * G[2,:] - w_proj * G[0,:] = 0
v_proj * G[2,:] - w_proj * G[1,:] = 0
```

#### 相机约束
```
b * [u_cam, v_cam, 1]^T = K * [X, Y, Z]^T
```
其中K是3x3相机内参矩阵，展开为：
```
b * u_cam = fx*X + cx*Z
b * v_cam = fy*Y + cy*Z
b * 1 = Z
```
消除标量b得到：
```
fx*X + (cx - u_cam)*Z = 0
fy*Y + (cy - v_cam)*Z = 0
```

### 4. 线性方程组构造

```python
A = np.array([
    # 投影仪约束方程1
    [u_proj*G[2,0] - w_proj*G[0,0], u_proj*G[2,1] - w_proj*G[0,1], 
     u_proj*G[2,2] - w_proj*G[0,2], u_proj*G[2,3] - w_proj*G[0,3]],
    
    # 投影仪约束方程2
    [v_proj*G[2,0] - w_proj*G[1,0], v_proj*G[2,1] - w_proj*G[1,1], 
     v_proj*G[2,2] - w_proj*G[1,2], v_proj*G[2,3] - w_proj*G[1,3]],
    
    # 相机约束方程1：fx*X + (cx - u_cam)*Z = 0
    [fx, 0, cx - u_cam, 0],

    # 相机约束方程2：fy*Y + (cy - v_cam)*Z = 0
    [0, fy, cy - v_cam, 0]
])
```

### 5. SVD求解

```python
_, _, VT = np.linalg.svd(A)
X_hom = VT[-1]  # 最小奇异值对应的右奇异向量

# 齐次坐标归一化
if abs(X_hom[3]) > 1e-8:
    X_3d = X_hom[:3] / X_hom[3]
else:
    X_3d = X_hom[:3]
```

## 其他修复

### 1. coplanar_optimization函数修复
修复了维度不匹配问题：
```python
# 修复前
alpha_pred = (G1 @ Xi) / (G3 @ Xi + 1e-6)  # Xi是3维，G1是4维

# 修复后
Xi_hom = np.append(Xi, 1)  # 转换为齐次坐标
alpha_pred = (G1 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
```

### 2. 添加测试图像生成
当测试图像不存在时，自动创建一个简单的测试图像。

### 3. 导入os模块
添加了 `import os` 以支持文件存在性检查。

## 测试结果

### 1. 单元测试
- 创建了 `test_recover_3d.py` 测试脚本
- 测试了模拟数据和真实数据
- 所有测试通过，输出数值正常

### 2. 集成测试
- 主程序 `main.py` 成功运行
- 3D点恢复正常
- 共面约束优化正常
- 预扭曲图像生成成功

## 优势

1. **数学严谨性**：基于完整的线性代数理论
2. **鲁棒性**：SVD方法对噪声具有良好的鲁棒性
3. **数值稳定性**：SVD分解数值稳定
4. **完整性**：同时考虑投影仪和相机约束

## 文件结构

```
algo2_2/
├── main.py                    # 主程序（已修改）
├── test_recover_3d.py         # 测试脚本（新增）
├── SVD_method_explanation.md  # 数学原理说明（新增）
├── CHANGES_SUMMARY.md         # 修改总结（本文件）
└── test_image.jpg            # 自动生成的测试图像
```

## 使用方法

```python
# 调用修改后的函数
X_3d = recover_3d_points(quad_2d, G, K, dist)

# 其中：
# quad_2d: 相机中的2D四边形坐标 (4x2)
# G: 投影矩阵 (3x4)
# K: 相机内参矩阵 (3x3)
# dist: 畸变系数 (5,)
# 返回: X_3d 恢复的3D点坐标 (4x3)
```

修改完成，函数现在使用严格的SVD方法求解3D点坐标。
