#include "square_corner_detector.hpp"
#include <algorithm>
#include <cmath>

SquareCornerDetector::SquareCornerDetector(double minArea, double maxArea, double approxEpsilon, 
                                         double cannyLow, double cannyHigh)
    : minArea_(minArea), maxArea_(maxArea), approxEpsilon_(approxEpsilon),
      cannyLow_(cannyLow), cannyHigh_(cannyHigh) {
}

SquareCornerDetector::~SquareCornerDetector() {
}

bool SquareCornerDetector::detectSquareCorners(const cv::Mat& image) {
    // Clear previous results
    detectedSquares_.clear();
    gridCorners_.isValid = false;
    
    if (image.empty()) {
        return false;
    }
    
    // Preprocess the image
    cv::Mat binary = preprocessImage(image);
    
    // Detect square contours
    std::vector<std::vector<cv::Point>> contours = detectSquareContours(binary);
    
    // Filter valid square contours
    std::vector<std::vector<cv::Point>> validContours = filterSquareContours(contours);
    
    // Check if we have exactly 4 squares
    if (validContours.size() != 4) {
        return false;
    }
    
    // Convert contours to DetectedSquare objects
    convertContoursToSquares(validContours);
    
    // Arrange squares and extract corner points
    return arrangeSquaresAndExtractCorners();
}

cv::Mat SquareCornerDetector::preprocessImage(const cv::Mat& image) {
    cv::Mat gray, binary;
    
    // Convert to grayscale if needed
    if (image.channels() == 3) {
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    } else {
        gray = image.clone();
    }
    
    // Apply Gaussian blur to reduce noise
    cv::GaussianBlur(gray, gray, cv::Size(5, 5), 0);
    
    // Apply threshold to get binary image (white squares on dark background)
    cv::threshold(gray, binary, 0, 255, cv::THRESH_BINARY + cv::THRESH_OTSU);
    
    // Apply morphological operations to clean up the image
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(3, 3));
    cv::morphologyEx(binary, binary, cv::MORPH_CLOSE, kernel);
    cv::morphologyEx(binary, binary, cv::MORPH_OPEN, kernel);
    
    return binary;
}

std::vector<std::vector<cv::Point>> SquareCornerDetector::detectSquareContours(const cv::Mat& binary) {
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    
    // Find contours
    cv::findContours(binary, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    
    return contours;
}

std::vector<std::vector<cv::Point>> SquareCornerDetector::filterSquareContours(
    const std::vector<std::vector<cv::Point>>& contours) {
    
    std::vector<std::vector<cv::Point>> validContours;
    
    for (const auto& contour : contours) {
        // Check area constraint
        double area = cv::contourArea(contour);
        if (area < minArea_ || area > maxArea_) {
            continue;
        }
        
        // Check if contour approximates to a square
        if (isSquareContour(contour)) {
            validContours.push_back(contour);
        }
    }
    
    return validContours;
}

bool SquareCornerDetector::isSquareContour(const std::vector<cv::Point>& contour) {
    // Approximate contour to polygon
    std::vector<cv::Point> approx;
    double epsilon = approxEpsilon_ * cv::arcLength(contour, true);
    cv::approxPolyDP(contour, approx, epsilon, true);
    
    // Check if we have 4 vertices (square/rectangle)
    if (approx.size() != 4) {
        return false;
    }
    
    // Check if the contour is convex
    if (!cv::isContourConvex(approx)) {
        return false;
    }
    
    // Additional check: verify that angles are approximately 90 degrees
    std::vector<double> angles;
    for (int i = 0; i < 4; i++) {
        cv::Point2f v1 = approx[(i + 1) % 4] - approx[i];
        cv::Point2f v2 = approx[(i + 2) % 4] - approx[(i + 1) % 4];
        
        double angle = std::abs(std::atan2(v1.x * v2.y - v1.y * v2.x, v1.x * v2.x + v1.y * v2.y));
        angles.push_back(angle);
    }
    
    // Check if all angles are close to 90 degrees (π/2)
    const double angleThreshold = 0.3; // ~17 degrees tolerance
    for (double angle : angles) {
        if (std::abs(angle - CV_PI/2) > angleThreshold) {
            return false;
        }
    }
    
    return true;
}

void SquareCornerDetector::convertContoursToSquares(const std::vector<std::vector<cv::Point>>& contours) {
    for (const auto& contour : contours) {
        DetectedSquare square;
        
        // Approximate contour to get corner points
        std::vector<cv::Point> approx;
        double epsilon = approxEpsilon_ * cv::arcLength(contour, true);
        cv::approxPolyDP(contour, approx, epsilon, true);
        
        // Convert to Point2f and store corners
        for (const auto& point : approx) {
            square.corners.push_back(cv::Point2f(point.x, point.y));
        }
        
        // Calculate center and area
        square.center = calculateCenter(contour);
        square.area = cv::contourArea(contour);
        
        detectedSquares_.push_back(square);
    }
}

cv::Point2f SquareCornerDetector::calculateCenter(const std::vector<cv::Point>& contour) {
    cv::Moments moments = cv::moments(contour);
    if (moments.m00 != 0) {
        return cv::Point2f(moments.m10 / moments.m00, moments.m01 / moments.m00);
    }
    return cv::Point2f(0, 0);
}

bool SquareCornerDetector::arrangeSquaresAndExtractCorners() {
    if (detectedSquares_.size() != 4) {
        return false;
    }
    
    // Sort squares by position
    std::vector<DetectedSquare> sortedSquares = sortSquaresByPosition(detectedSquares_);
    
    // Extract corner points from each square
    // Top-left square: get top-left corner
    auto& topLeftSquare = sortedSquares[0];
    gridCorners_.topLeft = *std::min_element(topLeftSquare.corners.begin(), topLeftSquare.corners.end(),
        [](const cv::Point2f& a, const cv::Point2f& b) {
            return (a.x + a.y) < (b.x + b.y);
        });
    
    // Top-right square: get top-right corner
    auto& topRightSquare = sortedSquares[1];
    gridCorners_.topRight = *std::max_element(topRightSquare.corners.begin(), topRightSquare.corners.end(),
        [](const cv::Point2f& a, const cv::Point2f& b) {
            return (a.x - a.y) < (b.x - b.y);
        });
    
    // Bottom-left square: get bottom-left corner
    auto& bottomLeftSquare = sortedSquares[2];
    gridCorners_.bottomLeft = *std::min_element(bottomLeftSquare.corners.begin(), bottomLeftSquare.corners.end(),
        [](const cv::Point2f& a, const cv::Point2f& b) {
            return (a.x - a.y) < (b.x - b.y);
        });
    
    // Bottom-right square: get bottom-right corner
    auto& bottomRightSquare = sortedSquares[3];
    gridCorners_.bottomRight = *std::max_element(bottomRightSquare.corners.begin(), bottomRightSquare.corners.end(),
        [](const cv::Point2f& a, const cv::Point2f& b) {
            return (a.x + a.y) < (b.x + b.y);
        });
    
    gridCorners_.isValid = true;
    return true;
}

std::vector<DetectedSquare> SquareCornerDetector::sortSquaresByPosition(const std::vector<DetectedSquare>& squares) {
    std::vector<DetectedSquare> sorted = squares;

    // Sort by center position: first by y-coordinate, then by x-coordinate
    std::sort(sorted.begin(), sorted.end(), [](const DetectedSquare& a, const DetectedSquare& b) {
        if (std::abs(a.center.y - b.center.y) < 50) { // Same row (tolerance for alignment)
            return a.center.x < b.center.x; // Sort by x-coordinate
        }
        return a.center.y < b.center.y; // Sort by y-coordinate
    });

    // Rearrange to get: [top-left, top-right, bottom-left, bottom-right]
    std::vector<DetectedSquare> result(4);

    // Determine which squares are in top row and bottom row
    std::vector<DetectedSquare> topRow, bottomRow;

    // Find the median y-coordinate to separate top and bottom rows
    std::vector<float> yCoords;
    for (const auto& square : sorted) {
        yCoords.push_back(square.center.y);
    }
    std::sort(yCoords.begin(), yCoords.end());
    float medianY = (yCoords[1] + yCoords[2]) / 2.0f;

    for (const auto& square : sorted) {
        if (square.center.y < medianY) {
            topRow.push_back(square);
        } else {
            bottomRow.push_back(square);
        }
    }

    // Sort each row by x-coordinate
    std::sort(topRow.begin(), topRow.end(), [](const DetectedSquare& a, const DetectedSquare& b) {
        return a.center.x < b.center.x;
    });
    std::sort(bottomRow.begin(), bottomRow.end(), [](const DetectedSquare& a, const DetectedSquare& b) {
        return a.center.x < b.center.x;
    });

    // Assign positions: [top-left, top-right, bottom-left, bottom-right]
    if (topRow.size() >= 2 && bottomRow.size() >= 2) {
        result[0] = topRow[0];    // top-left
        result[1] = topRow[1];    // top-right
        result[2] = bottomRow[0]; // bottom-left
        result[3] = bottomRow[1]; // bottom-right
    }

    return result;
}

cv::Mat SquareCornerDetector::drawResults(const cv::Mat& image, bool drawSquares, bool drawCorners) const {
    cv::Mat result = image.clone();

    if (drawSquares) {
        // Draw detected squares
        for (const auto& square : detectedSquares_) {
            std::vector<cv::Point> intCorners;
            for (const auto& corner : square.corners) {
                intCorners.push_back(cv::Point(static_cast<int>(corner.x), static_cast<int>(corner.y)));
            }

            // Draw square contour
            cv::polylines(result, intCorners, true, cv::Scalar(0, 255, 0), 2);

            // Draw center point
            cv::circle(result, cv::Point(static_cast<int>(square.center.x), static_cast<int>(square.center.y)),
                      5, cv::Scalar(255, 0, 0), -1);
        }
    }

    if (drawCorners && gridCorners_.isValid) {
        // Draw the four corner points of the grid
        cv::circle(result, cv::Point(static_cast<int>(gridCorners_.topLeft.x), static_cast<int>(gridCorners_.topLeft.y)),
                  8, cv::Scalar(0, 0, 255), -1);
        cv::circle(result, cv::Point(static_cast<int>(gridCorners_.topRight.x), static_cast<int>(gridCorners_.topRight.y)),
                  8, cv::Scalar(0, 0, 255), -1);
        cv::circle(result, cv::Point(static_cast<int>(gridCorners_.bottomLeft.x), static_cast<int>(gridCorners_.bottomLeft.y)),
                  8, cv::Scalar(0, 0, 255), -1);
        cv::circle(result, cv::Point(static_cast<int>(gridCorners_.bottomRight.x), static_cast<int>(gridCorners_.bottomRight.y)),
                  8, cv::Scalar(0, 0, 255), -1);

        // Add labels for corner points
        cv::putText(result, "TL", cv::Point(static_cast<int>(gridCorners_.topLeft.x - 20), static_cast<int>(gridCorners_.topLeft.y - 10)),
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(0, 0, 255), 2);
        cv::putText(result, "TR", cv::Point(static_cast<int>(gridCorners_.topRight.x + 10), static_cast<int>(gridCorners_.topRight.y - 10)),
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(0, 0, 255), 2);
        cv::putText(result, "BL", cv::Point(static_cast<int>(gridCorners_.bottomLeft.x - 20), static_cast<int>(gridCorners_.bottomLeft.y + 20)),
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(0, 0, 255), 2);
        cv::putText(result, "BR", cv::Point(static_cast<int>(gridCorners_.bottomRight.x + 10), static_cast<int>(gridCorners_.bottomRight.y + 20)),
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(0, 0, 255), 2);
    }

    return result;
}
