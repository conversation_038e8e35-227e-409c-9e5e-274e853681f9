@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Simple Windows Build Script
echo ========================================
echo.
echo This script will attempt to build simulate_center_stage.exe
echo using system-installed OpenCV if Windows prebuilt libraries are not available.
echo.

REM Set project variables
set PROJECT_NAME=czcv_camera
set BUILD_TYPE=Release
set BUILD_DIR=build_windows_simple
set OUTPUT_DIR=output\windows_simple

REM Check for CMake
cmake --version >nul 2>&1
if !errorlevel! neq 0 (
    echo [ERROR] CMake not found! Please install CMake.
    pause
    exit /b 1
)
echo [OK] CMake found

REM Setup Visual Studio environment
echo Setting up Visual Studio environment...
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
) else (
    echo [ERROR] Visual Studio not found!
    pause
    exit /b 1
)

REM Create directories
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
mkdir "%BUILD_DIR%"
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

echo.
echo Configuring CMake with system OpenCV...
cd "%BUILD_DIR%"

REM Try to build with system OpenCV first
cmake .. ^
    -G "Visual Studio 16 2019" ^
    -A x64 ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DDst_Platform=linux_x86 ^
    -DBUILD_TESTS=ON ^
    -DBUILD_Release=ON ^
    -DBUILD_Shared=OFF ^
    -DCMAKE_RUNTIME_OUTPUT_DIRECTORY="%CD%\..\%OUTPUT_DIR%"

if !errorlevel! neq 0 (
    echo [WARNING] CMake configuration with system OpenCV failed.
    echo Trying alternative configuration...
    
    REM Try with minimal configuration
    cmake .. ^
        -G "Visual Studio 16 2019" ^
        -A x64 ^
        -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
        -DBUILD_TESTS=ON ^
        -DBUILD_Release=ON ^
        -DBUILD_Shared=OFF
    
    if !errorlevel! neq 0 (
        echo [ERROR] CMake configuration failed!
        cd ..
        pause
        exit /b 1
    )
)

echo.
echo Building simulate_center_stage...
cmake --build . --config %BUILD_TYPE% --target simulate_center_stage

if !errorlevel! neq 0 (
    echo [ERROR] Build failed!
    echo.
    echo This is likely due to missing dependencies. Please ensure you have:
    echo 1. OpenCV 4.5.1 installed and accessible
    echo 2. All required third-party libraries
    echo.
    echo Consider downloading the Windows prebuilt libraries and using
    echo the main build script: build_windows_simulate_center_stage.bat
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo ========================================
echo Build completed!
echo ========================================

REM Check for executable
if exist "%OUTPUT_DIR%\%BUILD_TYPE%\simulate_center_stage.exe" (
    echo [SUCCESS] simulate_center_stage.exe created successfully!
    echo Location: %OUTPUT_DIR%\%BUILD_TYPE%\simulate_center_stage.exe
) else if exist "%BUILD_DIR%\test\%BUILD_TYPE%\simulate_center_stage.exe" (
    echo [SUCCESS] simulate_center_stage.exe created successfully!
    echo Location: %BUILD_DIR%\test\%BUILD_TYPE%\simulate_center_stage.exe
    echo Copying to output directory...
    copy "%BUILD_DIR%\test\%BUILD_TYPE%\simulate_center_stage.exe" "%OUTPUT_DIR%\"
) else (
    echo [WARNING] simulate_center_stage.exe not found in expected locations.
    echo Please check the build output for the actual location.
)

echo.
echo Build completed at: %date% %time%
pause
