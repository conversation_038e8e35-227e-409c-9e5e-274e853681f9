@echo off
echo ========================================
echo Android ARM64 Build Summary
echo ========================================
echo.

echo Project: czcv_camera
echo Target Platform: Android ARM64 (arm64-v8a)
echo Build Type: Release
echo.

echo Checking build output...
if exist "output\android_v8a\libs\libczcv_camera.so" (
    echo [SUCCESS] Library built successfully!
    echo.
    echo Library Details:
    echo   Location: output\android_v8a\libs\libczcv_camera.so
    echo   Architecture: ARM64 (aarch64)
    echo   Type: ELF 64-bit LSB shared object
    
    echo.
    echo Third-party Libraries Used:
    if exist "third_party\prebuilt\android_v8a\opencv4.5.1" (
        echo   [OK] OpenCV 4.5.1
    ) else (
        echo   [MISSING] OpenCV 4.5.1
    )
    
    if exist "third_party\prebuilt\android_v8a\glog" (
        echo   [OK] glog 0.3.5
    ) else (
        echo   [MISSING] glog 0.3.5
    )
    
    if exist "third_party\prebuilt\android_v8a\TNN" (
        echo   [OK] TNN
    ) else (
        echo   [MISSING] TNN
    )
    
    echo.
    echo Build Configuration:
    echo   CMake Generator: MSYS Makefiles
    echo   Android NDK: D:\Package\android-ndk-r23c
    echo   Android API Level: 24
    echo   Android STL: c++_shared
    echo   Android ABI: arm64-v8a
    echo   Dst_Platform: android_v8a
    
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Next Steps:
    echo 1. Copy libczcv_camera.so to your Android project
    echo 2. Place it in: app/src/main/jniLibs/arm64-v8a/
    echo 3. Load the library in Java/Kotlin code
    
) else (
    echo [ERROR] Library not found!
    echo Expected location: output\android_v8a\libs\libczcv_camera.so
    echo.
    echo Please run the build script first:
    echo   build_armeabi_arm64.bat
)

echo.
echo ========================================
pause
