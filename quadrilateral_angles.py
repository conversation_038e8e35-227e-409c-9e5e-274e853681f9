#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四边形角度计算模块
给定四个二维点，计算四边形的四个内角
"""

import numpy as np
import math


def calculate_angle(p1, p2, p3):
    """
    计算由三个点组成的角度（以p2为顶点）
    
    Args:
        p1: 第一个点 (x, y)
        p2: 顶点 (x, y)  
        p3: 第三个点 (x, y)
        
    Returns:
        float: 角度（度数）
    """
    # 计算向量
    v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
    v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])
    
    # 计算向量的模长
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    
    # 避免除零错误
    if norm_v1 == 0 or norm_v2 == 0:
        return 0
    
    # 计算余弦值
    cos_angle = np.dot(v1, v2) / (norm_v1 * norm_v2)
    
    # 限制余弦值在[-1, 1]范围内，避免数值误差
    cos_angle = np.clip(cos_angle, -1, 1)
    
    # 计算角度（弧度转度数）
    angle_rad = np.arccos(cos_angle)
    angle_deg = np.degrees(angle_rad)
    
    return angle_deg


def calculate_quadrilateral_angles(points):
    """
    计算四边形的四个内角
    
    Args:
        points: 四个点的列表，每个点为(x, y)元组，按顺序排列
        
    Returns:
        list: 四个角度的列表（度数）
        
    Raises:
        ValueError: 如果点的数量不是4个
    """
    if len(points) != 4:
        raise ValueError("必须提供4个点")
    
    angles = []
    n = len(points)
    
    # 计算每个顶点的内角
    for i in range(n):
        # 获取当前顶点和相邻的两个顶点
        prev_point = points[(i - 1) % n]  # 前一个点
        current_point = points[i]         # 当前点
        next_point = points[(i + 1) % n]  # 下一个点
        
        # 计算角度
        angle = calculate_angle(prev_point, current_point, next_point)
        angles.append(angle)
    
    return angles


def print_quadrilateral_info(points):
    """
    打印四边形的详细信息
    
    Args:
        points: 四个点的列表
        
    Returns:
        list: 四个角度的列表
    """
    print("四边形顶点坐标:")
    for i, point in enumerate(points):
        print(f"  点{i+1}: ({point[0]:.2f}, {point[1]:.2f})")
    
    angles = calculate_quadrilateral_angles(points)
    
    print("\n四边形内角:")
    for i, angle in enumerate(angles):
        print(f"  角{i+1}: {angle:.2f}°")
    
    print(f"\n内角和: {sum(angles):.2f}° (理论值应为360°)")
    
    return angles


def validate_quadrilateral(points):
    """
    验证四个点是否能构成有效的四边形
    
    Args:
        points: 四个点的列表
        
    Returns:
        tuple: (is_valid, message)
    """
    if len(points) != 4:
        return False, "必须提供4个点"
    
    # 检查是否有重复点
    for i in range(len(points)):
        for j in range(i + 1, len(points)):
            if points[i] == points[j]:
                return False, f"点{i+1}和点{j+1}重复"
    
    # 检查是否所有点共线
    # 使用叉积判断
    def cross_product(o, a, b):
        return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])
    
    # 检查前三个点是否共线
    if abs(cross_product(points[0], points[1], points[2])) < 1e-10:
        return False, "前三个点共线，无法构成四边形"
    
    return True, "有效的四边形"


if __name__ == "__main__":
    # 测试示例
    print("=== 四边形角度计算测试 ===")
    
    # 示例1: 正方形
    print("\n示例1: 正方形")
    square_points = [(223, 110), (1093,126), (1174, 650), (137, 637)]
    is_valid, msg = validate_quadrilateral(square_points)
    print(f"验证结果: {msg}")
    if is_valid:
        print_quadrilateral_info(square_points)
    
    # # 示例2: 矩形  
    # print("\n示例2: 矩形")
    # rectangle_points = [(0, 0), (3, 0), (3, 2), (0, 2)]
    # print_quadrilateral_info(rectangle_points)
    
    # # 示例3: 任意四边形
    # print("\n示例3: 任意四边形")
    # arbitrary_points = [(0, 0), (4, 1), (3, 4), (1, 3)]
    # print_quadrilateral_info(arbitrary_points)
