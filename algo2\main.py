import cv2
from warp import compute_warp

cam = cv2.VideoCapture(0)
cv2.namedWindow('proj', cv2.WINDOW_NORMAL)
cv2.resizeWindow('proj', 1024, 768)

# 加载待投射内容（可换成桌面抓屏或 PPT 窗口）
content = cv2.imread('slide.png')
h, w = content.shape[:2]
content = cv2.resize(content, (w*768//h, 768))

while True:
    warped = compute_warp(cam, content)
    if warped is not None:
        cv2.imshow('proj', warped)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cam.release()
cv2.destroyAllWindows()