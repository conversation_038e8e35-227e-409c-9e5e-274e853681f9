import numpy as np
from scipy.optimize import minimize
from shapely.geometry import Polygon, Point

# 给定的四边形顶点（按顺序，假设是凸四边形且顺序正确）
pts_3d = np.array([
    [-686.32386643, -434.34548246, 1277.21997129],
    [ 770.37500943, -531.64785369, 1601.19735203],
    [ 820.0434904 ,  339.2766473 , 1407.01233744],
    [-706.22383899,  252.71124411, 1111.81707544]
])

# 步骤1: 计算平面法向量
v1 = pts_3d[1] - pts_3d[0]
v2 = pts_3d[3] - pts_3d[0]
normal = np.cross(v1, v2)
normal /= np.linalg.norm(normal)

# 步骤2: 构建局部坐标系 (u, v)
u_axis = pts_3d[1] - pts_3d[0]
u_axis -= np.dot(u_axis, normal) * normal  # 去除法向分量（确保在平面内）
u_axis /= np.linalg.norm(u_axis)
v_axis = np.cross(normal, u_axis)
v_axis /= np.linalg.norm(v_axis)

# 原点
origin = pts_3d[0]

# 投影函数：3D -> 2D (u, v)
def project_to_2d(p):
    vec = p - origin
    u = np.dot(vec, u_axis)
    v = np.dot(vec, v_axis)
    return np.array([u, v])

# 将四边形投影到2D
pts_2d = np.array([project_to_2d(p) for p in pts_3d])

# 创建Shapely多边形用于点包含测试
poly_2d = Polygon(pts_2d)

# 高宽比 r = height / width = 0.8
r = 0.8

# 定义目标函数：最大化面积 = w * h = w * (r * w) = r * w^2 → 最小化 -r*w^2
def objective(x):
    # x = [cx, cy, w]  # 中心x, 中心y, 宽度
    cx, cy, w = x
    h = r * w
    if w <= 0:
        return 1e6
    # 四个角点（轴对齐矩形）
    corners = [
        (cx - w/2, cy - h/2),
        (cx + w/2, cy - h/2),
        (cx + w/2, cy + h/2),
        (cx - w/2, cy + h/2)
    ]
    # 检查是否所有角点都在多边形内
    for pt in corners:
        if not poly_2d.contains(Point(pt)) and not poly_2d.touches(Point(pt)):
            return 1e6  # 惩罚项
    return -r * w * w  # 负面积（最大化）

# 获取2D点的范围作为初始猜测和边界
u_vals, v_vals = pts_2d[:, 0], pts_2d[:, 1]
u_min, u_max = u_vals.min(), u_vals.max()
v_min, v_max = v_vals.min(), v_vals.max()

# 初始猜测：中心在多边形中心，宽度为范围的一半
init_w = (u_max - u_min) * 0.5
init_cx = (u_max + u_min) / 2
init_cy = (v_max + v_min) / 2

# 优化变量边界
bounds = [
    (u_min, u_max),      # cx
    (v_min, v_max),      # cy
    (1e-3, u_max - u_min)  # w > 0
]

# 执行优化
result = minimize(objective, x0=[init_cx, init_cy, init_w], bounds=bounds, method='L-BFGS-B')

if result.success:
    cx_opt, cy_opt, w_opt = result.x
    h_opt = r * w_opt
    print(f"Optimal rectangle in 2D:")
    print(f"Center: ({cx_opt:.3f}, {cy_opt:.3f})")
    print(f"Width: {w_opt:.3f}, Height: {h_opt:.3f}")
    print(f"Area: {w_opt * h_opt:.3f}")

    # 构造2D角点
    rect_2d = np.array([
        [cx_opt - w_opt/2, cy_opt - h_opt/2],
        [cx_opt + w_opt/2, cy_opt - h_opt/2],
        [cx_opt + w_opt/2, cy_opt + h_opt/2],
        [cx_opt - w_opt/2, cy_opt + h_opt/2]
    ])

    # 转换回3D
    def unproject_to_3d(p2d):
        return origin + p2d[0] * u_axis + p2d[1] * v_axis

    rect_3d = np.array([unproject_to_3d(p) for p in rect_2d])
    print("\n3D coordinates of the rectangle:")
    for i, p in enumerate(rect_3d):
        print(f"Point {i}: [{p[0]:.6f}, {p[1]:.6f}, {p[2]:.6f}]")
else:
    print("Optimization failed:", result.message)