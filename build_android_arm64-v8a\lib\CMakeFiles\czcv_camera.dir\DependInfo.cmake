
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "D:/Program/Project/project/czcv_camera_new/lib/src/base/abstract_model.cpp" "lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/base/aes256.cpp" "lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/base/common.cpp" "lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/base/macro.cpp" "lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/base/mem_allocator.cpp" "lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/base/nms.cpp" "lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/base/status.cpp" "lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/cam_dewarper.cpp" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_api.cpp" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/center_stage_capi.cpp" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/czcv_center_stage.cpp" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/center_stage/person_viewer.cpp" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/config/config_setter.cpp" "lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/detector/base_detector.cpp" "lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/detector/detail/rknn_yolox.cpp" "lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/detector/detail/tnn_yolox.cpp" "lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/detector/detect_white_board.cpp" "lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/detector/detect_white_board.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/detector/detector_factory.cpp" "lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/detector/yolox_person_det.cpp" "lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/hand/rknn_yolov10.cpp" "lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/libopencl-stub/src/libopencl.cpp" "lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/libopencl-stub/src/libopencl.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/tracker/base_tracker.cpp" "lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/tracker/byte_tracker.cpp" "lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/tracker/detail/kalmanFilter.cpp" "lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/person_assert.cpp" "lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/tracker/person_assert/rknn_person_assert.cpp" "lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/tracker/tracker_factory.cpp" "lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/utils/hungarian_match.cpp" "lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o.d"
  "D:/Program/Project/project/czcv_camera_new/lib/src/utils/lapjv.cpp" "lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o" "gcc" "lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
