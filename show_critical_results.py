import pandas as pd
import numpy as np

def show_critical_results():
    """
    显示相邻角度之间的临界c值计算结果
    """
    try:
        # 读取结果文件
        df = pd.read_csv('critical_values_results.csv')
        
        print("=" * 90)
        print("相邻角度之间的临界c值计算结果")
        print("=" * 90)
        
        print("\n【方法说明】")
        print("• 交点法: 通过分析两个角度分布曲线的交点确定临界值")
        print("• 错误最小化法: 通过最小化分类错误确定最佳阈值")
        print("• 中点法: 使用两个角度分布中心的中点作为临界值")
        print("• 重叠度: 两个角度分布重叠区域占总范围的百分比")
        
        print(f"\n【数据概览】")
        print(f"相邻角度对数: {len(df)}")
        print(f"角度范围: {df['angle1'].min()}° ~ {df['angle2'].max()}°")
        print(f"总样本数: {df['samples1'].sum() + df['samples2'].iloc[-1]}")
        
        print("\n【详细临界值结果】")
        print("角度对        交点法    错误最小化    中点法    错误率%  重叠度%  样本数")
        print("-" * 85)
        for _, row in df.iterrows():
            print(f"{row['angle1']:6.1f}°-{row['angle2']:6.1f}°  {row['critical_intersection']:8.1f}  "
                  f"{row['critical_error_min']:10.1f}  {row['critical_midpoint']:8.1f}  "
                  f"{row['error_rate_percent']:7.1f}  {row['overlap_percentage']:7.1f}  "
                  f"{row['samples1']:3.0f}+{row['samples2']:3.0f}")
        
        print("\n【推荐临界值 - 基于交点法】")
        print("角度对        临界c值    分类建议")
        print("-" * 50)
        for _, row in df.iterrows():
            critical_val = row['critical_intersection']
            if row['overlap_percentage'] < 5:
                quality = "优秀"
            elif row['overlap_percentage'] < 20:
                quality = "良好"
            elif row['overlap_percentage'] < 40:
                quality = "一般"
            else:
                quality = "困难"
            
            print(f"{row['angle1']:6.1f}°-{row['angle2']:6.1f}°  {critical_val:10.1f}    {quality}")
        
        print("\n【分类质量分析】")
        # 按重叠度分类
        excellent = df[df['overlap_percentage'] < 5]
        good = df[(df['overlap_percentage'] >= 5) & (df['overlap_percentage'] < 20)]
        fair = df[(df['overlap_percentage'] >= 20) & (df['overlap_percentage'] < 40)]
        difficult = df[df['overlap_percentage'] >= 40]
        
        print(f"优秀分离 (重叠<5%): {len(excellent)} 对")
        if len(excellent) > 0:
            for _, row in excellent.iterrows():
                print(f"  {row['angle1']}°-{row['angle2']}°: 重叠度 {row['overlap_percentage']:.1f}%")
        
        print(f"良好分离 (重叠5-20%): {len(good)} 对")
        if len(good) > 0:
            for _, row in good.iterrows():
                print(f"  {row['angle1']}°-{row['angle2']}°: 重叠度 {row['overlap_percentage']:.1f}%")
        
        print(f"一般分离 (重叠20-40%): {len(fair)} 对")
        if len(fair) > 0:
            for _, row in fair.iterrows():
                print(f"  {row['angle1']}°-{row['angle2']}°: 重叠度 {row['overlap_percentage']:.1f}%")
        
        print(f"困难分离 (重叠>40%): {len(difficult)} 对")
        if len(difficult) > 0:
            for _, row in difficult.iterrows():
                print(f"  {row['angle1']}°-{row['angle2']}°: 重叠度 {row['overlap_percentage']:.1f}%")
        
        print("\n【统计信息】")
        print(f"平均错误率: {df['error_rate_percent'].mean():.1f}%")
        print(f"平均重叠度: {df['overlap_percentage'].mean():.1f}%")
        print(f"最低错误率: {df['error_rate_percent'].min():.1f}% ({df.loc[df['error_rate_percent'].idxmin(), 'angle1']}°-{df.loc[df['error_rate_percent'].idxmin(), 'angle2']}°)")
        print(f"最高错误率: {df['error_rate_percent'].max():.1f}% ({df.loc[df['error_rate_percent'].idxmax(), 'angle1']}°-{df.loc[df['error_rate_percent'].idxmax(), 'angle2']}°)")
        
        print("\n【临界值范围分析】")
        print("角度对        c值范围1        c值范围2        临界值位置")
        print("-" * 70)
        for _, row in df.iterrows():
            range1 = f"[{row['range1_min']:.0f}, {row['range1_max']:.0f}]"
            range2 = f"[{row['range2_min']:.0f}, {row['range2_max']:.0f}]"
            critical = row['critical_intersection']
            
            # 判断临界值位置
            if critical < row['range1_min']:
                position = "左侧"
            elif critical > row['range1_max']:
                position = "右侧"
            elif row['range1_min'] <= critical <= row['range1_max']:
                if row['range2_min'] <= critical <= row['range2_max']:
                    position = "重叠区"
                else:
                    position = "范围1内"
            else:
                position = "范围2内"
            
            print(f"{row['angle1']:6.1f}°-{row['angle2']:6.1f}°  {range1:>15}  {range2:>15}  {position}")
        
        print("\n【实用建议】")
        print("1. 优先使用交点法的临界值，它基于分布曲线的自然交点")
        print("2. 对于重叠度高的角度对，考虑:")
        print("   - 增加样本数量以获得更稳定的分布")
        print("   - 检查数据质量，排除异常值")
        print("   - 考虑使用更复杂的分类方法")
        print("3. 对于重叠度低的角度对，临界值设定相对简单可靠")
        
        print("\n【DOA分类阈值表】")
        print("# 可直接用于DOA系统的分类阈值")
        print("if c_value > {:.1f}: angle = {:.1f}°".format(df.iloc[0]['critical_intersection'], df.iloc[0]['angle1']))
        for i, row in df.iterrows():
            if i < len(df) - 1:
                next_critical = df.iloc[i+1]['critical_intersection']
                print("elif c_value > {:.1f}: angle = {:.1f}°".format(next_critical, row['angle2']))
        print("else: angle = {:.1f}°".format(df.iloc[-1]['angle2']))
        
        print("\n" + "=" * 90)
        print("注：临界值用于判断测量值c应该归类到哪个角度")
        print("建议结合实际应用场景和精度要求选择合适的临界值方法")
        print("=" * 90)
        
    except FileNotFoundError:
        print("错误: 找不到结果文件 'critical_values_results.csv'")
        print("请先运行 'python find_critical_values.py' 生成结果文件")
    except Exception as e:
        print(f"读取结果时出错: {e}")

if __name__ == "__main__":
    show_critical_results()
