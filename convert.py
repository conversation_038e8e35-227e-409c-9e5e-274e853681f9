import cv2
import numpy as np
import argparse
import os
import math

def nv12_to_bgr(input_path, output_path, width, height):
    """
    将 NV12 二进制文件转换为 BGR 格式的图片
    :param input_path: 输入的 NV12 文件路径
    :param output_path: 输出的 BGR 图片路径
    :param width: 图像宽度 (3840)
    :param height: 图像高度 (2160)
    """
    try:
        # 计算 NV12 文件预期大小
        expected_size = width * height * 3 // 2
        
        # 读取二进制数据
        with open(input_path, 'rb') as f:
            nv12_data = f.read()
        
        # 验证文件大小
        if len(nv12_data) != expected_size:
            raise ValueError(f"文件大小错误: 预期 {expected_size} 字节, 实际 {len(nv12_data)} 字节")
        
        # 转换为 numpy 数组并重塑形状
        nv12_array = np.frombuffer(nv12_data, dtype=np.uint8)
        bgr_image = nv12_array.reshape((height * 3 // 2, width,1))  # 关键步骤：NV12 格式的特殊形状
        bgr_image = cv2.cvtColor(bgr_image, cv2.COLOR_YUV2BGR_NV12)  # 关键步骤：颜色空间转换
        
        # 执行颜色空间转换
 
        # 保存结果
        cv2.imwrite(output_path, bgr_image)
        print(f"转换成功! 结果已保存至: {output_path}")
        
        return True
    
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

def calculate_angle(p1, p2, p3):
    """
    计算由三个点组成的角度（以p2为顶点）
    :param p1: 第一个点 (x, y)
    :param p2: 顶点 (x, y)
    :param p3: 第三个点 (x, y)
    :return: 角度（度数）
    """
    # 计算向量
    v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
    v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

    # 计算向量的模长
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)

    # 避免除零错误
    if norm_v1 == 0 or norm_v2 == 0:
        return 0

    # 计算余弦值
    cos_angle = np.dot(v1, v2) / (norm_v1 * norm_v2)

    # 限制余弦值在[-1, 1]范围内，避免数值误差
    cos_angle = np.clip(cos_angle, -1, 1)

    # 计算角度（弧度转度数）
    angle_rad = np.arccos(cos_angle)
    angle_deg = np.degrees(angle_rad)

    return angle_deg

def calculate_quadrilateral_angles(points):
    """
    计算四边形的四个内角
    :param points: 四个点的列表，每个点为(x, y)元组，按顺序排列
    :return: 四个角度的列表（度数）
    """
    if len(points) != 4:
        raise ValueError("必须提供4个点")

    angles = []
    n = len(points)

    # 计算每个顶点的内角
    for i in range(n):
        # 获取当前顶点和相邻的两个顶点
        prev_point = points[(i - 1) % n]  # 前一个点
        current_point = points[i]         # 当前点
        next_point = points[(i + 1) % n]  # 下一个点

        # 计算角度
        angle = calculate_angle(prev_point, current_point, next_point)
        angles.append(angle)

    return angles

def print_quadrilateral_info(points):
    """
    打印四边形的详细信息
    :param points: 四个点的列表
    """
    print("四边形顶点坐标:")
    for i, point in enumerate(points):
        print(f"  点{i+1}: ({point[0]:.2f}, {point[1]:.2f})")

    angles = calculate_quadrilateral_angles(points)

    print("\n四边形内角:")
    for i, angle in enumerate(angles):
        print(f"  角{i+1}: {angle:.2f}°")

    print(f"\n内角和: {sum(angles):.2f}° (理论值应为360°)")

    return angles

if __name__ == "__main__":


   

    # 如果需要运行NV12转换代码，请取消下面的注释
    width = 1280
    height = 720
    srcdir = r"D:\imgs"
    dstdir = r"\imgs\rgb"
    os.makedirs(dstdir, exist_ok=True)
    for fn in os.listdir(srcdir):
        nv12_to_bgr(os.path.join(srcdir,fn), os.path.join(dstdir,fn.replace(".bin",".bmp")), width, height)