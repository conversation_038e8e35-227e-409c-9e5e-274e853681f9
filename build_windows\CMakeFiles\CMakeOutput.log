The system is: Windows - 10.0.22631 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.10.1+2fd48ab73
版权所有(C) Microsoft Corporation。保留所有权利。

生成启动时间为 2025/9/5 15:16:06。
节点 1 上的项目“D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdC\CompilerIdC.vcxproj”(默认目标)。
PrepareForBuild:
  正在创建目录“Debug\”。
  正在创建目录“Debug\CompilerIdC.tlog\”。
InitializeBuildStatus:
  正在创建“Debug\CompilerIdC.tlog\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
VcpkgTripletSelection:
  Using triplet "x64-windows" from "D:\Program\Project\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\bin\HostX64\x64\CL.exe /c /I"D:\Program\Project\vcpkg\installed\x64-windows\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc142.pdb" /external:env:EXTERNAL_INCLUDE /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
  CMakeCCompilerId.c
Link:
  d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\Program\Project\vcpkg\installed\x64-windows\lib" /LIBPATH:"D:\Program\Project\vcpkg\installed\x64-windows\lib\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\Program\Project\vcpkg\installed\x64-windows\lib\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdC.lib" /MACHINE:X64 Debug\CMakeCCompilerId.obj
  CompilerIdC.vcxproj -> D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdC\CompilerIdC.exe
AppLocalFromInstalled:
  pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\Program\Project\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdC\CompilerIdC.exe" "D:\Program\Project\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdC.tlog\CompilerIdC.write.1u.tlog" "Debug\vcpkg.applocal.log"
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
  命令“pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\Program\Project\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdC\CompilerIdC.exe" "D:\Program\Project\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdC.tlog\CompilerIdC.write.1u.tlog" "Debug\vcpkg.applocal.log"”已退出，代码为 9009。
  "C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\Program\Project\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdC\CompilerIdC.exe" "D:\Program\Project\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdC.tlog\CompilerIdC.write.1u.tlog" "Debug\vcpkg.applocal.log"
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_C_COMPILER=d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  正在删除文件“Debug\CompilerIdC.tlog\unsuccessfulbuild”。
  正在对“Debug\CompilerIdC.tlog\CompilerIdC.lastbuildstate”执行 Touch 任务。
已完成生成项目“D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdC\CompilerIdC.vcxproj”(默认目标)的操作。

已成功生成。
    0 个警告
    0 个错误

已用时间 00:00:04.03


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"

The C compiler identification is MSVC, found in "D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/3.25.3/CompilerIdC/CompilerIdC.exe"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.10.1+2fd48ab73
版权所有(C) Microsoft Corporation。保留所有权利。

生成启动时间为 2025/9/5 15:16:10。
节点 1 上的项目“D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdCXX\CompilerIdCXX.vcxproj”(默认目标)。
PrepareForBuild:
  正在创建目录“Debug\”。
  正在创建目录“Debug\CompilerIdCXX.tlog\”。
InitializeBuildStatus:
  正在创建“Debug\CompilerIdCXX.tlog\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
VcpkgTripletSelection:
  Using triplet "x64-windows" from "D:\Program\Project\vcpkg\installed\x64-windows\"
  Using normalized configuration "Release"
ClCompile:
  d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\bin\HostX64\x64\CL.exe /c /I"D:\Program\Project\vcpkg\installed\x64-windows\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc142.pdb" /external:env:EXTERNAL_INCLUDE /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
  CMakeCXXCompilerId.cpp
Link:
  d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\Program\Project\vcpkg\installed\x64-windows\lib" /LIBPATH:"D:\Program\Project\vcpkg\installed\x64-windows\lib\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\Program\Project\vcpkg\installed\x64-windows\lib\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdCXX.lib" /MACHINE:X64 Debug\CMakeCXXCompilerId.obj
  CompilerIdCXX.vcxproj -> D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdCXX\CompilerIdCXX.exe
AppLocalFromInstalled:
  pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\Program\Project\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdCXX\CompilerIdCXX.exe" "D:\Program\Project\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdCXX.tlog\CompilerIdCXX.write.1u.tlog" "Debug\vcpkg.applocal.log"
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
  命令“pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\Program\Project\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdCXX\CompilerIdCXX.exe" "D:\Program\Project\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdCXX.tlog\CompilerIdCXX.write.1u.tlog" "Debug\vcpkg.applocal.log"”已退出，代码为 9009。
  "C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\Program\Project\vcpkg\scripts\buildsystems\msbuild\applocal.ps1" "D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdCXX\CompilerIdCXX.exe" "D:\Program\Project\vcpkg\installed\x64-windows\bin" "Debug\CompilerIdCXX.tlog\CompilerIdCXX.write.1u.tlog" "Debug\vcpkg.applocal.log"
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_CXX_COMPILER=d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  正在删除文件“Debug\CompilerIdCXX.tlog\unsuccessfulbuild”。
  正在对“Debug\CompilerIdCXX.tlog\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
已完成生成项目“D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CompilerIdCXX\CompilerIdCXX.vcxproj”(默认目标)的操作。

已成功生成。
    0 个警告
    0 个错误

已用时间 00:00:04.02


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"

The CXX compiler identification is MSVC, found in "D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/3.25.3/CompilerIdCXX/CompilerIdCXX.exe"

Detecting C compiler ABI info compiled with the following output:
Change Dir: D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/CMakeScratch/TryCompile-25b24i

Run Build Command(s):d:/Program Files (x86)/Microsoft Visual Studio/2019/Community/MSBuild/Current/Bin/MSBuild.exe cmTC_fe3a6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:m && 用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.10.1+2fd48ab73

版权所有(C) Microsoft Corporation。保留所有权利。



  用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30037 版

  版权所有(C) Microsoft Corporation。保留所有权利。

  CMakeCCompilerABI.c

  cl /c /I"D:\Program\Project\vcpkg\installed\x64-windows\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\"Debug\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fe3a6.dir\Debug\\" /Fd"cmTC_fe3a6.dir\Debug\vc142.pdb" /external:env:EXTERNAL_INCLUDE /external:W3 /Gd /TC /errorReport:queue "D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCCompilerABI.c"

  cmTC_fe3a6.vcxproj -> D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\CMakeScratch\TryCompile-25b24i\Debug\cmTC_fe3a6.exe

  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序

  或批处理文件。




Detecting CXX compiler ABI info compiled with the following output:
Change Dir: D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/CMakeScratch/TryCompile-iglnyf

Run Build Command(s):d:/Program Files (x86)/Microsoft Visual Studio/2019/Community/MSBuild/Current/Bin/MSBuild.exe cmTC_631f7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:m && 用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.10.1+2fd48ab73

版权所有(C) Microsoft Corporation。保留所有权利。



  用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30037 版

  版权所有(C) Microsoft Corporation。保留所有权利。

  CMakeCXXCompilerABI.cpp

  cl /c /I"D:\Program\Project\vcpkg\installed\x64-windows\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\"Debug\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_631f7.dir\Debug\\" /Fd"cmTC_631f7.dir\Debug\vc142.pdb" /external:env:EXTERNAL_INCLUDE /external:W3 /Gd /TP /errorReport:queue "D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXCompilerABI.cpp"

  cmTC_631f7.vcxproj -> D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\CMakeScratch\TryCompile-iglnyf\Debug\cmTC_631f7.exe

  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序

  或批处理文件。




