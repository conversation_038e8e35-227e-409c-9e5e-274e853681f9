#include "hough_line_detector.h"
#include <opencv2/opencv.hpp>
#include <iostream>

int main() {
    // 读取图像
    cv::Mat image = cv::imread("test_image.jpg");
    if (image.empty()) {
        std::cerr << "无法读取图像文件" << std::endl;
        return -1;
    }

    // 创建Hough直线检测器
    // 参数: threshold=100, rho=1.0, theta=CV_PI/180, method=SOBEL_GRADIENT
    HoughLineDetector detector(100, 1.0, CV_PI/180, SOBEL_GRADIENT);

    // 检测直线和矩形
    bool success = detector.detectLines(image);
    
    if (success) {
        std::cout << "成功检测到矩形!" << std::endl;
        
        // 获取检测结果
        const DetectedRectangle& rect = detector.getRectangle();
        
        // 打印四个顶点坐标
        std::cout << "矩形顶点坐标:" << std::endl;
        std::cout << "左上角: (" << rect.topLeft.x << ", " << rect.topLeft.y << ")" << std::endl;
        std::cout << "右上角: (" << rect.topRight.x << ", " << rect.topRight.y << ")" << std::endl;
        std::cout << "左下角: (" << rect.bottomLeft.x << ", " << rect.bottomLeft.y << ")" << std::endl;
        std::cout << "右下角: (" << rect.bottomRight.x << ", " << rect.bottomRight.y << ")" << std::endl;
        
        // 绘制检测结果
        cv::Mat result = detector.drawResults(image, true, true);
        
        // 显示结果
        cv::imshow("原图", image);
        cv::imshow("检测结果", result);
        cv::waitKey(0);
        
        // 保存结果
        cv::imwrite("detection_result.jpg", result);
        
    } else {
        std::cout << "检测失败!" << std::endl;
    }

    return 0;
}

/*
使用说明:
1. 编译: g++ -o hough_detector example_usage.cpp hough_line_detector.cpp `pkg-config --cflags --libs opencv4`
2. 运行: ./hough_detector

参数调整建议:
- threshold: 控制检测敏感度，值越小检测到的直线越多
- rho: 距离分辨率，通常设为1.0
- theta: 角度分辨率，通常设为CV_PI/180（1度）
- EdgeDetectionMethod: 
  * SOBEL_GRADIENT: 适用于大多数情况
  * LAPLACIAN: 对噪声敏感，适用于清晰图像
  * MORPHOLOGY_GRADIENT: 适用于二值图像

使用不同边缘检测方法的示例:
HoughLineDetector detector1(100, 1.0, CV_PI/180, SOBEL_GRADIENT);
HoughLineDetector detector2(100, 1.0, CV_PI/180, LAPLACIAN);
HoughLineDetector detector3(100, 1.0, CV_PI/180, MORPHOLOGY_GRADIENT);
*/
