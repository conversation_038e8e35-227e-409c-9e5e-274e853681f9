#ifndef TRAPEZOID_CORNER_DETECTOR_HPP
#define TRAPEZOID_CORNER_DETECTOR_HPP

#include <opencv2/opencv.hpp>
#include <vector>

/**
 * @brief Structure to hold the four corner points of a 2x2 trapezoid grid
 */
struct TrapezoidGridCorners {
    cv::Point2f topLeft;      // Top-left corner of top-left trapezoid
    cv::Point2f topRight;     // Top-right corner of top-right trapezoid
    cv::Point2f bottomLeft;   // Bottom-left corner of bottom-left trapezoid
    cv::Point2f bottomRight;  // Bottom-right corner of bottom-right trapezoid
    bool isValid;             // Flag indicating if corners are valid
    
    TrapezoidGridCorners() : isValid(false) {}
};

/**
 * @brief Structure to represent a detected trapezoid
 */
struct DetectedTrapezoid {
    std::vector<cv::Point2f> corners;  // Four corner points of the trapezoid
    cv::Point2f center;                // Center point of the trapezoid
    double area;                       // Area of the trapezoid
    
    DetectedTrapezoid() : area(0.0) {}
};

/**
 * @brief Class for detecting 4 white trapezoids arranged in 2x2 grid and extracting corner points
 */
class TrapezoidCornerDetector {
public:
    /**
     * @brief Constructor with configurable parameters
     * @param minArea Minimum area for trapezoid detection
     * @param maxArea Maximum area for trapezoid detection
     * @param approxEpsilon Epsilon for contour approximation
     * @param cannyLow Lower threshold for Canny edge detection
     * @param cannyHigh Upper threshold for Canny edge detection
     */
    TrapezoidCornerDetector(double minArea = 100.0, 
                           double maxArea = 50000.0,
                           double approxEpsilon = 0.02,
                           double cannyLow = 50.0,
                           double cannyHigh = 150.0);
    
    /**
     * @brief Destructor
     */
    ~TrapezoidCornerDetector();
    
    /**
     * @brief Detect trapezoids in the input image and extract corner points
     * @param image Input image containing 4 white trapezoids in 2x2 arrangement
     * @return True if exactly 4 trapezoids were detected and corners extracted successfully
     */
    bool detectTrapezoidCorners(const cv::Mat& image);
    
    /**
     * @brief Get the detected corner points of the 2x2 trapezoid grid
     * @return TrapezoidGridCorners structure containing the four corner points
     */
    const TrapezoidGridCorners& getGridCorners() const { return gridCorners_; }
    
    /**
     * @brief Get all detected trapezoids
     * @return Vector of detected trapezoids
     */
    const std::vector<DetectedTrapezoid>& getDetectedTrapezoids() const { return detectedTrapezoids_; }
    
    /**
     * @brief Draw detection results on the image
     * @param image Input image to draw on
     * @param drawTrapezoids Whether to draw detected trapezoid contours
     * @param drawCorners Whether to draw the four corner points
     * @return Image with detection results drawn
     */
    cv::Mat drawResults(const cv::Mat& image, bool drawTrapezoids = true, bool drawCorners = true) const;
    
    /**
     * @brief Check if detection was successful
     * @return True if valid corner points were detected
     */
    bool isDetectionValid() const { return gridCorners_.isValid; }
    
    /**
     * @brief Reset detection results
     */
    void reset();

private:
    // Detection parameters
    double minArea_;        // Minimum area for trapezoid detection
    double maxArea_;        // Maximum area for trapezoid detection
    double approxEpsilon_;  // Epsilon for contour approximation
    double cannyLow_;       // Lower threshold for Canny edge detection
    double cannyHigh_;      // Upper threshold for Canny edge detection
    
    // Detection results
    TrapezoidGridCorners gridCorners_;                    // Final corner points of the 2x2 grid
    std::vector<DetectedTrapezoid> detectedTrapezoids_;   // All detected trapezoids
    
    /**
     * @brief Preprocess the input image for contour detection
     * @param image Input image
     * @return Preprocessed binary image
     */
    cv::Mat preprocessImage(const cv::Mat& image);
    
    /**
     * @brief Detect trapezoid contours in the binary image
     * @param binary Binary input image
     * @return Vector of detected trapezoid contours
     */
    std::vector<std::vector<cv::Point>> detectTrapezoidContours(const cv::Mat& binary);
    
    /**
     * @brief Filter and validate trapezoid contours
     * @param contours Input contours
     * @return Vector of valid trapezoid contours
     */
    std::vector<std::vector<cv::Point>> filterTrapezoidContours(const std::vector<std::vector<cv::Point>>& contours);
    
    /**
     * @brief Convert contours to DetectedTrapezoid objects
     * @param contours Valid trapezoid contours
     */
    void convertContoursToTrapezoids(const std::vector<std::vector<cv::Point>>& contours);
    
    /**
     * @brief Arrange detected trapezoids in 2x2 grid and extract corner points
     * @return True if arrangement was successful
     */
    bool arrangeTrapezoidsAndExtractCorners();
    
    /**
     * @brief Check if a contour approximates to a trapezoid (4 vertices)
     * @param contour Input contour
     * @return True if contour is trapezoid-like
     */
    bool isTrapezoidContour(const std::vector<cv::Point>& contour);
    
    /**
     * @brief Calculate the center point of a contour
     * @param contour Input contour
     * @return Center point
     */
    cv::Point2f calculateCenter(const std::vector<cv::Point>& contour);
    
    /**
     * @brief Sort trapezoids by their position to arrange in 2x2 grid
     * @param trapezoids Input trapezoids
     * @return Sorted trapezoids in order: [top-left, top-right, bottom-left, bottom-right]
     */
    std::vector<DetectedTrapezoid> sortTrapezoidsByPosition(const std::vector<DetectedTrapezoid>& trapezoids);
    
    /**
     * @brief Find the specific corner point of a trapezoid based on position
     * @param trapezoid Input trapezoid
     * @param cornerType Type of corner to find (0=top-left, 1=top-right, 2=bottom-left, 3=bottom-right)
     * @return The requested corner point
     */
    cv::Point2f findTrapezoidCorner(const DetectedTrapezoid& trapezoid, int cornerType);
};

#endif // TRAPEZOID_CORNER_DETECTOR_HPP
