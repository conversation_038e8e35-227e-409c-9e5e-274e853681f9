#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件D列时间格式统一脚本（使用openpyxl直接操作）
将D列的时间格式统一改为：yyyy.mm.dd
"""

import os
import sys
from datetime import datetime
import re

def format_date_column_openpyxl(file_path, output_path=None):
    """
    使用openpyxl直接操作Excel文件，将D列的时间格式统一改为yyyy.mm.dd格式
    
    Args:
        file_path (str): 输入Excel文件路径
        output_path (str, optional): 输出文件路径，如果为None则覆盖原文件
    
    Returns:
        bool: 处理是否成功
    """
    try:
        import openpyxl
        from openpyxl.utils import get_column_letter
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 '{file_path}' 不存在")
            return False
        
        print(f"正在读取文件: {file_path}")
        
        # 打开Excel文件
        workbook = openpyxl.load_workbook(file_path)
        
        # 获取活动工作表
        worksheet = workbook.active
        print(f"工作表名称: {worksheet.title}")
        
        # 获取数据范围
        max_row = worksheet.max_row
        max_col = worksheet.max_column
        print(f"数据范围: {max_row} 行 x {max_col} 列")
        
        # 检查是否有D列（第4列）
        if max_col < 4:
            print("错误：文件中没有D列")
            return False
        
        # D列是第4列
        d_column = 4
        d_column_letter = get_column_letter(d_column)
        print(f"处理列: {d_column_letter}列")
        
        # 显示D列前几行的原始数据
        print(f"\nD列前5行原始数据:")
        for row in range(1, min(6, max_row + 1)):
            cell_value = worksheet.cell(row=row, column=d_column).value
            print(f"第{row}行: {cell_value} (类型: {type(cell_value).__name__})")
        
        # 处理D列的时间格式
        def format_date_value(value):
            """
            将各种时间格式转换为yyyy.mm.dd格式
            """
            if value is None or value == '':
                return value  # 保持空值不变
            
            try:
                # 如果已经是datetime类型
                if isinstance(value, datetime):
                    return value.strftime('%Y.%m.%d')
                
                # 如果是字符串，尝试解析
                if isinstance(value, str):
                    # 移除可能的空格
                    value = value.strip()
                    if value == '':
                        return value
                    
                    # 如果已经是目标格式，直接返回
                    if re.match(r'^\d{4}\.\d{2}\.\d{2}$', value):
                        return value
                    
                    # 尝试多种日期格式解析
                    date_formats = [
                        '%Y-%m-%d',      # 2023-01-01
                        '%Y/%m/%d',      # 2023/01/01
                        '%Y.%m.%d',      # 2023.01.01 (已经是目标格式)
                        '%m/%d/%Y',      # 01/01/2023
                        '%m-%d-%Y',      # 01-01-2023
                        '%d/%m/%Y',      # 01/01/2023 (欧洲格式)
                        '%d-%m-%Y',      # 01-01-2023 (欧洲格式)
                        '%Y%m%d',        # 20230101
                        '%m/%d/%y',      # 01/01/23
                        '%m-%d-%y',      # 01-01-23
                        '%d/%m/%y',      # 01/01/23 (欧洲格式)
                        '%d-%m-%y',      # 01-01-23 (欧洲格式)
                        '%Y-%m-%d %H:%M:%S',  # 2023-01-01 12:00:00
                        '%Y/%m/%d %H:%M:%S',  # 2023/01/01 12:00:00
                    ]
                    
                    for fmt in date_formats:
                        try:
                            parsed_date = datetime.strptime(value, fmt)
                            return parsed_date.strftime('%Y.%m.%d')
                        except ValueError:
                            continue
                    
                    print(f"警告：无法解析日期格式: '{value}'，保持原值")
                    return value
                
                # 如果是数字（可能是Excel的日期序列号）
                if isinstance(value, (int, float)):
                    try:
                        # Excel日期序列号转换（从1900年1月1日开始计算）
                        # Excel的日期序列号：1900年1月1日 = 1
                        if value > 0:
                            # 计算从1900年1月1日开始的天数
                            base_date = datetime(1900, 1, 1)
                            # Excel有一个闰年bug，需要减去2天
                            days_to_add = int(value) - 2
                            if days_to_add >= 0:
                                target_date = base_date.replace(year=1900, month=1, day=1)
                                from datetime import timedelta
                                target_date = target_date + timedelta(days=days_to_add)
                                return target_date.strftime('%Y.%m.%d')
                    except:
                        pass
                    
                    print(f"警告：无法解析数字日期: {value}，保持原值")
                    return value
                
                # 其他情况保持原值
                return value
                
            except Exception as e:
                print(f"警告：处理日期值 '{value}' 时出错: {e}，保持原值")
                return value
        
        # 应用格式转换
        print(f"\n正在转换D列时间格式...")
        changed_count = 0
        
        for row in range(1, max_row + 1):
            cell = worksheet.cell(row=row, column=d_column)
            original_value = cell.value
            
            if original_value is not None:
                new_value = format_date_value(original_value)
                if str(original_value) != str(new_value):
                    cell.value = new_value
                    changed_count += 1
                    if changed_count <= 5:  # 显示前5个转换示例
                        print(f"第{row}行: '{original_value}' -> '{new_value}'")
        
        # 显示转换后的前几行数据
        print(f"\nD列前5行转换后数据:")
        for row in range(1, min(6, max_row + 1)):
            cell_value = worksheet.cell(row=row, column=d_column).value
            print(f"第{row}行: {cell_value}")
        
        # 统计转换情况
        print(f"\n转换统计:")
        print(f"总行数: {max_row}")
        print(f"已转换行数: {changed_count}")
        print(f"未变更行数: {max_row - changed_count}")
        
        # 确定输出文件路径
        if output_path is None:
            output_path = file_path
        
        # 保存文件
        print(f"\n正在保存文件到: {output_path}")
        
        # 创建输出目录（如果不存在）
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 保存Excel文件
        workbook.save(output_path)
        workbook.close()
        
        print(f"文件保存成功！")
        print(f"D列时间格式已统一为 yyyy.mm.dd 格式")
        
        return True
        
    except ImportError:
        print("错误：需要安装openpyxl库")
        print("请运行: pip install openpyxl")
        return False
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 目标文件路径
    file_path = r"D:\大图.xlsx"
    
    print("Excel文件D列时间格式统一工具 (openpyxl版本)")
    print("=" * 50)
    print(f"目标文件: {file_path}")
    print(f"目标格式: yyyy.mm.dd")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 '{file_path}' 不存在")
        print("请确认文件路径是否正确")
        return
    
    # 询问是否创建备份
    backup_choice = input("\n是否创建备份文件？(y/n，默认为y): ").strip().lower()
    if backup_choice != 'n':
        backup_path = file_path.replace('.xlsx', '_backup.xlsx')
        try:
            import shutil
            shutil.copy2(file_path, backup_path)
            print(f"备份文件已创建: {backup_path}")
        except Exception as e:
            print(f"创建备份文件失败: {e}")
            return
    
    # 执行格式转换
    success = format_date_column_openpyxl(file_path)
    
    if success:
        print("\n" + "=" * 50)
        print("处理完成！D列时间格式已统一为 yyyy.mm.dd")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("处理失败，请检查错误信息")
        print("=" * 50)

if __name__ == "__main__":
    main()
