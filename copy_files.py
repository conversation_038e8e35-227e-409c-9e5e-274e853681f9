import os
import shutil

def copy_files():
    # 定义路径
    input_file = r"D:\ret.txt"
    images_dir = r"D:\Download\imgs"
    json_dir = r"D:\Download\json_labels"
    output_dir = r"D:\Download\imgs\sub"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取输入文件
    with open(input_file, 'r') as f:
        filenames = [line.strip() for line in f.readlines() if line.strip()]
    
    # 拷贝文件
    for filename in filenames:
        # 拷贝jpg文件
        jpg_src = os.path.join(images_dir, filename)
        jpg_dst = os.path.join(output_dir, filename)
        try:
            shutil.copy2(jpg_src, jpg_dst)
            print(f"Copied {jpg_src} to {jpg_dst}")
        except FileNotFoundError:
            print(f"Warning: {jpg_src} not found")
        
        # 拷贝json文件
        json_filename = os.path.splitext(filename)[0] + '.json'
        json_src = os.path.join(json_dir, json_filename)
        json_dst = os.path.join(output_dir, json_filename)
        try:
            shutil.copy2(json_src, json_dst)
            print(f"Copied {json_src} to {json_dst}")
        except FileNotFoundError:
            print(f"Warning: {json_src} not found")

if __name__ == "__main__":
    copy_files()
