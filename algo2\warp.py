import cv2, numpy as np

PROJ_W, PROJ_H = 1024, 768
RATIO = 4/3   # 目标矩形比例

def load_calib():
    c = np.load('system_calib.npz')
    return c['Kp'], c['Kc'], c['R'], c['t']

# 用摄像头画面估算投影四边形 4 角点
def find_proj_quad(cam, Kc, R, t):
    """
    简化：在摄像头画面里手动点 4 个角点（或自动检测白色区域轮廓）
    返回 4 个角点 uv_cam
    """
    ret, frame = cam.read()
    if not ret:
        return None
    print('【提示】用鼠标点 4 个角点（顺时针 from TL），q 结束')
    pts_cam = []
    def click(event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN and len(pts_cam) < 4:
            pts_cam.append([x, y])
            cv2.circle(frame, (x, y), 3, (0, 0, 255), -1)
            cv2.imshow('frame', frame)
    cv2.namedWindow('frame')
    cv2.setMouseCallback('frame', click)
    cv2.imshow('frame', frame)
    while len(pts_cam) < 4:
        if cv2.waitKey(1) == ord('q'):
            break
    cv2.destroyWindow('frame')
    if len(pts_cam) != 4:
        return None
    return np.array(pts_cam, dtype=np.float32)

# 三角化得到 3D 点（摄像头坐标系）
def triangulate(uv_cam, Kc, R, t):
    # 本例简化：假设平面 Z=0，用 homography 反推 3D
    # 更严谨可用 cv2.triangulatePoints
    Hcam, _ = cv2.findHomography(uv_cam,
                                   np.array([[0, 0], [1, 0], [1, 1], [0, 1]], np.float32))
    # 归一化平面 -> 实际 3D
    pts_3d = []
    for u, v in uv_cam:
        x = (u - Kc[0, 2]) / Kc[0, 0]
        y = (v - Kc[1, 2]) / Kc[1, 1]
        pts_3d.append([x, y, 0])
    return np.array(pts_3d, np.float32)

# 计算最大内接 4:3 矩形
def max_inscribed_rect(quad_2d, ratio):
    """
    quad_2d: 4x2  ndarray
    返回 4 个角点（顺序同 quad）
    这里用暴力离散版，够用
    """
    # 简化：直接缩放 quad 中心矩形
    cx, cy = quad_2d.mean(axis=0)
    w = min(cv2.norm(quad_2d[1]-quad_2d[0]),
            cv2.norm(quad_2d[2]-quad_2d[3]))
    h = w / ratio
    rect = np.array([[cx-w/2, cy-h/2],
                     [cx+w/2, cy-h/2],
                     [cx+w/2, cy+h/2],
                     [cx-w/2, cy+h/2]], np.float32)
    return rect

# 主接口：输入摄像头，输出预变形图像
def compute_warp(cam, src_img):
    Kp, Kc, R, t = load_calib()
    uv_cam = find_proj_quad(cam, Kc, R, t)
    if uv_cam is None:
        return None
    pts_3d = triangulate(uv_cam, Kc, R, t)

    # 投影到投影仪图像坐标
    pts_proj, _ = cv2.projectPoints(pts_3d, R, t, Kp, None)
    pts_proj = pts_proj.reshape(-1, 2).astype(np.float32)

    # 计算最大内接矩形（在 3D 平面单位框内）
    rect_3d = max_inscribed_rect(np.array([[0, 0], [1, 0], [1, 1], [0, 1]], np.float32), RATIO)
    rect_proj, _ = cv2.projectPoints(rect_3d, R, t, Kp, None)
    rect_proj = rect_proj.reshape(-1, 2).astype(np.float32)

    # 计算 homography: 原始图像 -> 投影矩形
    h, w = src_img.shape[:2]
    src_pts = np.array([[0, 0], [w, 0], [w, h], [0, h]], np.float32)
    H, _ = cv2.findHomography(src_pts, rect_proj)
    warped = cv2.warpPerspective(src_img, H, (PROJ_W, PROJ_H))
    return warped