import os
import shutil

def copy_png_files():
    # 定义路径
    input_dir = r"D:\Download\gesture_1014\sub"
    source_dir = r"D:\Download\gesture_1014"
    output_dir = r"D:\Download\gesture_1014\png_sub"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取输入目录中的文件列表(不含扩展名)
    filenames = [os.path.splitext(f)[0] for f in os.listdir(input_dir) 
                if os.path.isfile(os.path.join(input_dir, f))]
    
    # 拷贝文件
    for filename in filenames:
        # 构建png文件名
        png_filename = filename + '.png'
        
        # 拷贝png文件
        png_src = os.path.join(source_dir, png_filename)
        png_dst = os.path.join(output_dir, png_filename)
        try:
            shutil.copy2(png_src, png_dst)
            print(f"Copied {png_src} to {png_dst}")
        except FileNotFoundError:
            print(f"Warning: {png_src} not found")

if __name__ == "__main__":
    copy_png_files()
