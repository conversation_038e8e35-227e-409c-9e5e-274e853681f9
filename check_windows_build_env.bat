@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Windows Build Environment Check
echo ========================================
echo.

REM Check for CMake
echo Checking CMake...
cmake --version >nul 2>&1
if !errorlevel! neq 0 (
    echo [ERROR] CMake not found! Please install CMake.
    echo Download from: https://cmake.org/download/
) else (
    echo [OK] CMake found
    cmake --version | findstr /C:"cmake version"
)

echo.
echo Checking Visual Studio...
REM Check for Visual Studio
where cl >nul 2>&1
if !errorlevel! neq 0 (
    echo [WARNING] Visual Studio compiler not found in PATH
    echo Searching for Visual Studio installations...
    
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        echo [FOUND] Visual Studio 2022 Community
    )
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        echo [FOUND] Visual Studio 2022 Professional
    )
    if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        echo [FOUND] Visual Studio 2019 Community
    )
    if exist "C:\Program Files\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        echo [FOUND] Visual Studio 2019 Professional
    )
    
    echo.
    echo Please run this script from Visual Studio Developer Command Prompt
    echo or run vcvars64.bat first
) else (
    echo [OK] Visual Studio compiler found
    cl 2>&1 | findstr /C:"Microsoft"
)

echo.
echo Checking dependencies...

REM Check OpenCV
if exist "third_party\prebuilt\windows\opencv4.5.1" (
    echo [OK] OpenCV 4.5.1 Windows libraries found
) else (
    echo [WARNING] OpenCV 4.5.1 Windows libraries not found
    echo Expected location: third_party\prebuilt\windows\opencv4.5.1\
    if exist "third_party\source\opencv-4.5.1" (
        echo [INFO] OpenCV source found - will be built automatically during build process
        echo Source location: third_party\source\opencv-4.5.1\
    ) else (
        echo [WARNING] OpenCV source also not found at third_party\source\opencv-4.5.1\
    )
)

REM Check glog
if exist "third_party\prebuilt\windows\glog" (
    echo [OK] glog Windows libraries found
) else (
    echo [WARNING] glog Windows libraries not found
    echo Expected location: third_party\prebuilt\windows\glog\
    if exist "third_party\source\glog-0.3.5" (
        echo [INFO] glog source found - will be built automatically during build process
        echo Source location: third_party\source\glog-0.3.5\
    ) else (
        echo [WARNING] glog source also not found at third_party\source\glog-0.3.5\
    )
)

REM Check TNN
if exist "third_party\prebuilt\windows\tnn" (
    echo [OK] TNN Windows libraries found
) else (
    echo [WARNING] TNN Windows libraries not found
    echo Expected location: third_party\prebuilt\windows\tnn\
)

REM Check NCNN
if exist "third_party\prebuilt\windows\ncnn_20220216" (
    echo [OK] NCNN Windows libraries found
) else (
    echo [WARNING] NCNN Windows libraries not found
    echo Expected location: third_party\prebuilt\windows\ncnn_20220216\
)

REM Check Eigen
if exist "third_party\prebuilt\windows\Eigen" (
    echo [OK] Eigen Windows libraries found
) else (
    echo [WARNING] Eigen Windows libraries not found
    echo Expected location: third_party\prebuilt\windows\Eigen\
)

echo.
echo Checking source files...
if exist "test\simulate_center_stage.cpp" (
    echo [OK] simulate_center_stage.cpp found
) else (
    echo [ERROR] simulate_center_stage.cpp not found!
)

if exist "lib\include\center_stage\czcv_center_stage.h" (
    echo [OK] czcv_center_stage.h found
) else (
    echo [WARNING] czcv_center_stage.h not found at expected location
)

if exist "lib\include\center_stage\center_stage_api.h" (
    echo [OK] center_stage_api.h found
) else (
    echo [WARNING] center_stage_api.h not found at expected location
)

echo.
echo ========================================
echo Environment Check Summary
echo ========================================
echo.
echo If all checks passed, you can proceed with building.
echo Use one of the following build scripts:
echo   - build_windows_simulate_center_stage.bat (recommended)
echo   - build_windows_simple.bat (fallback)
echo   - Build-SimulateCenterStage.ps1 (PowerShell, advanced)
echo.
pause
