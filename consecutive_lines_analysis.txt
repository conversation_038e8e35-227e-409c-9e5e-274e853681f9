连续行号分析结果
分析文件: gesture_analysis_results.txt
找到 9 个连续值个数大于5的序列
================================================================================

序列 1:
  起始行号: 340121
  结束行号: 340132
  连续个数: 12
  完整序列: 340121-340132
  详细序列: [340121, 340122, 340123, 340124, 340125, 340126, 340127, 340128, 340129, 340130, 340131, 340132]
--------------------------------------------------------------------------------

序列 2:
  起始行号: 359029
  结束行号: 359034
  连续个数: 6
  完整序列: 359029-359034
  详细序列: [359029, 359030, 359031, 359032, 359033, 359034]
--------------------------------------------------------------------------------

序列 3:
  起始行号: 529167
  结束行号: 529173
  连续个数: 7
  完整序列: 529167-529173
  详细序列: [529167, 529168, 529169, 529170, 529171, 529172, 529173]
--------------------------------------------------------------------------------

序列 4:
  起始行号: 595333
  结束行号: 595339
  连续个数: 7
  完整序列: 595333-595339
  详细序列: [595333, 595334, 595335, 595336, 595337, 595338, 595339]
--------------------------------------------------------------------------------

序列 5:
  起始行号: 595550
  结束行号: 595555
  连续个数: 6
  完整序列: 595550-595555
  详细序列: [595550, 595551, 595552, 595553, 595554, 595555]
--------------------------------------------------------------------------------

序列 6:
  起始行号: 604785
  结束行号: 604806
  连续个数: 22
  完整序列: 604785-604806
  详细序列: [604785, 604786, 604787, 604788, 604789, 604790, 604791, 604792, 604793, 604794, 604795, 604796, 604797, 604798, 604799, 604800, 604801, 604802, 604803, 604804, 604805, 604806]
--------------------------------------------------------------------------------

序列 7:
  起始行号: 623724
  结束行号: 623733
  连续个数: 10
  完整序列: 623724-623733
  详细序列: [623724, 623725, 623726, 623727, 623728, 623729, 623730, 623731, 623732, 623733]
--------------------------------------------------------------------------------

序列 8:
  起始行号: 642593
  结束行号: 642601
  连续个数: 9
  完整序列: 642593-642601
  详细序列: [642593, 642594, 642595, 642596, 642597, 642598, 642599, 642600, 642601]
--------------------------------------------------------------------------------

序列 9:
  起始行号: 642605
  结束行号: 642610
  连续个数: 6
  完整序列: 642605-642610
  详细序列: [642605, 642606, 642607, 642608, 642609, 642610]
--------------------------------------------------------------------------------

