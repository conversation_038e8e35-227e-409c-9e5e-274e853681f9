#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四边形角度计算使用示例
"""

from quadrilateral_angles import calculate_quadrilateral_angles, print_quadrilateral_info, validate_quadrilateral


def main():
    """主函数 - 演示如何使用四边形角度计算功能"""

    print("=== 四边形角度计算使用示例 ===\n")

    # 示例1: 计算一个梯形的角度
    print("示例1: 梯形")
    trapezoid_points = [(0, 0), (4, 0), (3, 2), (1, 2)]

    # 验证四边形
    is_valid, message = validate_quadrilateral(trapezoid_points)
    print(f"验证结果: {message}")

    if is_valid:
        # 计算角度
        angles = calculate_quadrilateral_angles(trapezoid_points)
        print(f"四个角度: {[f'{angle:.2f}°' for angle in angles]}")
        print(f"角度和: {sum(angles):.2f}°")
        print()

        # 详细信息
        print_quadrilateral_info(trapezoid_points)

    print("\n" + "="*50)

    # 示例2: 计算一个菱形的角度
    print("\n示例2: 菱形")
    rhombus_points = [(0, 0), (2, 1), (0, 2), (-2, 1)]
    print_quadrilateral_info(rhombus_points)

    print("\n" + "="*50)

    # 示例3: 用户自定义点
    print("\n示例3: 自定义四个点")
    # 你可以修改这里的坐标来计算你想要的四边形
    custom_points = [
        (10, 10),   # 点1
        (50, 15),   # 点2
        (45, 60),   # 点3
        (5, 55)     # 点4
    ]

    print("自定义四边形:")
    print_quadrilateral_info(custom_points)

    print("\n" + "="*50)

    # 示例4: 只获取角度数值
    print("\n示例4: 只获取角度数值（不打印详细信息）")
    points = [(0, 0), (3, 0), (4, 3), (1, 3)]
    angles = calculate_quadrilateral_angles(points)

    print("四个顶点:", points)
    print("对应角度:", [f"{angle:.2f}°" for angle in angles])

    # 找出最大角和最小角
    max_angle = max(angles)
    min_angle = min(angles)
    max_index = angles.index(max_angle) + 1
    min_index = angles.index(min_angle) + 1

    print(f"最大角: {max_angle:.2f}° (第{max_index}个角)")
    print(f"最小角: {min_angle:.2f}° (第{min_index}个角)")

    # 判断是否为矩形（所有角都是90度）
    is_rectangle = all(abs(angle - 90) < 0.01 for angle in angles)
    print(f"是否为矩形: {'是' if is_rectangle else '否'}")


if __name__ == "__main__":
    main()

    print("\n" + "="*50)
    print("提示:")
    print("1. 修改 custom_points 中的坐标来计算你想要的四边形")
    print("2. 点的顺序很重要，应该按照四边形的顶点顶序排列")
    print("3. 可以是顺时针或逆时针，但要保持一致")
    print("4. 四边形的内角和应该等于360度")
    print("="*50)