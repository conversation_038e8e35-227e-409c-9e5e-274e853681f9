The target system is: Android - 1 - aarch64
The host system is: Windows - 10.0.22631 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;
Id flags: -c;--target=aarch64-none-linux-android24 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "D:/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/3.25.3/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;;
Id flags: -c;--target=aarch64-none-linux-android24 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "D:/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/3.25.3/CompilerIdCXX/CMakeCXXCompilerId.o"

Detecting C compiler ABI info compiled with the following output:
Change Dir: D:/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-lul7tl

Run Build Command(s):C:/msys64/usr/bin/make.exe -f Makefile cmTC_3d5ac/fast && /usr/bin/make  -f CMakeFiles/cmTC_3d5ac.dir/build.make CMakeFiles/cmTC_3d5ac.dir/build
make[1]: Entering directory '/d/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-lul7tl'
Building C object CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o

/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot   -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat   -fPIE   -v -MD -MT CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o -c "/D/Program Files/CMake/share/cmake-3.25/Modules/CMakeCCompilerABI.c"
Android (8481493, based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)

Target: aarch64-none-linux-android24

Thread model: posix

InstalledDir: D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\bin

Found candidate GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android\4.9.x

Selected GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x

Candidate multilib: .;@m64

Selected multilib: .;@m64

Found CUDA installation: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.0, version 11.0

 (in-process)

 "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe" -cc1 -triple aarch64-none-linux-android24 -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9" -dependency-file CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-isystem "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\include" -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdebug-compilation-dir "D:\\Program\\Project\\project\\czcv_camera_new\\build_android_arm64-v8a\\CMakeFiles\\CMakeScratch\\TryCompile-lul7tl" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -o CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o -x c "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeCCompilerABI.c"

clang -cc1 version 12.0.9 based upon LLVM 12.0.9git default target x86_64-w64-windows-gnu

ignoring nonexistent directory "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include

 D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include

 D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android

 D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

Linking C executable cmTC_3d5ac

/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat   -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -Wl,--gc-sections  -v "CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o"  -o cmTC_3d5ac 
Android (8481493, based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)

Target: aarch64-none-linux-android24

Thread model: posix

InstalledDir: D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\bin

Found candidate GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android\4.9.x

Selected GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x

Candidate multilib: .;@m64

Selected multilib: .;@m64

Found CUDA installation: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.0, version 11.0

 "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\ld" --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_3d5ac "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o" "-LD:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64" -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24 -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o"

make[1]: Leaving directory '/d/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-lul7tl'



Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/include]
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/include] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/include]
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/include;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: D:/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-lul7tl]
  ignore line: []
  ignore line: [Run Build Command(s):C:/msys64/usr/bin/make.exe -f Makefile cmTC_3d5ac/fast && /usr/bin/make  -f CMakeFiles/cmTC_3d5ac.dir/build.make CMakeFiles/cmTC_3d5ac.dir/build]
  ignore line: [make[1]: Entering directory '/d/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-lul7tl']
  ignore line: [Building C object CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o]
  ignore line: [/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot   -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat   -fPIE   -v -MD -MT CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o -c "/D/Program Files/CMake/share/cmake-3.25/Modules/CMakeCCompilerABI.c"]
  ignore line: [Android (8481493  based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android24]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\bin]
  ignore line: [Found candidate GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android\4.9.x]
  ignore line: [Selected GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [Found CUDA installation: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.0  version 11.0]
  ignore line: [ (in-process)]
  ignore line: [ "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe" -cc1 -triple aarch64-none-linux-android24 -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9" -dependency-file CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-isystem "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\include" -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdebug-compilation-dir "D:\\Program\\Project\\project\\czcv_camera_new\\build_android_arm64-v8a\\CMakeFiles\\CMakeScratch\\TryCompile-lul7tl" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -o CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o -x c "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeCCompilerABI.c"]
  ignore line: [clang -cc1 version 12.0.9 based upon LLVM 12.0.9git default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
  ignore line: [ D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include]
  ignore line: [ D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [Linking C executable cmTC_3d5ac]
  ignore line: [/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat   -Wl --build-id=sha1 -Wl --no-rosegment -Wl --fatal-warnings -Wl --no-undefined -Qunused-arguments -Wl --gc-sections  -v "CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o"  -o cmTC_3d5ac ]
  ignore line: [Android (8481493  based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android24]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\bin]
  ignore line: [Found candidate GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android\4.9.x]
  ignore line: [Selected GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [Found CUDA installation: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.0  version 11.0]
  link line: [ "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\ld" --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_3d5ac "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o" "-LD:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64" -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24 -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o"]
    arg [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\ld] ==> ignore
    arg [--sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_3d5ac] ==> ignore
    arg [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o] ==> obj [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o]
    arg [-LD:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64] ==> dir [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64]
    arg [-LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x] ==> dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
    arg [-LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24] ==> dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24]
    arg [-LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_3d5ac.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a] ==> lib [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a] ==> lib [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o] ==> obj [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o]
  remove lib [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
  remove lib [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
  collapse obj [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/crtbegin_dynamic.o]
  collapse obj [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/crtend_android.o]
  collapse library dir [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/lib/linux/aarch64]
  collapse library dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  collapse library dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24]
  collapse library dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/crtbegin_dynamic.o;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/crtend_android.o]
  implicit dirs: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/lib/linux/aarch64;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: D:/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-28f6cb

Run Build Command(s):C:/msys64/usr/bin/make.exe -f Makefile cmTC_32a44/fast && /usr/bin/make  -f CMakeFiles/cmTC_32a44.dir/build.make CMakeFiles/cmTC_32a44.dir/build
make[1]: Entering directory '/d/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-28f6cb'
Building CXX object CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o

/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot   -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat    -fPIE   -v -MD -MT CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -c "/D/Program Files/CMake/share/cmake-3.25/Modules/CMakeCXXCompilerABI.cpp"
Android (8481493, based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)

Target: aarch64-none-linux-android24

Thread model: posix

InstalledDir: D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\bin

Found candidate GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android\4.9.x

Selected GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x

Candidate multilib: .;@m64

Selected multilib: .;@m64

Found CUDA installation: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.0, version 11.0

 (in-process)

 "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe" -cc1 -triple aarch64-none-linux-android24 -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9" -dependency-file CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1 -internal-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-isystem "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\include" -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdeprecated-macro -fdebug-compilation-dir "D:\\Program\\Project\\project\\czcv_camera_new\\build_android_arm64-v8a\\CMakeFiles\\CMakeScratch\\TryCompile-28f6cb" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -o CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -x c++ "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeCXXCompilerABI.cpp"

clang -cc1 version 12.0.9 based upon LLVM 12.0.9git default target x86_64-w64-windows-gnu

ignoring nonexistent directory "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1

 D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include

 D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include

 D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android

 D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

Linking CXX executable cmTC_32a44

/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat    -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -Wl,--gc-sections  -v "CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o"  -o cmTC_32a44 
Android (8481493, based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)

Target: aarch64-none-linux-android24

Thread model: posix

InstalledDir: D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\bin

Found candidate GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android\4.9.x

Selected GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x

Candidate multilib: .;@m64

Selected multilib: .;@m64

Found CUDA installation: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.0, version 11.0

 "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\ld" --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_32a44 "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o" "-LD:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64" -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24 -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lm "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o"

make[1]: Leaving directory '/d/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-28f6cb'



Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/include]
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/include] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/include]
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/include;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: D:/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-28f6cb]
  ignore line: []
  ignore line: [Run Build Command(s):C:/msys64/usr/bin/make.exe -f Makefile cmTC_32a44/fast && /usr/bin/make  -f CMakeFiles/cmTC_32a44.dir/build.make CMakeFiles/cmTC_32a44.dir/build]
  ignore line: [make[1]: Entering directory '/d/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles/CMakeScratch/TryCompile-28f6cb']
  ignore line: [Building CXX object CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot   -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat    -fPIE   -v -MD -MT CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -c "/D/Program Files/CMake/share/cmake-3.25/Modules/CMakeCXXCompilerABI.cpp"]
  ignore line: [Android (8481493  based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android24]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\bin]
  ignore line: [Found candidate GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android\4.9.x]
  ignore line: [Selected GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [Found CUDA installation: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.0  version 11.0]
  ignore line: [ (in-process)]
  ignore line: [ "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe" -cc1 -triple aarch64-none-linux-android24 -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9" -dependency-file CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1 -internal-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-isystem "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\include" -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdeprecated-macro -fdebug-compilation-dir "D:\\Program\\Project\\project\\czcv_camera_new\\build_android_arm64-v8a\\CMakeFiles\\CMakeScratch\\TryCompile-28f6cb" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -o CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -x c++ "D:/Program Files/CMake/share/cmake-3.25/Modules/CMakeCXXCompilerABI.cpp"]
  ignore line: [clang -cc1 version 12.0.9 based upon LLVM 12.0.9git default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  ignore line: [ D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
  ignore line: [ D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\12.0.9\include]
  ignore line: [ D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [Linking CXX executable cmTC_32a44]
  ignore line: [/D/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe --target=aarch64-none-linux-android24 --gcc-toolchain=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat    -Wl --build-id=sha1 -Wl --no-rosegment -Wl --fatal-warnings -Wl --no-undefined -Qunused-arguments -Wl --gc-sections  -v "CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o"  -o cmTC_32a44 ]
  ignore line: [Android (8481493  based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android24]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:\Package\android-ndk-r23c\toolchains\llvm\prebuilt\windows-x86_64\bin]
  ignore line: [Found candidate GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android\4.9.x]
  ignore line: [Selected GCC installation: D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [Found CUDA installation: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.0  version 11.0]
  link line: [ "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\ld" --sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_32a44 "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o" "-LD:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64" -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24 -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android -LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lm "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl "D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o"]
    arg [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\ld] ==> ignore
    arg [--sysroot=D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_32a44] ==> ignore
    arg [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o] ==> obj [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o]
    arg [-LD:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64] ==> dir [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64]
    arg [-LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x] ==> dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
    arg [-LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24] ==> dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24]
    arg [-LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-LD:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_32a44.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lc++] ==> lib [c++]
    arg [-lm] ==> lib [m]
    arg [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a] ==> lib [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a] ==> lib [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o] ==> obj [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o]
  remove lib [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
  remove lib [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
  collapse obj [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtbegin_dynamic.o] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/crtbegin_dynamic.o]
  collapse obj [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24\\crtend_android.o] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/crtend_android.o]
  collapse library dir [D:\\Package\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\lib64\\clang\\12.0.9\\lib\\linux\\aarch64] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/lib/linux/aarch64]
  collapse library dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  collapse library dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24]
  collapse library dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [c++;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/crtbegin_dynamic.o;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/crtend_android.o]
  implicit dirs: [D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.9/lib/linux/aarch64;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android;D:/Package/android-ndk-r23c/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


