#!/usr/bin/env python3
"""
测试相机标定代码的脚本
检查修改后的代码是否能正确计算内参和外参
"""

import sys
import os
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_camera_calibration():
    """
    测试相机标定功能
    """
    print("开始测试相机标定代码...")
    
    try:
        # 导入修改后的相机标定模块
        import camera_calibration
        print("✓ 成功导入相机标定模块")
        
        # 检查是否生成了参数文件
        params_file = "./camera_params.yml"
        if os.path.exists(params_file):
            print("✓ 找到相机参数文件")
            
            # 读取并验证参数文件内容
            import yaml
            with open(params_file, 'r') as f:
                params = yaml.safe_load(f)
            
            required_keys = ['camera_matrix', 'dist_coeffs', 'image_size', 
                           'rotation_vectors', 'translation_vectors', 'num_images']
            
            missing_keys = []
            for key in required_keys:
                if key not in params:
                    missing_keys.append(key)
            
            if missing_keys:
                print(f"✗ 参数文件缺少以下键: {missing_keys}")
                return False
            else:
                print("✓ 参数文件包含所有必需的键")
                
            # 验证数据类型和结构
            print(f"✓ 内参矩阵形状: {np.array(params['camera_matrix']).shape}")
            print(f"✓ 畸变系数形状: {np.array(params['dist_coeffs']).shape}")
            print(f"✓ 图像尺寸: {params['image_size']}")
            print(f"✓ 标定图像数量: {params['num_images']}")
            print(f"✓ 旋转向量数量: {len(params['rotation_vectors'])}")
            print(f"✓ 平移向量数量: {len(params['translation_vectors'])}")
            
            # 验证外参数据
            if len(params['rotation_vectors']) == params['num_images'] and \
               len(params['translation_vectors']) == params['num_images']:
                print("✓ 外参数量与图像数量匹配")
            else:
                print("✗ 外参数量与图像数量不匹配")
                return False
                
            return True
        else:
            print("✗ 未找到相机参数文件")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        return False

def print_extrinsic_summary():
    """
    打印外参摘要信息
    """
    try:
        import yaml
        with open("./camera_params.yml", 'r') as f:
            params = yaml.safe_load(f)
        
        print("\n" + "="*50)
        print("相机外参摘要")
        print("="*50)
        
        for i, (rvec, tvec) in enumerate(zip(params['rotation_vectors'], params['translation_vectors'])):
            rvec = np.array(rvec)
            tvec = np.array(tvec)
            
            print(f"\n图像 {i+1}:")
            print(f"  旋转向量: [{rvec[0]:.4f}, {rvec[1]:.4f}, {rvec[2]:.4f}]")
            print(f"  平移向量: [{tvec[0]:.2f}, {tvec[1]:.2f}, {tvec[2]:.2f}] mm")
            
            # 计算旋转角度（弧度转度）
            rotation_angle = np.linalg.norm(rvec) * 180 / np.pi
            print(f"  旋转角度: {rotation_angle:.2f}°")
            
    except Exception as e:
        print(f"打印外参摘要时出现错误: {e}")

if __name__ == "__main__":
    success = test_camera_calibration()
    
    if success:
        print("\n🎉 相机标定测试通过！")
        print_extrinsic_summary()
    else:
        print("\n❌ 相机标定测试失败！")
        
    print("\n测试完成。")
