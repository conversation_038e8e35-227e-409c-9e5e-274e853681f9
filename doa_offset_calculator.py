import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.ndimage import gaussian_filter1d
from collections import defaultdict
import pandas as pd

def parse_doa_file(filename):
    """
    解析DOA文件，返回按角度分组的数据
    """
    data = defaultdict(list)
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(', ')
                if len(parts) == 3:
                    angle = float(parts[0])  # 角度a
                    fitted_value = float(parts[1])  # 拟合值b
                    actual_value = float(parts[2])  # 实际值c
                    
                    data[angle].append({
                        'fitted': fitted_value,
                        'actual': actual_value
                    })
    
    return data

def find_distribution_peak(values, bins=50, smooth_sigma=1.0):
    """
    统计值的分布，平滑后找到峰值
    """
    if len(values) < 3:
        return np.mean(values)

    # 创建直方图
    hist, bin_edges = np.histogram(values, bins=bins)

    # 计算bin中心点
    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

    # 对直方图进行高斯平滑
    smoothed_hist = gaussian_filter1d(hist.astype(float), sigma=smooth_sigma)

    # 找到峰值位置
    peak_idx = np.argmax(smoothed_hist)
    peak_value = bin_centers[peak_idx]

    return peak_value, hist, bin_centers, smoothed_hist

def calculate_offset_for_angle(angle_data, bins=50, smooth_sigma=1.0):
    """
    计算单个角度的偏移量 - 使用分布峰值方法
    """
    if not angle_data:
        return None, None

    # 提取实际值c和拟合值b
    actual_values = [item['actual'] for item in angle_data]
    fitted_values = [item['fitted'] for item in angle_data]

    # 拟合值（假设所有拟合值相同，取第一个）
    fitted_value = fitted_values[0]

    # 找到实际值分布的峰值
    peak_value, hist, bin_centers, smoothed_hist = find_distribution_peak(
        actual_values, bins=bins, smooth_sigma=smooth_sigma
    )

    # 计算偏移量：峰值与拟合值的差值
    offset = peak_value - fitted_value

    # 返回偏移量和分布信息
    distribution_info = {
        'peak_value': peak_value,
        'fitted_value': fitted_value,
        'hist': hist,
        'bin_centers': bin_centers,
        'smoothed_hist': smoothed_hist,
        'actual_values': actual_values
    }

    return offset, distribution_info

def smooth_offsets(angles, offsets, window_length=5):
    """
    对偏移量进行平滑处理
    """
    if len(offsets) < window_length:
        window_length = len(offsets) if len(offsets) % 2 == 1 else len(offsets) - 1
    
    if window_length < 3:
        return offsets
    
    # 使用Savitzky-Golay滤波器进行平滑
    smoothed = signal.savgol_filter(offsets, window_length, 2)
    return smoothed

def calculate_doa_offsets(filename, bins=50, smooth_sigma=1.0, smooth_window=5):
    """
    主函数：计算DOA偏移量 - 使用分布峰值方法
    """
    print(f"正在解析文件: {filename}")

    # 解析文件
    data = parse_doa_file(filename)

    print(f"找到 {len(data)} 个不同的角度")

    # 计算每个角度的偏移量
    results = []
    distribution_data = {}

    for angle in sorted(data.keys()):
        offset, dist_info = calculate_offset_for_angle(data[angle], bins, smooth_sigma)
        if offset is not None:
            results.append({
                'angle': angle,
                'offset': offset,
                'sample_count': len(data[angle]),
                'peak_value': dist_info['peak_value'],
                'fitted_value': dist_info['fitted_value']
            })
            distribution_data[angle] = dist_info
            print(f"角度 {angle}°: 峰值 = {dist_info['peak_value']:.3f}, 拟合值 = {dist_info['fitted_value']:.3f}, 偏移量 = {offset:.6f}, 样本数 = {len(data[angle])}")

    if not results:
        print("没有找到有效的数据")
        return None, None

    # 转换为DataFrame便于处理
    df = pd.DataFrame(results)

    # 对偏移量进行平滑处理
    if len(df) > 1:
        smoothed_offsets = smooth_offsets(df['angle'].values, df['offset'].values, smooth_window)
        df['smoothed_offset'] = smoothed_offsets
    else:
        df['smoothed_offset'] = df['offset']

    return df, distribution_data

def plot_results(df, distribution_data=None, output_path=None):
    """
    绘制结果图表 - 包含分布峰值分析
    """
    if df is None or df.empty:
        print("没有数据可以绘制")
        return

    # 创建更大的图表来容纳更多信息
    fig = plt.figure(figsize=(16, 12))

    # 转换为numpy数组以避免pandas兼容性问题
    angles = df['angle'].values
    offsets = df['offset'].values
    smoothed_offsets = df['smoothed_offset'].values
    sample_counts = df['sample_count'].values
    peak_values = df['peak_value'].values
    fitted_values = df['fitted_value'].values

    # 1. 偏移量分析
    plt.subplot(3, 2, 1)
    plt.plot(angles, offsets, 'o-', label='原始偏移量', alpha=0.7)
    plt.plot(angles, smoothed_offsets, 's-', label='平滑后偏移量', linewidth=2)
    plt.xlabel('Angle (degrees)')
    plt.ylabel('Offset')
    plt.title('DOA Offset Analysis')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. 峰值vs拟合值对比
    plt.subplot(3, 2, 2)
    plt.plot(angles, peak_values, 'o-', label='Peak Values', color='red', alpha=0.7)
    plt.plot(angles, fitted_values, 's-', label='Fitted Values', color='blue', alpha=0.7)
    plt.xlabel('Angle (degrees)')
    plt.ylabel('Value')
    plt.title('Peak vs Fitted Values')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. 样本数量分布
    plt.subplot(3, 2, 3)
    plt.bar(angles, sample_counts, alpha=0.7, color='orange')
    plt.xlabel('Angle (degrees)')
    plt.ylabel('Sample Count')
    plt.title('Sample Count Distribution')
    plt.grid(True, alpha=0.3)

    # 4. 偏移量分布直方图
    plt.subplot(3, 2, 4)
    plt.hist(offsets, bins=20, alpha=0.7, color='green', edgecolor='black')
    plt.xlabel('Offset Value')
    plt.ylabel('Frequency')
    plt.title('Offset Distribution')
    plt.grid(True, alpha=0.3)

    # 5-6. 显示几个典型角度的分布
    if distribution_data:
        # 选择样本数最多的两个角度进行详细展示
        sorted_angles = sorted(distribution_data.keys(),
                             key=lambda x: len(distribution_data[x]['actual_values']),
                             reverse=True)

        for i, angle in enumerate(sorted_angles[:2]):
            plt.subplot(3, 2, 5 + i)
            dist_info = distribution_data[angle]

            # 绘制原始直方图和平滑后的分布
            plt.bar(dist_info['bin_centers'], dist_info['hist'],
                   alpha=0.6, width=np.diff(dist_info['bin_centers'])[0]*0.8,
                   label='Original Distribution')

            # 绘制平滑后的分布
            plt.plot(dist_info['bin_centers'], dist_info['smoothed_hist'],
                    'r-', linewidth=2, label='Smoothed Distribution')

            # 标记峰值和拟合值
            plt.axvline(dist_info['peak_value'], color='red', linestyle='--',
                       label=f'Peak: {dist_info["peak_value"]:.1f}')
            plt.axvline(dist_info['fitted_value'], color='blue', linestyle='--',
                       label=f'Fitted: {dist_info["fitted_value"]:.1f}')

            plt.xlabel('Value')
            plt.ylabel('Frequency')
            plt.title(f'Distribution for {angle}° (n={len(dist_info["actual_values"])})')
            plt.legend(fontsize=8)
            plt.grid(True, alpha=0.3)

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {output_path}")

    plt.show()

def save_results(df, output_path):
    """
    保存结果到CSV文件
    """
    if df is None or df.empty:
        print("没有数据可以保存")
        return
    
    df.to_csv(output_path, index=False, encoding='utf-8-sig')
    print(f"结果已保存到: {output_path}")

if __name__ == "__main__":
    # 配置参数
    input_file = "czcv_debug_doa.txt"
    bins = 50  # 直方图分箱数量
    smooth_sigma = 1.0  # 高斯平滑参数
    smooth_window = 5  # 偏移量平滑窗口大小

    # 计算偏移量
    results_df, distribution_data = calculate_doa_offsets(input_file, bins, smooth_sigma, smooth_window)

    if results_df is not None:
        # 显示统计信息
        print("\n=== 统计信息 ===")
        print(f"总角度数: {len(results_df)}")
        print(f"偏移量范围: {results_df['offset'].min():.6f} ~ {results_df['offset'].max():.6f}")
        print(f"平滑后偏移量范围: {results_df['smoothed_offset'].min():.6f} ~ {results_df['smoothed_offset'].max():.6f}")
        print(f"平均偏移量: {results_df['offset'].mean():.6f}")
        print(f"平滑后平均偏移量: {results_df['smoothed_offset'].mean():.6f}")

        # 保存结果
        save_results(results_df, "doa_offset_results_peak.csv")

        # 绘制图表
        plot_results(results_df, distribution_data, "doa_offset_analysis_peak.png")

        # 显示前几行结果
        print("\n=== 前10个角度的结果 ===")
        print(results_df.head(10).to_string(index=False))
    else:
        print("处理失败，请检查输入文件格式")
