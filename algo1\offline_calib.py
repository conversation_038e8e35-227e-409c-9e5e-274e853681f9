import cv2, numpy as np, glob

# ---------- 用户参数 ----------
PROJ_CROSS = r"D:\Dataset\keystone\20251027-113839.png"          # 十字校准图
PATTERN_CHESS = (8, 5)            # 角点网格
N_POSE = 3                       # 姿态数
# ------------------------------

objp = np.ones((np.prod(PATTERN_CHESS), 3), np.float32)
objp[:, :2] = np.mgrid[0:PATTERN_CHESS[0], 0:PATTERN_CHESS[1]].T.reshape(-1, 2) * 10  # 单位棋盘格
print("objp", objp)
img_pts = []   # 相机像素
obj_pts = []   # 3-D 坐标（以格为单元）
proj_pts = []  # 投影仪像素（固定）

# 1. 投影仪角点 = 固定值（同一幅图）
cross_gray = cv2.imread(PROJ_CROSS, 0)
ret, corners_p = cv2.findChessboardCorners(cross_gray, PATTERN_CHESS)

ret = True
corners_p = np.array([[203, 107], [420, 108], [636, 107], [852, 108], [1070, 108], [1285, 109], [1500, 108], [1715, 109], [203, 324], [419, 326], [635, 324], [850, 324], [1067, 324], [1285, 326], [1500, 324], [1715, 326], [203, 540], [419, 541], [635, 539], [852, 541], [1068, 540], [1285, 541], [1500, 539], [1716, 541], [204, 757], [421, 754], [636, 754], [852, 756], [1068, 756], [1284, 757], [1499, 756], [1716, 754], [204, 973], [419, 972], [635, 971], [852, 972], [1068, 972], [1284, 973], [1499, 973], [1717, 972]], dtype=np.float32)

corners_p = np.reshape(corners_p, (-1, 1, 2))
#print("corners_p", corners_p)
cv2.cornerSubPix(cross_gray, corners_p, (11, 11), (-1, -1),
                 (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
proj_pts = corners_p.reshape(-1, 2)  # M×2

# 2. 遍历相机拍到的图
images = sorted(glob.glob('calib_*.jpg'))
images_dir = r"D:\Dataset\keystone"
import os
images = ["cam_1080p_10.jpg", "cam_1080p_11.jpg", "cam_1080p_9.jpg"]
assert len(images) >= N_POSE
corners_c_dict = {"cam_1080p_10.jpg": np.array([[427, 243], [481, 243], [537, 241], [593, 241], [649, 241], [705, 240], [763, 239], [819, 238], [423, 294], [478, 293], [535, 293], [593, 293], [650, 293], [708, 293], [764, 292], [823, 292], [418, 348], [476, 348], [532, 347], [590, 346], [649, 346], [709, 347], [768, 346], [827, 345], [413, 403], [472, 403], [530, 403], [590, 403], [650, 403], [709, 403], [770, 403], [831, 403], [409, 461], [468, 462], [528, 461], [589, 461], [650, 462], [711, 463], [773, 461], [835, 462]],dtype=np.float32),
                  "cam_1080p_11.jpg": np.array([[415, 242], [470, 243], [525, 241], [580, 241], [636, 240], [694, 240], [750, 240], [807, 240], [410, 294], [467, 293], [522, 293], [580, 293], [636, 292], [695, 293], [753, 292], [810, 291], [405, 348], [463, 348], [520, 345], [579, 346], [636, 346], [696, 346], [755, 346], [815, 345], [400, 403], [459, 403], [518, 403], [576, 403], [636, 403], [697, 403], [757, 403], [818, 403], [395, 461], [456, 461], [515, 460], [576, 461], [636, 461], [697, 462], [760, 462], [821, 462]],dtype=np.float32),
                  "cam_1080p_9.jpg" : np.array([[440, 245], [495, 241], [549, 240], [605, 241], [663, 242], [720, 242], [775, 240], [833, 240], [437, 295], [493, 294], [547, 293], [606, 294], [663, 293], [722, 294], [778, 293], [836, 293], [432, 348], [489, 348], [546, 346], [605, 348], [663, 348], [722, 347], [781, 347], [840, 346], [427, 403], [485, 404], [544, 403], [603, 404], [663, 403], [724, 405], [784, 404], [844, 404], [423, 462], [482, 462], [542, 462], [602, 463], [663, 463], [726, 463], [785, 462], [848, 463]],dtype=np.float32)}
for fname in images[:N_POSE]:
    img = cv2.imread(os.path.join(images_dir, fname))
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    ret, corners_c = cv2.findChessboardCorners(gray, PATTERN_CHESS)
    ret = True
    corners_c = corners_c_dict[fname]
    corners_c = np.reshape(corners_c, (-1, 1, 2))
    if ret:
        cv2.cornerSubPix(gray, corners_c, (11, 11), (-1, -1),
                         (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
        img_pts.append(corners_c.reshape(-1, 2))
        obj_pts.append(objp)          # 同一组 3-D 点

#print("img_pts", img_pts)


# 3. 相机标定（拿 Kc）
#Kc = np.load('Kc.npy')  
Kc = np.array([ 8.3829501611528804e+02, 0., 6.1849577402276270e+02, 0.,
       8.5114568888309248e+02, 4.0359283026107522e+02, 0., 0., 1. ],dtype=np.float32) # 用户已知
Kc = np.reshape(Kc, (3, 3))
np.save('Kc.npy', Kc)
dist = np.zeros(5)       # 假设无畸变或已去畸变

# 4. 求每幅图的外参 → 得到 **相机坐标系下** 的 3-D 点
X_cam = []               # 累积所有 3-D 点
for i, (ipt, opt) in enumerate(zip(img_pts, obj_pts)):
    _, rvec, tvec = cv2.solvePnP(opt, ipt, Kc, dist)
    R, _ = cv2.Rodrigues(rvec)
    # 把棋盘格点转到相机坐标系
    X = (R @ opt.T + tvec).T
    print("X", X)
    X_cam.append(X)


X_cam = np.vstack(X_cam)        # N×3
proj_pts = np.vstack([proj_pts]*len(X_cam))  # 对应投影仪像素

# 5. DLT 求 G
def DLT(X, x):
    """ X: N×3, x: N×2 → 返回 3×4 G """
    A = []
    for i in range(X.shape[0]):
        Xi = np.append(X[i], 1)          # 4×1
        ui, vi = x[i]
        A.append(np.concatenate([vi*Xi, -ui*Xi] ))       # 3×4 堆叠
    A = np.array(A)
    U, D, V = np.linalg.svd(A)
    D = np.reshape(V[-1], (2, 4))
    print(U.shape, D.shape, V.shape)

    A = []
    b = []
    for i in range(X.shape[0]):
        Xi = np.append(X[i], 1)          # 4×1
        ui, vi = x[i]
        A.append(ui*Xi )  
        b.append(np.sum(D[0] * Xi))

    A = np.array(A)
    b = np.array(b)
    print(A.shape, b.shape)
    G = np.linalg.lstsq(A, b, rcond=None)[0]
   
    G = np.concatenate([D, G[None, :]])
    return G

G = DLT(X_cam, proj_pts)
print('G (camera → projector):\n', G)
np.save('G.npy', G)