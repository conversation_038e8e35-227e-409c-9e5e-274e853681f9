import cv2 as cv
import numpy as np
import os
import argparse

def load_remap_maps(xmap_path, ymap_path, width=3840, height=2160):
    """加载xmap和ymap二进制文件"""
    # 读取xmap
    with open(xmap_path, 'rb') as f:
        xmap_data = np.frombuffer(f.read(), dtype=np.float32)
    xmap = xmap_data.reshape((height, width))
    
    # 读取ymap
    with open(ymap_path, 'rb') as f:
        ymap_data = np.frombuffer(f.read(), dtype=np.float32)
    ymap = ymap_data.reshape((height, width))
    
    return xmap, ymap

def process_images(input_dir, output_dir, xmap, ymap):
    """处理目录下的所有图像"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 支持的图像格式
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    
    # 遍历输入目录
    for filename in os.listdir(input_dir):
        if any(filename.lower().endswith(ext) for ext in image_extensions):
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename)
            
            # 读取图像
            img = cv.imread(input_path)
            if img is None:
                print(f"无法读取图像: {input_path}")
                continue
            
            # 执行remap操作
            remapped_img = cv.remap(img, xmap, ymap, cv.INTER_LINEAR, cv.BORDER_CONSTANT)
            
            # 保存结果
            cv.imwrite(output_path, remapped_img)
            print(f"处理完成: {filename}")

def compose_remap_maps(xmap1, ymap1, xmap2, ymap2):
    """
    合并两个remap映射，等效于先应用map1再应用map2
    
    Args:
        xmap1, ymap1: 第一次remap的映射
        xmap2, ymap2: 第二次remap的映射
    
    Returns:
        composed_xmap, composed_ymap: 合并后的映射
    """
    height, width = xmap2.shape
    composed_xmap = np.zeros_like(xmap2)
    composed_ymap = np.zeros_like(ymap2)
    
    for y in range(height):
        for x in range(width):
            # 第二个映射的坐标
            intermediate_x = xmap2[y, x]
            intermediate_y = ymap2[y, x]
            
            # 检查中间坐标是否在第一个映射的范围内
            if (0 <= intermediate_x < width and 0 <= intermediate_y < height):
                # 使用双线性插值从第一个映射中获取最终坐标
                ix = int(intermediate_x)
                iy = int(intermediate_y)
                fx = intermediate_x - ix
                fy = intermediate_y - iy
                
                # 边界检查
                ix1 = min(ix + 1, width - 1)
                iy1 = min(iy + 1, height - 1)
                
                # 双线性插值
                final_x = (1-fx)*(1-fy)*xmap1[iy, ix] + fx*(1-fy)*xmap1[iy, ix1] + \
                         (1-fx)*fy*xmap1[iy1, ix] + fx*fy*xmap1[iy1, ix1]
                final_y = (1-fx)*(1-fy)*ymap1[iy, ix] + fx*(1-fy)*ymap1[iy, ix1] + \
                         (1-fx)*fy*ymap1[iy1, ix] + fx*fy*ymap1[iy1, ix1]
                
                composed_xmap[y, x] = final_x
                composed_ymap[y, x] = final_y
            else:
                # 超出范围的像素设为无效值
                composed_xmap[y, x] = -1
                composed_ymap[y, x] = -1
    
    return composed_xmap, composed_ymap

def load_and_compose_maps(xmap1_path, ymap1_path, xmap2_path, ymap2_path, width=3840, height=2160):
    """加载并合并两组remap映射"""
    # 加载第一组映射
    xmap1, ymap1 = load_remap_maps(xmap1_path, ymap1_path, width, height)
    # 加载第二组映射  
    xmap2, ymap2 = load_remap_maps(xmap2_path, ymap2_path, width, height)
    
    # 合并映射
    composed_xmap, composed_ymap = compose_remap_maps(xmap1, ymap1, xmap2, ymap2)
    
    return composed_xmap, composed_ymap

def main():
    parser = argparse.ArgumentParser(description='图像remap处理工具')
    parser.add_argument('--xmap', required=True, help='xmap.bin文件路径')
    parser.add_argument('--ymap', required=True, help='ymap.bin文件路径')
    parser.add_argument('--input', required=True, help='输入图像目录')
    parser.add_argument('--output', required=True, help='输出图像目录')
    
    args = parser.parse_args()
    
    try:
        # 加载remap映射
        print("加载remap映射文件...")
        xmap, ymap = load_remap_maps(args.xmap, args.ymap)
        print(f"映射文件加载成功，分辨率: {xmap.shape[1]}x{xmap.shape[0]}")
        
        # 处理图像
        print("开始处理图像...")
        process_images(args.input, args.output, xmap, ymap)
        print("所有图像处理完成！")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    composed_xmap, composed_ymap = load_and_compose_maps(
        r'D:/Dataset/image/mapx1.bin', r'D:/Dataset/image/mapy1.bin', 
        r'D:/Dataset/image/mapx2.bin', r'D:/Dataset/image/mapy2.bin'
    )

    # 对图像进行一次remap操作，等效于两次连续remap
    img = cv.imread("D:/result.jpg")
    result = cv.remap(img, composed_xmap, composed_ymap, cv.INTER_LINEAR, cv.BORDER_CONSTANT)
    composed_xmap.tofile('D:/Dataset/image/composed_xmap.bin')
    composed_ymap.tofile('D:/Dataset/image/composed_ymap.bin')
    cv.imwrite('D:/Dataset/image/output.jpg', result)
    #main()

