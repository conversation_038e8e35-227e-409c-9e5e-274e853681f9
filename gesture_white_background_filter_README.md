# 手势检测白色背景过滤功能

## 功能描述

在 `czcv_center_stage.cpp` 的 `thread_loop_gesture` 函数中，为手势识别结果添加了基于白色背景的过滤功能。该功能会根据RGB格式的图像和对应的检测框，判断手势的背景是否接近于白色，如果是则保留该手势检测结果，否则删除。

## 修改内容

### 1. 在 thread_loop_gesture 函数中添加过滤调用

**位置**: `lib\src\center_stage\czcv_center_stage.cpp` 第4711-4717行

```cpp
_gestureRecognitiongPtr->run(tracks);

// 过滤手势检测结果：保留背景接近白色的手势
filter_gesture_by_white_background(tracks, bgr);

set_tracks_with_gesture(tracks);
gestureResults = tracks.gestureResults();
```

### 2. 添加私有成员函数声明

**位置**: `lib\src\center_stage\czcv_center_stage.cpp` 第5837-5839行

```cpp
// 手势背景白色过滤函数
void filter_gesture_by_white_background(TrackerInputOutput &tracks, const cv::Mat &bgr);
bool is_background_white(const cv::Mat &bgr, const stGestureRecResult &gesture);
```

### 3. 实现过滤函数

**位置**: `lib\src\center_stage\czcv_center_stage.cpp` 第5676-5734行

#### filter_gesture_by_white_background 函数
- 遍历所有手势检测结果
- 对每个手势调用 `is_background_white` 判断背景是否为白色
- 只保留背景为白色的手势检测结果

#### is_background_white 函数
- 从 `stGestureRecResult.rectf` 获取检测框坐标（x0, y0, x1, y1格式）
- 进行边界检查确保坐标在图像范围内
- 提取检测框区域的图像
- 计算该区域的平均颜色值
- 判断是否接近白色（默认阈值为200，每个BGR通道都需要超过此阈值）

## 技术细节

### 数据结构
- 使用 `stGestureRecResult.rectf` 字段获取检测框坐标
- `rectf` 包含 `x0, y0, x1, y1` 四个float值表示矩形框
- 输入图像为BGR格式的 `cv::Mat`

### 白色判断逻辑
- 白色阈值设置为200（可调整）
- 要求BGR三个通道的平均值都超过阈值
- 使用 `cv::mean()` 计算检测框区域的平均颜色

### 边界处理
- 对检测框坐标进行边界检查
- 确保提取的ROI区域有效（宽度和高度大于0）
- 处理空图像或非3通道图像的异常情况

## 使用说明

该功能会自动在手势识别流程中生效，无需额外配置。如需调整白色检测的敏感度，可以修改 `is_background_white` 函数中的 `whiteThreshold` 值：

- 增大阈值：更严格的白色判断，只有非常接近白色的背景才会被保留
- 减小阈值：更宽松的白色判断，浅色背景也会被认为是白色

## 注意事项

1. 该过滤功能只在手势模式开启时生效
2. 过滤是在手势识别完成后进行的，不影响原始检测性能
3. 如果所有手势都被过滤掉，`gestureResults` 将为空向量
4. 函数对输入参数进行了充分的边界检查，确保程序稳定性
