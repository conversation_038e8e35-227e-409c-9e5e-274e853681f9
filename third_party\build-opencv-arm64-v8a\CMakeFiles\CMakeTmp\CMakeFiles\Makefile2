# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = "/D/Program Files/CMake/bin/cmake.exe"

# The command to remove a file.
RM = "/D/Program Files/CMake/bin/cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/cmTC_d25e1.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/cmTC_d25e1.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/cmTC_d25e1.dir

# All Build rule for target.
CMakeFiles/cmTC_d25e1.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cmTC_d25e1.dir/build.make CMakeFiles/cmTC_d25e1.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cmTC_d25e1.dir/build.make CMakeFiles/cmTC_d25e1.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/CMakeFiles --progress-num=1,2 "Built target cmTC_d25e1"
.PHONY : CMakeFiles/cmTC_d25e1.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/cmTC_d25e1.dir/rule:
	$(CMAKE_COMMAND) -E cmake_progress_start /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/cmTC_d25e1.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /D/Program/Project/project/czcv_camera_new/third_party/build-opencv-arm64-v8a/CMakeFiles/CMakeTmp/CMakeFiles 0
.PHONY : CMakeFiles/cmTC_d25e1.dir/rule

# Convenience name for target.
cmTC_d25e1: CMakeFiles/cmTC_d25e1.dir/rule
.PHONY : cmTC_d25e1

# clean rule for target.
CMakeFiles/cmTC_d25e1.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cmTC_d25e1.dir/build.make CMakeFiles/cmTC_d25e1.dir/clean
.PHONY : CMakeFiles/cmTC_d25e1.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

