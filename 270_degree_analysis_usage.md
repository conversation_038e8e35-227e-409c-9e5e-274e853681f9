# 270°坐标值拟合分析功能使用说明

## 功能概述

在 `find_critical_values.py` 文件中新增了专门用于分析270°坐标值的函数，可以从输入文件中找到260°、270°、280°对应的坐标值，并使用多种方法拟合出270°对应的最佳坐标值。该功能能够智能处理数据较少的情况，并提供多种拟合策略。

## 新增函数

### 1. `find_and_fit_270_degree_values(filename)`

**功能**: 找到所有270°对应的坐标值，并拟合出270°对应的最佳坐标值

**参数**:
- `filename` (str): 输入文件路径

**返回值**:
- `dict`: 包含270°数据分析结果的字典，包括：
  - 基本统计信息（样本数、可用角度、范围、均值、中位数等）
  - 异常值检测结果
  - 九种拟合方法的结果：
    - 270°算术平均值（仅使用270°数据）
    - 270°中位数（仅使用270°数据，对异常值更鲁棒）
    - 多角度线性拟合（使用260°、270°、280°数据进行线性插值）
    - 多角度加权拟合（基于角度距离的智能加权平均）
    - 多项式回归拟合（使用二次多项式对多角度数据进行回归）
    - 鲁棒组合拟合（结合多种方法并使用鲁棒统计）
    - 边界中点拟合（基于相邻角度边界值的中点方法）
    - 去除异常值后的平均值（使用IQR方法）
    - KDE（核密度估计）最大密度值
  - 各方法的拟合质量指标（RMSE）
  - 推荐的最佳方法和值

### 2. `plot_270_degree_analysis(result, output_path=None)`

**功能**: 绘制270°数据分析的可视化图表

**参数**:
- `result` (dict): `find_and_fit_270_degree_values`函数的返回结果
- `output_path` (str, 可选): 图表保存路径

**图表内容**:
- 原始数据分布直方图
- 去除异常值后的数据分布
- 箱线图显示数据分布和异常值
- 不同拟合方法的RMSE比较

### 3. `save_270_degree_results(result, output_path)`

**功能**: 保存270°分析结果到CSV文件

**参数**:
- `result` (dict): `find_and_fit_270_degree_values`函数的返回结果
- `output_path` (str): 输出文件路径

**输出文件**:
- 主结果文件：包含所有分析指标的摘要
- 原始数据文件：包含所有270°的原始坐标值

## 使用方法

### 方法1: 直接调用函数

```python
from find_critical_values import find_and_fit_270_degree_values, plot_270_degree_analysis, save_270_degree_results

# 分析270°数据
result = find_and_fit_270_degree_values("your_data_file.txt")

if result is not None:
    # 保存结果
    save_270_degree_results(result, "270_analysis_results.csv")
    
    # 绘制图表
    plot_270_degree_analysis(result, "270_analysis.png")
    
    # 获取推荐的最佳坐标值
    best_value = result['recommended_value']
    method = result['recommended_method']
    rmse = result['recommended_rmse']
    
    print(f"推荐的270°最佳坐标值: {best_value:.3f}")
    print(f"使用方法: {method}")
    print(f"拟合质量(RMSE): {rmse:.3f}")
```

### 方法2: 运行主程序

直接运行 `find_critical_values.py`，程序会自动执行270°数据分析：

```bash
python find_critical_values.py
```

程序会：
1. 首先执行原有的临界值分析
2. 然后自动执行270°数据分析
3. 保存所有结果和图表

## 输入文件格式

输入文件应包含角度和坐标值的数据，格式为：
```
260.0, 148.123
260.0, 147.876
270.0, 150.123
270.0, 149.876
270.0, 151.234
280.0, 152.345
280.0, 151.987
...
```

每行包含一个角度值和对应的坐标值，用逗号分隔。程序会自动提取260°、270°、280°的数据进行分析。

## 拟合方法说明

1. **270°算术平均值**: 仅使用270°坐标值的简单平均
2. **270°中位数**: 仅使用270°数据，对异常值更鲁棒的中心趋势度量
3. **多角度线性拟合**: 使用260°、270°、280°数据进行线性插值拟合270°值，包含智能权重策略
4. **多角度加权拟合**: 基于角度与270°距离的加权平均，距离越近权重越大
5. **多项式回归拟合**: 使用二次多项式对260°、270°、280°数据进行回归拟合，能捕捉非线性关系
6. **鲁棒组合拟合**: 结合多种方法的结果，使用IQR过滤异常结果，返回鲁棒统计值
7. **边界中点拟合**: 基于相邻角度边界值的中点方法，使用260°最大值与270°最小值的中点作为260°/270°临界值，270°最大值与280°最小值的中点作为270°/280°临界值，最终270°值为两个临界值的中点
8. **过滤平均值**: 使用IQR方法去除异常值后的平均值
9. **KDE最大密度**: 使用核密度估计找到数据密度最高的点

程序会自动选择RMSE最小的方法作为推荐方法。

## 数据处理特点

- **智能数据采样**: 对原始数据取整去重后，270°数据最多选取30个样本，260°和280°数据最多选取10个样本
- **异常值处理**: 使用IQR方法自动识别和处理异常值
- **数据缺失适应**: 能够处理260°、270°、280°数据部分缺失的情况
- **单角度支持**: 即使只有270°数据也能正常工作

## 输出文件

- `270_degree_analysis_results.csv`: 分析结果摘要
- `270_degree_analysis_results_raw_data.csv`: 原始270°坐标值数据
- `270_degree_analysis.png`: 可视化分析图表

## 注意事项

1. 程序优先使用270°数据，但也能处理完全没有270°数据的情况（使用260°和280°插值）
2. 如果既没有270°数据，也缺少足够的相邻角度数据，函数会返回None并给出警告
3. 程序能够处理260°、270°、280°数据部分缺失的情况
4. 即使只有270°数据，程序也能正常工作
5. 异常值检测使用IQR方法，可能需要根据具体数据调整
6. 建议查看可视化图表来验证拟合结果的合理性
7. 多角度拟合方法在数据较少时可能与单角度方法结果相近
8. 新增的多项式回归、鲁棒组合和边界中点方法能更好地利用260°和280°数据，提高拟合精度
9. 边界中点方法特别适用于相邻角度数据有明确边界关系的场景
