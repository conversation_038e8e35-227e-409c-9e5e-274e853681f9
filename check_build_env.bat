@echo off
echo ========================================
echo Checking Build Environment for Android ARM64
echo ========================================
echo.

REM Check Android NDK
set ANDROID_NDK=D:\Package\android-ndk-r23c
echo Checking Android NDK...
if exist "%ANDROID_NDK%\build\cmake\android.toolchain.cmake" (
    echo [OK] Android NDK found at: %ANDROID_NDK%
    echo [OK] Toolchain file exists
) else (
    echo [ERROR] Android NDK not found at: %ANDROID_NDK%
    echo Please install Android NDK and update the path
    goto :error
)

REM Check CMake
echo.
echo Checking CMake...
cmake --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] CMake found
    cmake --version | findstr /C:"cmake version"
) else (
    echo [ERROR] CMake not found
    echo Please install CMake and add it to PATH
    goto :error
)

REM Check Make
echo.
echo Checking Make...
make --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Make found
    make --version | findstr /C:"GNU Make"
) else (
    echo [ERROR] Make not found
    echo Please install MSYS2 or MinGW and add make to PATH
    goto :error
)

REM Check third-party libraries
echo.
echo Checking third-party libraries...

if exist "third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\jni\include\opencv2\opencv.hpp" (
    echo [OK] OpenCV headers found
) else (
    echo [WARNING] OpenCV headers not found
    echo Run: cd third_party && build_opencv_android.bat
)

if exist "third_party\prebuilt\android_v8a\opencv4.5.1\sdk\native\staticlibs\arm64-v8a\libopencv_core.a" (
    echo [OK] OpenCV libraries found
) else (
    echo [WARNING] OpenCV libraries not found
    echo Run: cd third_party && build_opencv_android.bat
)

if exist "third_party\prebuilt\android_v8a\glog\include\glog\logging.h" (
    echo [OK] glog headers found
) else (
    echo [WARNING] glog headers not found
    echo Run: cd third_party && build_glog_android.bat
)

if exist "third_party\prebuilt\android_v8a\glog\lib\libglog.a" (
    echo [OK] glog library found
) else (
    echo [WARNING] glog library not found
    echo Run: cd third_party && build_glog_android.bat
)

if exist "third_party\prebuilt\android_v8a\TNN\include\tnn\core\tnn.h" (
    echo [OK] TNN headers found
) else (
    echo [WARNING] TNN headers not found
    echo Run: cd third_party && build_tnn_android.bat
)

if exist "third_party\prebuilt\android_v8a\TNN\libTNN.a" (
    echo [OK] TNN library found
) else (
    echo [WARNING] TNN library not found
    echo Run: cd third_party && build_tnn_android.bat
)

REM Check project files
echo.
echo Checking project files...

if exist "CMakeLists.txt" (
    echo [OK] CMakeLists.txt found
) else (
    echo [ERROR] CMakeLists.txt not found
    goto :error
)

if exist "third_party\third_party.cmake" (
    echo [OK] third_party.cmake found
) else (
    echo [ERROR] third_party.cmake not found
    goto :error
)

if exist "lib\CMakeLists.txt" (
    echo [OK] lib\CMakeLists.txt found
) else (
    echo [ERROR] lib\CMakeLists.txt not found
    goto :error
)

echo.
echo ========================================
echo Environment Check Summary
echo ========================================
echo [OK] Core build tools are ready
echo [INFO] You can now run: build_armeabi_arm64.bat
echo.
echo If any third-party libraries are missing, build them first:
echo   cd third_party
echo   build_all_android_arm64.bat
echo   cd ..
echo   build_armeabi_arm64.bat
echo ========================================
goto :end

:error
echo.
echo ========================================
echo [ERROR] Environment check failed!
echo Please fix the issues above before building.
echo ========================================
pause
exit /b 1

:end
pause
