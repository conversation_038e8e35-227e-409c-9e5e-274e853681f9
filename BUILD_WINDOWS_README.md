# Windows Build Guide for simulate_center_stage

This guide provides instructions for building the `simulate_center_stage.exe` executable on Windows.

## Prerequisites

### Required Software
1. **CMake** (version 3.1 or higher)
   - Download from: https://cmake.org/download/
   - Make sure to add CMake to your system PATH during installation

2. **Microsoft Visual Studio** (2019 or later)
   - Visual Studio Community (free): https://visualstudio.microsoft.com/downloads/
   - Must include C++ development tools
   - Supported versions: VS2019, VS2022

3. **Git** (optional, for cloning repositories)
   - Download from: https://git-scm.com/download/win

### Required Dependencies
The project requires the following third-party libraries:
- **OpenCV 4.5.1** (Computer Vision library)
- **glog** (Google Logging library)
- **TNN** (Tencent Neural Network library)
- **NCNN** (Neural Network library)
- **Eigen** (Linear algebra library)

## Build Scripts

Three build scripts are provided for different scenarios:

### 1. Main Build Script (Recommended)
**File:** `build_windows_simulate_center_stage.bat`

This is the comprehensive build script that:
- Checks for all prerequisites
- Validates dependency locations
- Provides detailed error messages
- Builds the complete project

**Usage:**
```cmd
build_windows_simulate_center_stage.bat
```

### 2. Simple Build Script
**File:** `build_windows_simple.bat`

A simplified script that attempts to build with system-installed OpenCV:
- Minimal dependency checking
- Uses system OpenCV if available
- Fallback for missing prebuilt libraries

**Usage:**
```cmd
build_windows_simple.bat
```

### 3. PowerShell Build Script (Advanced)
**File:** `Build-SimulateCenterStage.ps1`

Advanced PowerShell script with additional features:
- Automatic OpenCV download option
- Better error handling
- Configurable build options
- Clean build option

**Usage:**
```powershell
# Basic build
.\Build-SimulateCenterStage.ps1

# Build with automatic OpenCV download
.\Build-SimulateCenterStage.ps1 -DownloadOpenCV

# Clean build
.\Build-SimulateCenterStage.ps1 -Clean

# Debug build
.\Build-SimulateCenterStage.ps1 -BuildType Debug
```

## Dependency Setup

### Option 1: Automatic Build from Source (Recommended)
The build scripts will automatically detect if required prebuilt libraries are missing and build them from source if the source code is available.

**No manual intervention required** - just run any of the build scripts and they will:
1. Check for prebuilt libraries (OpenCV, glog, etc.)
2. If not found, automatically build missing libraries from source
3. Install the built libraries to the correct location
4. Continue with the main project build

**Supported automatic builds:**
- **OpenCV 4.5.1**: from `third_party\source\opencv-4.5.1\`
- **glog 0.3.5**: from `third_party\source\glog-0.3.5\`

### Option 2: Manual Setup
1. Download OpenCV 4.5.1 for Windows from: https://opencv.org/releases/
2. Extract to: `third_party\prebuilt\windows\opencv4.5.1\`
3. Ensure the following structure exists:
   ```
   third_party\prebuilt\windows\
   ├── opencv4.5.1\          # Auto-built from source if missing
   │   ├── include\
   │   ├── x64\vc16\staticlib\
   │   └── ...
   ├── glog\                  # Auto-built from source if missing
   │   ├── include\
   │   ├── lib\
   │   └── ...
   ├── tnn\
   └── Eigen\
   ```

### Option 3: Automatic Download (PowerShell only)
Use the PowerShell script with the `-DownloadOpenCV` flag:
```powershell
.\Build-SimulateCenterStage.ps1 -DownloadOpenCV
```

## Build Process

1. **Open Command Prompt or PowerShell** as Administrator (recommended)

2. **Navigate to project directory:**
   ```cmd
   cd path\to\czcv_camera_new
   ```

3. **Run the build script:**
   ```cmd
   # Using batch script
   build_windows_simulate_center_stage.bat
   
   # OR using PowerShell
   .\Build-SimulateCenterStage.ps1
   ```

4. **Wait for build completion**
   - The script will check prerequisites
   - If OpenCV prebuilt libraries are missing, it will automatically build OpenCV from source (this may take 20-60 minutes)
   - If glog prebuilt libraries are missing, it will automatically build glog from source (this may take 5-10 minutes)
   - Configure CMake for the main project
   - Build the project
   - Report results

## Output Locations

After successful build, the executable will be located at:
- `output\windows\bin\Release\simulate_center_stage.exe` (main script)
- `output\windows_simple\Release\simulate_center_stage.exe` (simple script)
- `build_windows_ps\test\Release\simulate_center_stage.exe` (PowerShell script)

## Troubleshooting

### Common Issues

1. **CMake not found**
   - Install CMake and add to PATH
   - Restart command prompt after installation

2. **Visual Studio not found**
   - Install Visual Studio with C++ development tools
   - Ensure vcvars64.bat is available

3. **OpenCV not found**
   - The script will automatically build OpenCV from source if available at `third_party\source\opencv-4.5.1\`
   - Alternatively, download OpenCV 4.5.1 for Windows and extract to correct directory structure
   - Or use PowerShell script with `-DownloadOpenCV`

4. **Build fails with linking errors**
   - Check that all required DLLs are available
   - Verify library paths in CMake configuration
   - Ensure matching architecture (x64)

5. **Missing dependencies**
   - Some third-party libraries may need to be built separately
   - Check `third_party\source\` for source code
   - Consider using vcpkg for dependency management

6. **OpenCV build takes too long**
   - Building OpenCV from source can take 20-60 minutes depending on your system
   - This is normal for the first build
   - Subsequent builds will use the cached prebuilt libraries
   - Consider downloading prebuilt libraries if build time is a concern

### Debug Build
For debugging purposes, you can build in Debug mode:
```powershell
.\Build-SimulateCenterStage.ps1 -BuildType Debug
```

### Clean Build
To start fresh:
```powershell
.\Build-SimulateCenterStage.ps1 -Clean
```

## Running the Executable

After successful build:
1. Navigate to the output directory
2. Ensure all required DLLs are in the same directory or system PATH
3. Run: `simulate_center_stage.exe`

## Additional Notes

- The project uses C++17 standard
- Static linking is preferred for distribution
- Some dependencies may require specific Visual Studio runtime versions
- For production builds, consider code signing and dependency bundling

## Support

If you encounter issues:
1. Check the build log for specific error messages
2. Verify all prerequisites are correctly installed
3. Ensure dependency directory structure matches expectations
4. Consider building dependencies from source if prebuilt versions are incompatible
