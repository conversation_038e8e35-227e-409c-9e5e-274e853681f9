import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from collections import defaultdict


plt.rcParams['font.family'] = 'SimHei'        # 或者 'Microsoft YaHei'
# 2. 解决负号“−”显示为方块的问题
plt.rcParams['axes.unicode_minus'] = False

def camera_model(theta_deg, u0, fx):
    """
    相机模型: u = u0 - fx * tan(θ)
    theta_deg: 角度（度）
    u0: 图像中心像素坐标
    fx: 水平焦距（像素单位）
    """
    theta_rad = np.deg2rad(theta_deg)
    return u0 - fx * np.tan(theta_rad)

def fit_camera_parameters(angles_deg, pixels):
    """
    拟合相机参数 u0 和 fx
    """
    # 初始参数猜测
    initial_guess = [1920, 1400]  # [u0, fx]
    
    # 使用曲线拟合
    # 步骤1: 初次拟合 - 使用普通最小二乘法进行初步拟合
    popt, pcov = curve_fit(camera_model, angles_deg, pixels, p0=initial_guess)
    
    # 迭代加权最小二乘拟合
    max_iterations = 50
    for iteration in range(max_iterations):
        # 步骤2: 计算残差与权重
        pixels_fitted_iter = camera_model(angles_deg, popt[0], popt[1])
        residuals_iter = np.abs(pixels - pixels_fitted_iter)
        
        # 根据残差大小分配权重：权重与残差的绝对值成反比
        # 添加一个小的常数避免除零错误
        epsilon = 1e-6
        weights = 1.0 / (residuals_iter + epsilon)
        
        # 归一化权重
        weights = weights / np.sum(weights) * len(weights)
        
        # 步骤3: 使用加权数据重新进行最小二乘拟合
        popt_prev = popt.copy()
        popt, pcov = curve_fit(camera_model, angles_deg, pixels, 
                              p0=popt, sigma=1.0/np.sqrt(weights), absolute_sigma=True)
        
        # 计算新参数和旧参数的error
        old_error = 0.0
        new_error = 0.0
        for i in range(len(angles_deg)):
            old_res = pixels[i] - camera_model(angles_deg[i], popt_prev[0], popt_prev[1])
            new_res = pixels[i] - camera_model(angles_deg[i], popt[0], popt[1])
            old_error += old_res * old_res
            new_error += new_res * new_res
        
        print(old_error, new_error)
        # 检查收敛性：如果参数变化很小，则停止迭代
        param_change = np.max(np.abs(popt - popt_prev) / np.abs(popt_prev))
        print(f"第{iteration+1}次迭代，参数变化：{param_change:.6f}")
        if param_change < 1e-4:
            break
    u0, fx = popt
    
    # 计算拟合误差
    pixels_fitted = camera_model(angles_deg, u0, fx)
    residuals = pixels - pixels_fitted
    rmse = np.sqrt(np.mean(residuals**2))
    
    return u0, fx, rmse, residuals

def calculate_fov(fx, image_width=3840):
    """
    计算视场角
    """
    tan_a = image_width / (2 * fx)
    a_rad = np.arctan(tan_a)
    a_deg = np.rad2deg(a_rad)
    return a_deg

# 输入数据
angles_deg = np.array([-45, -35, -25, -15, -5, 5, 15, 25, 35, 45])
# pixels = np.array([3335.8, 2918.6, 2519.2, 2341.5, 2031.4, 1788.9, 1476.0, 1257.7, 1095.5, 599.9])

pixels = np.array([3195.8, 2800.5, 2494.3, 2132.4, 1851.0, 1578.5, 1288.7, 1038.5, 714.9, 375.1])

# angles_deg = np.array([-15, -5, 5, 15])

# pixels = np.array([3062.1, 2404.6, 1581.2, 785.5])


def parse_doa_file(filename):
    """
    解析DOA文件，返回按角度分组的数据
    """
    data = defaultdict(list)
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(', ')
                if len(parts) == 3:
                    angle = float(parts[0])  # 角度a
                    fitted_value = float(parts[1])  # 拟合值b
                    actual_value = float(parts[2])  # 实际值c
                    
                    data[angle].append({
                        'fitted': fitted_value,
                        'actual': actual_value
                    })
    
    return data

filename = r"D:\czcv_debug_doa.txt"
data = parse_doa_file(filename)

# angles_deg = []
# pixels = []
# for angle in data.keys():
#     values = data[angle]
#     # 取整并去重
#     unique_actual_values = list(set(round(v['actual']) for v in values))
#     angles_deg.extend([angle - 270] * len(unique_actual_values))
#     pixels.extend([v for v in unique_actual_values])

# # 从angles_deg和pixels对应的元素中随机选取100个
# angles_deg = np.array(angles_deg)
# pixels = np.array(pixels)

# 随机选取100个样本
# if len(angles_deg) > 50:
#     indices = np.random.choice(len(angles_deg), size=50, replace=False)
#     angles_deg = angles_deg[indices]
#     pixels = pixels[indices]

# 拟合相机参数
u0, fx, rmse, residuals = fit_camera_parameters(angles_deg, pixels)
a_deg = calculate_fov(fx)

print("=== 相机参数拟合结果 ===")
print(f"图像中心 u0: {u0:.2f} pixels")
print(f"水平焦距 fx: {fx:.2f} pixels")
print(f"水平视场角: {a_deg:.2f}°")
print(f"拟合均方根误差: {rmse:.2f} pixels")

def calculate_pixel_values(u0, fx, angles_range=(-50, 51, 10)):
    """
    计算指定角度范围内的像素值
    """
    angles_to_calculate = np.arange(angles_range[0], angles_range[1], angles_range[2])
    pixel_values = camera_model(angles_to_calculate, u0, fx)
    
    results = []
    for angle, pixel in zip(angles_to_calculate, pixel_values):
        results.append((angle, pixel))
    
    return results

# 计算每10°的像素值
calculated_pixels = calculate_pixel_values(u0, fx)

print("\n=== 每10°对应的像素值 ===")
print("角度(度)    像素值")
print("-" * 20)
for angle, pixel in calculated_pixels:
    print(f"{angle:4d}°      {pixel:7.1f}")

# 验证正负50°的像素值
pixel_50 = camera_model(50, u0, fx)
pixel_minus_50 = camera_model(-50, u0, fx)

print(f"\n正负50°像素值验证:")
print(f"50°: {pixel_50:.1f}")
print(f"-50°: {pixel_minus_50:.1f}")

def plot_fitting_results(angles_deg, pixels, u0, fx):
    """
    绘制拟合结果和残差图
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # 拟合曲线图
    angles_continuous = np.linspace(-55, 55, 200)
    pixels_fitted_continuous = camera_model(angles_continuous, u0, fx)
    
    ax1.plot(angles_continuous, pixels_fitted_continuous, 'b-', label='拟合曲线', linewidth=2)
    ax1.scatter(angles_deg, pixels, color='red', s=50, label='实测数据', zorder=5)
    ax1.set_xlabel('角度 (度)')
    ax1.set_ylabel('像素值')
    ax1.set_title('角度-像素值拟合结果')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 标注关键参数
    ax1.text(0.05, 0.95, f'u0 = {u0:.2f}\nfx = {fx:.2f}\nFOV = {calculate_fov(fx):.2f}°', 
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    # 残差图
    pixels_fitted = camera_model(angles_deg, u0, fx)
    residuals = pixels - pixels_fitted
    
    ax2.scatter(angles_deg, residuals, color='green', s=50)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax2.set_xlabel('角度 (度)')
    ax2.set_ylabel('残差 (像素)')
    ax2.set_title('拟合残差')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# 绘制结果
plot_fitting_results(angles_deg, pixels, u0, fx)