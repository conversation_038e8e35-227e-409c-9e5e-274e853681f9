@echo off
setlocal enabledelayedexpansion

:menu
cls
echo ========================================
echo Windows Build Menu for simulate_center_stage
echo ========================================
echo.
echo Please select an option:
echo.
echo 1. Check build environment
echo 2. Build with main script (recommended)
echo 3. Build with simple script (fallback)
echo 4. Build with PowerShell script (advanced)
echo 5. Clean all build directories
echo 6. View build documentation
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto check_env
if "%choice%"=="2" goto build_main
if "%choice%"=="3" goto build_simple
if "%choice%"=="4" goto build_powershell
if "%choice%"=="5" goto clean_build
if "%choice%"=="6" goto view_docs
if "%choice%"=="7" goto exit
goto invalid_choice

:check_env
echo.
echo Running environment check...
call check_windows_build_env.bat
goto menu

:build_main
echo.
echo Running main build script...
call build_windows_simulate_center_stage.bat
goto menu

:build_simple
echo.
echo Running simple build script...
call build_windows_simple.bat
goto menu

:build_powershell
echo.
echo Running PowerShell build script...
powershell -ExecutionPolicy Bypass -File "Build-SimulateCenterStage.ps1"
goto menu

:clean_build
echo.
echo Cleaning build directories...
if exist "build_windows" (
    echo Removing build_windows...
    rmdir /s /q "build_windows"
)
if exist "build_windows_simple" (
    echo Removing build_windows_simple...
    rmdir /s /q "build_windows_simple"
)
if exist "build_windows_ps" (
    echo Removing build_windows_ps...
    rmdir /s /q "build_windows_ps"
)
if exist "output\windows" (
    echo Removing output\windows...
    rmdir /s /q "output\windows"
)
if exist "output\windows_simple" (
    echo Removing output\windows_simple...
    rmdir /s /q "output\windows_simple"
)
if exist "output\windows_ps" (
    echo Removing output\windows_ps...
    rmdir /s /q "output\windows_ps"
)
echo.
echo Build directories cleaned.
pause
goto menu

:view_docs
echo.
echo Opening build documentation...
if exist "BUILD_WINDOWS_README.md" (
    start notepad "BUILD_WINDOWS_README.md"
) else (
    echo BUILD_WINDOWS_README.md not found!
)
goto menu

:invalid_choice
echo.
echo Invalid choice. Please enter a number between 1 and 7.
pause
goto menu

:exit
echo.
echo Goodbye!
exit /b 0
