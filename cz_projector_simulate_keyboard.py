import cv2
import numpy as np
import math
import os

# -------- rotations --------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # R = Rz * Ry * Rx
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

# -------- intrinsics from throw ratio --------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    # throw_ratio = distance / image_width  ->  fovx = 2*atan( image_width/(2*distance) ) = 2*atan(1/(2*throw_ratio))
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

# -------- homographies --------
def homography_from_rotation(K, R):
    H = K @ R @ np.linalg.inv(K)
    return H / H[2,2]

def warp_image(img, H, dsize):
    return cv2.warpPerspective(img, H, dsize, flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT)

def render_simulation(img, K, pitch, yaw, roll, W, H):
    R = build_rotation(pitch, yaw, roll)
    H_forward = homography_from_rotation(K, R)

    # 让画布更大一些，便于观察
    T = np.array([[1.0, 0.0, W/2.0],
                  [0.0, 1.0, H/2.0],
                  [0.0, 0.0, 1.0]], dtype=np.float64)
    H_shift = T @ H_forward
    warped = warp_image(img, H_shift, (2*W, 2*H))
    return warped

# -------- compute wall-plane (Z=0) intersections for 4 image corners --------
def corners_world_xy(K, pitch, yaw, roll, W, H, D_m):
    """
    Projector center C = (0,0,-D), wall plane Z=0.
    For pixel p = [u,v,1]^T, ray dir_world = R @ K^{-1} p (not normalized).
    Intersection: t = D / dir_z; (X,Y) = t * (dir_x, dir_y).
    """
    R = build_rotation(pitch, yaw, roll)
    Kinv = np.linalg.inv(K)

    # four corners in pixel coords (u,v)
    corners = np.array([[0, 0, 1],
                        [W-1, 0, 1],
                        [W-1, H-1, 1],
                        [0, H-1, 1]], dtype=np.float64).T  # 3x4

    dirs = R @ (Kinv @ corners)  # 3x4
    xs, ys, zs = dirs[0, :], dirs[1, :], dirs[2, :]

    XY = []
    for i in range(4):
        dz = zs[i]
        if dz <= 1e-9:
            # 不与Z=0相交（或平行/背对），做保护
            XY.append((float('nan'), float('nan')))
        else:
            t = D_m / dz
            XY.append((t * xs[i], t * ys[i]))
    return XY  # list of (X,Y) in meters

def main():
    # ------------- parameters -------------
    target_path = "20251027-113839.jpg"
    W, H = 1920, 1080
    throw_ratio = 0.8
    cx, cy = W/2.0, H         # 主点在底边中心
    D_m = 2.0                 # 投影距离（米），光心到墙面的距离

    if not os.path.exists(target_path):
        raise FileNotFoundError(target_path)
    target = cv2.imread(target_path)
    target = cv2.resize(target, (W, H))

    K = intrinsics_from_throw_ratio(W, H, throw_ratio, cx, cy)

    pitch, yaw, roll = 0.0, 0.0, 0.0
    step = 0.5

    cv2.namedWindow("Warp Simulation", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Warp Simulation", 1280, 720)

    while True:
        warped = render_simulation(target, K, pitch, yaw, roll, W, H)
        # 计算四角世界坐标（米）
        xy_list = corners_world_xy(K, pitch, yaw, roll, W, H, D_m)

        # 文本叠加
        y0 = 40
        dy = 30
        h1 = xy_list[2][1] - xy_list[1][1]
        h0 = xy_list[3][1] - xy_list[0][1]
        h_diff = (h0 - h1) * 100

        w1 = xy_list[1][0] - xy_list[0][0]
        w0 = xy_list[2][0] - xy_list[3][0]
        w_diff = (w0 - w1) * 100

        info_lines = [
            f"Pitch: {pitch:.1f} deg   Yaw: {yaw:.1f} deg   Roll: {roll:.1f} deg   D: {D_m:.3f} m",
            "Corners @ Z=0 plane (meters):",
            f"TL (0,0):      X={xy_list[0][0]:.4f}, Y={xy_list[0][1]:.4f}",
            f"TR (W-1,0):    X={xy_list[1][0]:.4f}, Y={xy_list[1][1]:.4f}",
            f"BR (W-1,H-1):  X={xy_list[2][0]:.4f}, Y={xy_list[2][1]:.4f}",
            f"BL (0,H-1):    X={xy_list[3][0]:.4f}, Y={xy_list[3][1]:.4f}",
            f"H1 - H2:  {h_diff:.4f} cm  ",
            f"W1 - W2:  {w_diff:.4f} cm  ",
            "(W/S: pitch, A/D: yaw, Z/X: roll, ESC: quit)"
        ]

        # 绘制文本（绿色）
        for i, line in enumerate(info_lines):
            y = y0 + i * dy
            cv2.putText(warped, line, (30, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0,255,0), 2, cv2.LINE_AA)

        cv2.imshow("Warp Simulation", warped)

        key = cv2.waitKey(30) & 0xFF
        if key == 27:  # ESC
            break
        elif key == ord('w'):
            pitch += step
        elif key == ord('s'):
            pitch -= step
        elif key == ord('a'):
            yaw -= step
        elif key == ord('d'):
            yaw += step
        elif key == ord('z'):
            roll -= step
        elif key == ord('x'):
            roll += step
        # 你也可以顺手加按键调 D，比如 'q'/'e' 改变 D：
        elif key == ord('q'):
            D_m = max(0.1, D_m - 0.05)   # 最小 0.1 m，避免为零
        elif key == ord('e'):
            D_m += 0.05

    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()